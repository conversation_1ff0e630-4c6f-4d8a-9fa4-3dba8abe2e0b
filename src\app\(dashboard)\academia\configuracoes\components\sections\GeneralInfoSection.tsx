'use client';

import { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Building2, Mail, Link, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { 
  getGeneralInfo, 
  updateGeneralInfo, 
  checkSlugAvailability,
  type GeneralInfoFormData 
} from '../../actions/general-info-actions';
import { useRouter } from 'next/navigation';
import { PhoneInput } from '@/components/shared/PhoneInput';
import { parsePhoneNumber } from '@/utils/phone-utils';

// Schema de validação (reutilizando do actions)
const generalInfoSchema = z.object({
  academyName: z.string().min(2, 'Nome da academia deve ter pelo menos 2 caracteres'),
  slug: z.string()
    .min(3, 'Slug deve ter pelo menos 3 caracteres')
    .regex(/^[a-z0-9-]+$/, 'Slug deve conter apenas letras minúsculas, números e hífens'),
  email: z.string().email('Email inválido').or(z.literal('')),
  phone: z.string().refine(val => {
    if (!val) return true; // Permite string vazia
    const { nationalNumber } = parsePhoneNumber(val);
    return nationalNumber.length >= 8;
  }, {
    message: 'Telefone deve ter pelo menos 8 caracteres'
  }),
  // Endereço removido – será configurado em outra tela
});

export default function GeneralInfoSection() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [slugCheckTimeout, setSlugCheckTimeout] = useState<NodeJS.Timeout | null>(null);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [originalSlug, setOriginalSlug] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    watch,
    setError,
    clearErrors,
    reset,
    control
  } = useForm<GeneralInfoFormData>({
    resolver: zodResolver(generalInfoSchema),
    defaultValues: {
      academyName: '',
      slug: '',
      email: '',
      phone: '',
      // address removido
    }
  });

  const router = useRouter();

  const watchedSlug = watch('slug');

  // Carregar dados iniciais
  useEffect(() => {
    const loadGeneralInfo = async () => {
      try {
        setIsLoading(true);
        const result = await getGeneralInfo();
        
        if (result.success && result.data) {
          const data = result.data;
          setValue('academyName', data.academyName);
          setValue('slug', data.slug);
          setValue('email', data.email);
          setValue('phone', data.phone);
          setOriginalSlug(data.slug);
          reset(data); // Reset para marcar como não modificado
        } else {
          toast.error(result.errors?._form || 'Erro ao carregar informações');
        }
      } catch (error) {
        toast.error('Erro ao carregar informações da academia');
      } finally {
        setIsLoading(false);
      }
    };

    loadGeneralInfo();
  }, [setValue, reset]);

  // Verificar disponibilidade do slug com debounce
  useEffect(() => {
    if (!watchedSlug || watchedSlug === originalSlug) {
      setSlugAvailable(null);
      clearErrors('slug');
      return;
    }

    // Limpar timeout anterior
    if (slugCheckTimeout) {
      clearTimeout(slugCheckTimeout);
    }

    // Validar formato do slug primeiro
    const slugRegex = /^[a-z0-9-]+$/;
    if (!slugRegex.test(watchedSlug)) {
      setSlugAvailable(false);
      return;
    }

    // Verificar disponibilidade após 500ms
    const timeout = setTimeout(async () => {
      try {
        const result = await checkSlugAvailability(watchedSlug, originalSlug);
        setSlugAvailable(result.available);
        
        if (!result.available) {
          setError('slug', { 
            type: 'manual', 
            message: 'Este slug já está em uso' 
          });
        } else {
          clearErrors('slug');
        }
      } catch (error) {
        console.error('Erro ao verificar slug:', error);
      }
    }, 500);

    setSlugCheckTimeout(timeout);

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [watchedSlug, originalSlug, setError, clearErrors]);

  // Submeter formulário
  const onSubmit = async (data: GeneralInfoFormData) => {
    try {
      setIsSubmitting(true);
      const slugChanged = data.slug !== originalSlug;
      
      const result = await updateGeneralInfo(data);
      
      if (result.success) {
        toast.success(result.message || 'Informações atualizadas com sucesso');
        
        if (slugChanged) {
          toast.info('Slug alterado. Redirecionando para o novo endereço...');
          setTimeout(() => {
            const { protocol } = window.location;
            // TODO: Usar uma variável de ambiente para o domínio raiz em vez de hardcoding
            const rootDomain = 'sondtheanime.site'; 
            const newUrl = `${protocol}//${data.slug}.${rootDomain}/academia/configuracoes`;
            window.location.href = newUrl;
          }, 2000);
        } else if (result.data) {
          setOriginalSlug(result.data.slug);
          reset(result.data); // Reset para marcar como não modificado
        }
      } else {
        if (result.errors) {
          // Definir erros específicos nos campos
          Object.entries(result.errors).forEach(([field, message]) => {
            if (field === '_form') {
              toast.error(message as string);
            } else {
              setError(field as keyof GeneralInfoFormData, {
                type: 'manual',
                message: Array.isArray(message) ? message[0] : message as string
              });
            }
          });
        }
      }
    } catch (error) {
      toast.error('Erro ao atualizar informações');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <section className="space-y-6 rounded-2xl border border-border bg-card p-6 shadow-sm">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      </section>
    );
  }

  return (
    <section className="space-y-6 rounded-2xl border border-border bg-card p-6 shadow-sm">
      <div className="flex items-start gap-4">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg border border-border bg-background">
          <Building2 className="h-5 w-5 text-muted-foreground" />
        </div>
        <div>
          <h2 className="text-lg font-semibold">Informações da Academia</h2>
          <p className="text-sm text-muted-foreground">
            Configure as informações básicas da sua academia.
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="academyName">Nome da Academia</Label>
            <Input 
              id="academyName"
              {...register('academyName')}
              placeholder="Digite o nome da academia"
              className={errors.academyName ? 'border-red-500' : ''}
            />
            {errors.academyName && (
              <p className="text-sm text-red-500">{errors.academyName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug da Academia</Label>
            <div className="relative">
              <Link className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="slug"
                {...register('slug')}
                placeholder="slug-da-academia"
                className={`pl-10 ${
                  errors.slug 
                    ? 'border-red-500' 
                    : slugAvailable === true 
                    ? 'border-green-500' 
                    : slugAvailable === false 
                    ? 'border-red-500' 
                    : ''
                }`}
              />
              {watchedSlug && watchedSlug !== originalSlug && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  {slugAvailable === null ? (
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  ) : slugAvailable ? (
                    <div className="h-4 w-4 rounded-full bg-green-500" />
                  ) : (
                    <div className="h-4 w-4 rounded-full bg-red-500" />
                  )}
                </div>
              )}
            </div>
            {errors.slug && (
              <p className="text-sm text-red-500">{errors.slug.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              O slug será usado na URL da sua academia (ex: seu-slug.apexdojo.com.br)
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="email">E-mail</Label>
              {/* <button
                type="button"
                onClick={() => router.push('/academia/configuracoes/dominio-email')}
                className="text-primary text-xs font-medium uppercase hover:underline"
              >
                Definir endereço de envio
              </button> */}
            </div>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
                className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
              />
            </div>
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Caso não informado, será usado o e-mail do dono da academia
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="phone">Telefone</Label>
            </div>
            <Controller
              name="phone"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <PhoneInput
                  field={field}
                  error={error}
                  placeholder="(00) 00000-0000"
                />
              )}
            />
            {errors.phone && (
              <p className="text-sm text-red-500">{errors.phone.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Este é o telefone do dono da academia
            </p>
          </div>
        </div>

        <div className="flex justify-end">
          <Button 
            type="submit" 
            disabled={isSubmitting || !isDirty || (watchedSlug !== originalSlug && slugAvailable !== true)}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : (
              'Salvar Alterações'
            )}
          </Button>
        </div>
      </form>
    </section>
  );
} 