'use client';

import { Suspense } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';
import { useEffect } from 'react';
import ModalityEditForm from './ModalityEditForm';
import Loading from '../loading';
import { type ModalitySettings } from '@/services/modalities/settings';
import { Card } from '@/components/ui/card';
import { useModalityData, type GraduationLevelWithRequirements } from '../hooks/use-modality-data';

interface ModalityEditViewProps {
  modality: {
    id: string;
    slug: string;
    name: string;
    enabled: boolean;
  };
  modalitySettings: ModalitySettings | null;
  levelsWithRequirements: GraduationLevelWithRequirements[];
}

export default function ModalityEditView({
  modality,
  modalitySettings: initialModalitySettings,
  levelsWithRequirements: initialLevelsWithRequirements,
}: ModalityEditViewProps) {
  const { setPageTitle, setPageSubtitle } = usePageTitle();

  const {
    modalitySettings,
    levelsWithRequirements,
    isRefetching,
    refetchData,
  } = useModalityData({
    modalityId: modality.id,
    modalitySlug: modality.slug,
    initialModalitySettings,
    initialLevelsWithRequirements,
  });

  useEffect(() => {
    setPageTitle('Editar Modalidade');
    setPageSubtitle(`Configure os detalhes e requisitos da modalidade ${modality.name}`);
  }, [setPageTitle, setPageSubtitle, modality.name]);

  return (
    <Suspense fallback={<Loading />}>
      <div className="py-10">
        <Card className="p-6">
          <ModalityEditForm
            modality={modality}
            modalitySettings={modalitySettings}
            levelsWithRequirements={levelsWithRequirements}
            onDataUpdate={refetchData}
            isRefetching={isRefetching}
          />
        </Card>
      </div>
    </Suspense>
  );
}