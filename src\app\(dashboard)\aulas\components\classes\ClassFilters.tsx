'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Search, Filter, X, RotateCcw } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { ClassFilter } from '../../actions/schemas';

interface ClassFiltersProps {
  instructors: Array<{ id: string; name: string; }>;
  branches: Array<{ id: string; name: string; }>;
  classGroups: Array<{ id: string; name: string; }>;
  onFiltersChange?: (filters: Partial<ClassFilter>) => void;
  showGroupFilter?: boolean;
}

const STATUS_OPTIONS = [
  { value: 'scheduled', label: 'Agendada' },
  { value: 'ongoing', label: 'Em Andamento' },
  { value: 'completed', label: 'Concluída' },
  { value: 'cancelled', label: 'Cancelada' },
  { value: 'rescheduled', label: 'Reagendada' },
];

export function ClassFilters({
  instructors,
  branches,
  classGroups,
  onFiltersChange,
  showGroupFilter = true
}: ClassFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<Partial<ClassFilter>>({
    search: searchParams.get('search') || '',
    instructor_id: searchParams.get('instructor_id') || '',
    branch_id: searchParams.get('branch_id') || '',
    class_group_id: searchParams.get('class_group_id') || '',
    status: (searchParams.get('status') as any) || undefined,
    date_from: searchParams.get('date_from') || '',
    date_to: searchParams.get('date_to') || '',
    attendance_recorded: searchParams.get('attendance_recorded') ? 
      searchParams.get('attendance_recorded') === 'true' : undefined,
  });

  const [dateFrom, setDateFrom] = useState<Date | undefined>(
    filters.date_from ? new Date(filters.date_from) : undefined
  );
  const [dateTo, setDateTo] = useState<Date | undefined>(
    filters.date_to ? new Date(filters.date_to) : undefined
  );

  // Atualizar URL quando filtros mudarem
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    
    // Limpar parâmetros vazios e adicionar novos
    Object.keys(filters).forEach(key => {
      params.delete(key);
    });

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== '' && value !== undefined && value !== null) {
        params.set(key, String(value));
      }
    });

    const queryString = params.toString();
    const newUrl = queryString ? `${pathname}?${queryString}` : pathname;
    
    router.replace(newUrl, { scroll: false });
    
    // Notificar componente pai
    if (onFiltersChange) {
      onFiltersChange(filters);
    }
  }, [filters, pathname, router, searchParams, onFiltersChange]);

  const handleFilterChange = (key: keyof ClassFilter, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDateFromChange = (date: Date | undefined) => {
    setDateFrom(date);
    handleFilterChange('date_from', date ? format(date, 'yyyy-MM-dd') : '');
  };

  const handleDateToChange = (date: Date | undefined) => {
    setDateTo(date);
    handleFilterChange('date_to', date ? format(date, 'yyyy-MM-dd') : '');
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      instructor_id: '',
      branch_id: '',
      class_group_id: '',
      status: undefined,
      date_from: '',
      date_to: '',
      attendance_recorded: undefined,
    });
    setDateFrom(undefined);
    setDateTo(undefined);
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== '' && value !== undefined && value !== null
  );

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== '' && value !== undefined && value !== null
  ).length;

  return (
    <div className="space-y-4">
      {/* Barra de Busca e Controles */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Buscar aulas..."
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtros
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Limpar
            </Button>
          )}
        </div>
      </div>

      {/* Filtros Ativos */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filters.instructor_id && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              Instrutor: {instructors.find(i => i.id === filters.instructor_id)?.name}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleFilterChange('instructor_id', '')}
              />
            </Badge>
          )}
          {filters.branch_id && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              Filial: {branches.find(b => b.id === filters.branch_id)?.name}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleFilterChange('branch_id', '')}
              />
            </Badge>
          )}
          {filters.class_group_id && showGroupFilter && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              Turma: {classGroups.find(g => g.id === filters.class_group_id)?.name}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleFilterChange('class_group_id', '')}
              />
            </Badge>
          )}
          {filters.status && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              Status: {STATUS_OPTIONS.find(s => s.value === filters.status)?.label}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleFilterChange('status', '')}
              />
            </Badge>
          )}
          {filters.date_from && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              A partir de: {(() => {
                try {
                  const date = new Date(filters.date_from);
                  if (isNaN(date.getTime())) {
                    return 'Data inválida';
                  }
                  return format(date, 'dd/MM/yyyy');
                } catch (error) {
                  console.error('Erro ao formatar data inicial:', error);
                  return 'Data inválida';
                }
              })()}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleDateFromChange(undefined)}
              />
            </Badge>
          )}
          {filters.date_to && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              Até: {(() => {
                try {
                  const date = new Date(filters.date_to);
                  if (isNaN(date.getTime())) {
                    return 'Data inválida';
                  }
                  return format(date, 'dd/MM/yyyy');
                } catch (error) {
                  console.error('Erro ao formatar data final:', error);
                  return 'Data inválida';
                }
              })()}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleDateToChange(undefined)}
              />
            </Badge>
          )}
          {filters.attendance_recorded !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1 hover:bg-secondary/80">
              Presença: {filters.attendance_recorded ? 'Registrada' : 'Não registrada'}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-foreground" 
                onClick={() => handleFilterChange('attendance_recorded', undefined)}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Painel de Filtros Expandido */}
      {isOpen && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base text-foreground">Filtros Avançados</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* Instrutor */}
              <div className="space-y-2">
                <Label className="text-foreground">Instrutor</Label>
                <Select
                  value={filters.instructor_id || ''}
                  onValueChange={(value) => handleFilterChange('instructor_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os instrutores" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os instrutores</SelectItem>
                    {instructors.map(instructor => (
                      <SelectItem key={instructor.id} value={instructor.id}>
                        {instructor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Filial */}
              <div className="space-y-2">
                <Label className="text-foreground">Filial</Label>
                <Select
                  value={filters.branch_id || ''}
                  onValueChange={(value) => handleFilterChange('branch_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todas as filiais" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas as filiais</SelectItem>
                    {branches.map(branch => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Turma */}
              {showGroupFilter && (
                <div className="space-y-2">
                  <Label className="text-foreground">Turma</Label>
                  <Select
                    value={filters.class_group_id || ''}
                    onValueChange={(value) => handleFilterChange('class_group_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Todas as turmas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Todas as turmas</SelectItem>
                      {classGroups.map(group => (
                        <SelectItem key={group.id} value={group.id}>
                          {group.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Status */}
              <div className="space-y-2">
                <Label className="text-foreground">Status</Label>
                <Select
                  value={filters.status || ''}
                  onValueChange={(value) => handleFilterChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os status</SelectItem>
                    {STATUS_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Presença Registrada */}
              <div className="space-y-2">
                <Label className="text-foreground">Presença</Label>
                <Select
                  value={filters.attendance_recorded?.toString() || ''}
                  onValueChange={(value) => handleFilterChange('attendance_recorded', 
                    value === '' ? undefined : value === 'true')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos</SelectItem>
                    <SelectItem value="true">Presença registrada</SelectItem>
                    <SelectItem value="false">Presença não registrada</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Filtros de Data */}
            <div className="space-y-4">
              <Label className="text-foreground">Período</Label>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">Data inicial</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !dateFrom && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateFrom ? format(dateFrom, "dd/MM/yyyy", { locale: ptBR }) : "Selecionar data"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateFrom}
                        onSelect={handleDateFromChange}
                        initialFocus
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">Data final</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !dateTo && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateTo ? format(dateTo, "dd/MM/yyyy", { locale: ptBR }) : "Selecionar data"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateTo}
                        onSelect={handleDateToChange}
                        initialFocus
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 