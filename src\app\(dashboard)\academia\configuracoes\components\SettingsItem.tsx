+"use client";
import Link from 'next/link';
import { ChevronRightIcon } from '@heroicons/react/24/outline';

interface SettingsItemBase {
  title: string;
  description: string;
}

type SettingsItemProps =
  | (SettingsItemBase & { href: string; onClick?: never })
  | (SettingsItemBase & { href?: never; onClick: () => void });

export function SettingsItem({ title, description, href, onClick }: SettingsItemProps) {
  const Content = (
    <div className="flex items-center justify-between gap-4 px-6 py-5 w-full hover:bg-muted/50 focus:outline-none focus:bg-muted/60">
      <div className="flex flex-col text-start">
        <span className="text-sm font-medium text-foreground">{title}</span>
        <span className="text-xs text-muted-foreground mt-1 line-clamp-1">{description}</span>
      </div>
      <ChevronRightIcon
        className="h-4 w-4 text-foreground/60 group-hover:text-foreground transition-colors"
        aria-hidden="true"
      />
    </div>
  );

  return (
    <li className="group">
      {href ? (
        <Link href={href}>{Content}</Link>
      ) : (
        <button type="button" onClick={onClick} className="w-full text-left">
          {Content}
        </button>
      )}
    </li>
  );
} 