import { PageHeader } from '@/components/layout/page-header';
import { getMinorsWithoutGuardian, getMinorsWithGuardian } from '@/services/user/guardian-service';
import { GuardianManagementList } from './components/GuardianManagementList';
import { PageTitleWrapper } from './components/PageTitleWrapper';

export default async function ResponsaveisPage() {
  // Buscar menores em paralelo
  const [minorsWithoutGuardian, minorsWithGuardian] = await Promise.all([
    getMinorsWithoutGuardian(),
    getMinorsWithGuardian()
  ]);

  return (
    <>
      <PageTitleWrapper />
      <div className="min-h-screen pb-12">
        <PageHeader
          backHref="/academia/configuracoes"
          backLabel="Voltar para configurações"
        />

        <main className="px-4 sm:px-6 lg:px-8 py-6">
          <div className="space-y-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h2 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Administração de Contas de Menores
              </h2>
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                Esta página permite configurar e gerenciar responsáveis legais para estudantes menores de 18 anos. 
                Responsáveis podem administrar as contas até que os estudantes completem a maioridade.
              </p>
            </div>

            <GuardianManagementList 
              initialMinorsWithoutGuardian={minorsWithoutGuardian}
              initialMinorsWithGuardian={minorsWithGuardian}
            />
          </div>
        </main>
      </div>
    </>
  );
} 