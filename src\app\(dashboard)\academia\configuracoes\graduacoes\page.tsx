import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import GraduationLevelsClient from './GraduationLevelsClient';
import { listGraduationLevelsAction } from '@/app/(dashboard)/academia/actions/list-graduation-levels';
import { redirect } from 'next/navigation';

export default async function GraduacoesPage() {
  const result = await listGraduationLevelsAction();

  if (!result.success) {
    redirect(`/home?erro=${encodeURIComponent(result.errors?._form || 'falha')}`);
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Níveis de Graduação - Jiu-Jitsu</CardTitle>
      </CardHeader>
      <CardContent>
        <GraduationLevelsClient initialLevels={result.levels || []} />
      </CardContent>
    </Card>
  );
} 