'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  UserCheck, 
  QrCode, 
  X, 
  Calendar, 
  Settings 
} from 'lucide-react';
import { ClassWithDetails } from '../../types';
import { format, isAfter, isBefore, addHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';

interface ClassesTableProps {
  classes: ClassWithDetails[];
  showGroupInfo?: boolean;
  onCancel?: (classId: string) => void;
  onReschedule?: (classId: string) => void;
}

export function ClassesTable({
  classes,
  showGroupInfo = true,
  onCancel,
  onReschedule
}: ClassesTableProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'ongoing': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'completed': return 'bg-muted text-muted-foreground';
      case 'cancelled': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'rescheduled': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Agendada';
      case 'ongoing': return 'Em Andamento';
      case 'completed': return 'Concluída';
      case 'cancelled': return 'Cancelada';
      case 'rescheduled': return 'Reagendada';
      default: return 'Desconhecido';
    }
  };

  const getClassTypeColor = (type: string) => {
    switch (type) {
      case 'regular': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'free': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'workshop': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200';
      case 'exam': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getClassTypeLabel = (type: string) => {
    switch (type) {
      case 'regular': return 'Regular';
      case 'free': return 'Aula Livre';
      case 'workshop': return 'Workshop';
      case 'exam': return 'Exame';
      default: return 'Outro';
    }
  };

  return (
    <div className="rounded-md border border-border">
      <Table>
        <TableHeader>
          <TableRow className="border-border">
            <TableHead className="text-foreground">Aula</TableHead>
            <TableHead className="text-foreground">Data/Hora</TableHead>
            <TableHead className="text-foreground">Instrutor</TableHead>
            {showGroupInfo && <TableHead className="text-foreground">Turma</TableHead>}
            <TableHead className="text-foreground">Status</TableHead>
            <TableHead className="text-foreground">Presença</TableHead>
            <TableHead className="text-right text-foreground">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {classes.map((classItem) => {
            const startTime = new Date(classItem.start_time);
            const endTime = new Date(classItem.end_time);
            const now = new Date();
            
            const isUpcoming = isAfter(startTime, now);
            const isOngoing = isBefore(startTime, now) && isAfter(endTime, now);
            
            const canCheckIn = isOngoing || (isUpcoming && isBefore(now, addHours(startTime, -0.5)));
            const canEdit = isUpcoming && classItem.status === 'scheduled';
            const canCancel = isUpcoming && classItem.status === 'scheduled';

            const attendanceCount = classItem._count.attendance || 0;
            const maxCapacity = classItem.max_capacity || 0;
            const attendanceRate = classItem.attendance_rate || 0;

            return (
              <TableRow key={classItem.id} className={`border-border ${isOngoing ? 'bg-green-50 dark:bg-green-900/20' : ''}`}>
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium line-clamp-1 text-foreground">{classItem.name}</div>
                    {classItem.class_type && classItem.class_type !== 'regular' && (
                      <Badge 
                        variant="secondary" 
                        className={`${getClassTypeColor(classItem.class_type)} text-xs`}
                      >
                        {getClassTypeLabel(classItem.class_type)}
                      </Badge>
                    )}
                  </div>
                </TableCell>

                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium text-foreground">
                      {format(startTime, 'dd/MM/yyyy', { locale: ptBR })}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {format(startTime, 'HH:mm')} - {format(endTime, 'HH:mm')}
                    </div>
                    {isOngoing && (
                      <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-600 dark:border-green-400 text-xs">
                        AO VIVO
                      </Badge>
                    )}
                  </div>
                </TableCell>

                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium text-foreground">
                      {classItem.instructor.full_name || 
                       `${classItem.instructor.first_name} ${classItem.instructor.last_name || ''}`.trim()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {classItem.branch.name}
                    </div>
                  </div>
                </TableCell>

                {showGroupInfo && (
                  <TableCell>
                    {classItem.class_group ? (
                      <Link 
                        href={`/turmas/${classItem.class_group.id}`}
                        className="text-primary hover:underline font-medium"
                      >
                        {classItem.class_group.name}
                      </Link>
                    ) : (
                      <span className="text-muted-foreground text-sm">Aula avulsa</span>
                    )}
                  </TableCell>
                )}

                <TableCell>
                  <Badge variant="secondary" className={getStatusColor(classItem.status)}>
                    {getStatusLabel(classItem.status)}
                  </Badge>
                </TableCell>

                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium text-foreground">
                      {attendanceCount}
                      {/* Só mostra capacidade para aulas com turma e capacidade definida */}
                      {classItem.class_group_id && maxCapacity > 0 ? ` / ${maxCapacity}` : ''} alunos
                    </div>
                    {attendanceRate > 0 && (
                      <div className="text-sm text-muted-foreground">
                        {Math.round(attendanceRate)}% presença
                      </div>
                    )}
                    {canCheckIn && (
                      <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-600 dark:border-green-400 text-xs">
                        Check-in aberto
                      </Badge>
                    )}
                  </div>
                </TableCell>

                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`presenca/${classItem.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          Ver Detalhes
                        </Link>
                      </DropdownMenuItem>
                      
                      {canCheckIn && (
                        <DropdownMenuItem asChild>
                          <Link href={`presenca/${classItem.id}`}>
                            <UserCheck className="mr-2 h-4 w-4" />
                            Registrar Presença
                          </Link>
                        </DropdownMenuItem>
                      )}

                      {canCheckIn && (
                        <DropdownMenuItem asChild>
                          <Link href={`aulas/checkin/qr/${classItem.id}`}>
                            <QrCode className="mr-2 h-4 w-4" />
                            QR Code Check-in
                          </Link>
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      {canEdit && (
                        <DropdownMenuItem asChild>
                          <Link href={`/aulas/editar/${classItem.id}`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Editar Aula
                          </Link>
                        </DropdownMenuItem>
                      )}

                      {canCancel && onCancel && (
                        <DropdownMenuItem 
                          onClick={() => onCancel(classItem.id)}
                          className="text-red-600 dark:text-red-400"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancelar Aula
                        </DropdownMenuItem>
                      )}

                      {canEdit && onReschedule && (
                        <DropdownMenuItem onClick={() => onReschedule(classItem.id)}>
                          <Calendar className="mr-2 h-4 w-4" />
                          Reagendar
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem asChild>
                        <Link href={`/aulas/configurar/${classItem.id}`}>
                          <Settings className="mr-2 h-4 w-4" />
                          Configurações
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
} 