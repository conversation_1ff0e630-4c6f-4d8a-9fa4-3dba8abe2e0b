import { listModalities } from '@/services/modalities';
import { requireAuth } from '@/services/auth/actions/auth-actions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { redirect } from 'next/navigation';
import ProgramsAndLevelsClient from './ProgramsAndLevelsClient';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default async function ModalidadesPage() {
  const { user } = await requireAuth();
  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    redirect('/home?erro=tenant-nao-encontrado');
  }
  if (role !== 'admin') {
    redirect('/home?erro=acesso-negado');
  }

  const modalities = await listModalities(tenantId!);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Configurar Modalidades, Níveis e Graduações</CardTitle>
          <Link href="/academia/configuracoes">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <ProgramsAndLevelsClient initialModalities={modalities} />
      </CardContent>
    </Card>
  );
} 