'use client';

import { PermissionButton } from "@/services/permissions";
import { useRouter } from "next/navigation";

export function StudentActionsButtons() {
  const router = useRouter();
  
  const handleImportClick = () => {
    router.push('/alunos/importar?from=/alunos');
  };
  
  const handleExportClick = () => {
    // Implementação futura da exportação
    console.log("Exportar alunos");
  };
  
  return (
    <div className="flex flex-wrap items-center gap-2">
      <PermissionButton 
        resource="students"
        action="import"
        variant="outline"
        size="sm"
        onClick={handleImportClick}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
        Importar
      </PermissionButton>
      
      <PermissionButton
        resource="students"
        action="export"
        variant="outline"
        size="sm"
        onClick={handleExportClick}
        disabled
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>
        Exportar
      </PermissionButton>
    </div>
  );
} 