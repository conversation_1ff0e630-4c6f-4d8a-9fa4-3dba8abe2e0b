'use server';

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';
import { requireAuth } from '@/services/auth/server';
import { getTenantSlug } from '@/services/tenant/';
import { revalidatePath } from 'next/cache';
import { parsePhoneNumber } from '@/utils/phone-utils';

/**
 * Schema de validação para informações gerais da academia
 * Inclui validação de nome, slug, email, telefone e endereço
 */
const generalInfoSchema = z.object({
  academyName: z.string().min(2, 'Nome da academia deve ter pelo menos 2 caracteres'),
  slug: z.string()
    .min(3, 'Slug deve ter pelo menos 3 caracteres')
    .regex(/^[a-z0-9-]+$/, 'Slug deve conter apenas letras minúsculas, números e hífens'),
  email: z.string().email('Email inválido').or(z.literal('')),
  phone: z.string().refine(val => {
    if (!val) return true; // Permite string vazia
    const { nationalNumber } = parsePhoneNumber(val);
    return nationalNumber.length >= 8;
  }, {
    message: 'Telefone deve ter pelo menos 8 caracteres'
  }).or(z.literal('')),
});

export type GeneralInfoFormData = z.infer<typeof generalInfoSchema>;

/**
 * Busca as informações gerais da academia
 * Combina dados do tenant (nome, slug) com dados da filial principal (email, endereço) e telefone do dono
 */
export async function getGeneralInfo() {
  try {
    // Verificar autenticação
    const { user } = await requireAuth();
    
    // Obter slug do tenant atual
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      };
    }

    const supabase = await createClient();

    // Buscar dados do tenant
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, name, slug, description, owner_id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      console.error('Erro ao buscar tenant:', tenantError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar dados da academia' }
      };
    }

    if (!tenantData) {
      return {
        success: false,
        errors: { _form: 'Academia não encontrada' }
      };
    }

    // Buscar dados da filial principal (email, endereço e telefone)
    const { data: branchData, error: branchError } = await supabase
      .from('branches')
      .select('email, phone')
      .eq('tenant_id', tenantData.id)
      .eq('is_main', true)
      .single();

    if (branchError) {
      console.error('Erro ao buscar filial principal:', branchError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar dados da filial principal' }
      };
    }

    if (!branchData) {
      return {
        success: false,
        errors: { _form: 'Filial principal não encontrada' }
      };
    }

    let finalPhone = branchData.phone;
    let ownerEmail: string | undefined;

    // Se a filial principal não tiver telefone, buscar o do dono como fallback
    if (!finalPhone) {
      const { data: ownerData, error: ownerError } = await supabase
        .from('users')
        .select('phone, email')
        .eq('id', tenantData.owner_id)
        .single();

      if (ownerError) {
        console.error('Erro ao buscar dados do dono:', ownerError);
        // Não retornar erro, apenas continuar sem os dados do dono
      }
      
      finalPhone = ownerData?.phone || '';
      ownerEmail = ownerData?.email;
    }

    return {
      success: true,
      data: {
        academyName: tenantData.name,
        slug: tenantData.slug,
        email: branchData.email || ownerEmail || '',
        phone: finalPhone,
      }
    };

  } catch (error) {
    console.error('Erro ao buscar informações gerais:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}

/**
 * Atualiza as informações gerais da academia
 * Atualiza o tenant (nome, slug), a filial principal (email, endereço) e o telefone do dono
 */
export async function updateGeneralInfo(data: unknown) {
  try {
    // Verificar autenticação
    const { user } = await requireAuth();

    // Validar dados de entrada
    const result = generalInfoSchema.safeParse(data);
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format()
      };
    }

    const validatedData = result.data;

    // Obter slug do tenant atual
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      };
    }

    const supabase = await createClient();

    // Buscar dados do tenant atual com owner_id
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, slug, owner_id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      console.error('Erro ao buscar tenant atual:', tenantError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar dados da academia' }
      };
    }

    if (!tenantData) {
      return {
        success: false,
        errors: { _form: 'Academia não encontrada' }
      };
    }

    // Se o email não for informado, usa o do dono da academia
    if (!validatedData.email) {
      const { data: ownerData, error: ownerError } = await supabase
        .from('users')
        .select('email')
        .eq('id', tenantData.owner_id)
        .single();

      if (ownerError || !ownerData?.email) {
        console.error('Erro ao buscar e-mail do dono:', ownerError);
        return {
          success: false,
          errors: { _form: 'Não foi possível encontrar o e-mail do dono da academia.' }
        };
      }
      validatedData.email = ownerData.email;
    }

    // Verificar se o slug está disponível (se foi alterado)
    if (validatedData.slug !== tenantData.slug) {
      const slugAvailable = await checkSlugAvailability(validatedData.slug, tenantData.slug);
      if (!slugAvailable.available) {
        return {
          success: false,
          errors: { slug: 'Este slug já está em uso' }
        };
      }
    }

    // Atualiza o tenant (nome, slug)
    const { error: updateTenantError } = await supabase
      .from('tenants')
      .update({
        name: validatedData.academyName,
        slug: validatedData.slug,
      })
      .eq('id', tenantData.id);

    if (updateTenantError) {
      console.error('Erro ao atualizar tenant:', updateTenantError);
      return {
        success: false,
        errors: { _form: 'Erro ao salvar informações da academia' }
      };
    }
    
    // Atualiza a filial principal (email e endereço)
    const { error: updateBranchError } = await supabase
      .from('branches')
      .update({
        email: validatedData.email,
        phone: validatedData.phone, // Salva o telefone na filial principal
      })
      .eq('tenant_id', tenantData.id)
      .eq('is_main', true);

    if (updateBranchError) {
      console.error('Erro ao atualizar filial principal:', updateBranchError);
      return {
        success: false,
        errors: { _form: 'Erro ao salvar informações da filial' }
      };
    }

    // Atualiza o telefone do dono da academia na tabela users apenas se o telefone da filial for nulo
    if (!validatedData.phone) {
      const { error: updateUserError } = await supabase
        .from('users')
        .update({ phone: validatedData.phone })
        .eq('id', tenantData.owner_id);

      if (updateUserError) {
        console.error('Erro ao atualizar telefone do dono:', updateUserError);
        return {
          success: false,
          errors: { _form: 'Erro ao salvar telefone do proprietário' }
        };
      }
    }

    revalidatePath('/(dashboard)/academia/configuracoes', 'layout');
    
    // Retorna os dados atualizados para resetar o formulário
    const updatedData = {
      academyName: validatedData.academyName,
      slug: validatedData.slug,
      email: validatedData.email,
      phone: validatedData.phone,
    };

    return {
      success: true,
      data: updatedData,
      message: 'Informações gerais atualizadas com sucesso'
    };

  } catch (error) {
    console.error('Erro ao atualizar informações gerais:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}

/**
 * Verifica se um slug está disponível para uso
 * Exclui o slug atual da verificação se fornecido
 */
export async function checkSlugAvailability(slug: string, currentSlug?: string) {
  try {
    const supabase = await createClient();

    // Verificar se o slug já existe (excluindo o slug atual se fornecido)
    let query = supabase
      .from('tenants')
      .select('slug')
      .eq('slug', slug)
      .is('deleted_at', null);

    if (currentSlug) {
      query = query.neq('slug', currentSlug);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao verificar disponibilidade do slug:', error);
      return { available: false, error: 'Erro ao verificar disponibilidade' };
    }

    return { available: data.length === 0 };

  } catch (error) {
    console.error('Erro ao verificar disponibilidade do slug:', error);
    return { available: false, error: 'Erro interno do servidor' };
  }
} 