import { But<PERSON> } from '@/components/ui/button';
import { Plus, Download, FileText } from 'lucide-react';
import Link from 'next/link';

export function ClassGroupActions() {
  return (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" className="gap-2" disabled>
        <Download className="h-4 w-4" />
        Exportar
      </Button>
      
      <Button asChild>
        <Link href="/turmas/nova" className="gap-2">
          <Plus className="h-4 w-4" />
          Nova Turma
        </Link>
      </Button>
    </div>
  );
} 