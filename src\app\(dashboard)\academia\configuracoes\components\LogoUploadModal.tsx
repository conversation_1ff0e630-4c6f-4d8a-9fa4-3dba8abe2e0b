'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'react-easy-crop';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog';
import { useLogoUpload } from '../hooks/use-logo-upload';
import { Loader2, UploadCloud, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';

interface LogoUploadModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onLogoUpdated?: (url: string | null) => void;
}

interface CroppedAreaPixels {
  x: number;
  y: number;
  width: number;
  height: number;
}

const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.src = url;
  });

const getCroppedImg = async (
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number }
): Promise<Blob> => {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Não foi possível obter contexto do canvas');
  }

  // Definir as dimensões do canvas para criar uma imagem quadrada (para logo circular)
  const maxSize = Math.max(pixelCrop.width, pixelCrop.height);
  canvas.width = maxSize;
  canvas.height = maxSize;

  // NÃO preencher o fundo - deixar transparente por padrão
  // ctx.clearRect garante que o canvas está limpo e transparente
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Desenhar a imagem recortada no centro do canvas
  const offsetX = (maxSize - pixelCrop.width) / 2;
  const offsetY = (maxSize - pixelCrop.height) / 2;
  
  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    offsetX,
    offsetY,
    pixelCrop.width,
    pixelCrop.height
  );

  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (!blob) throw new Error('Falha ao criar blob');
      resolve(blob);
    }, 'image/png', 1.0); // Usar PNG para preservar transparência
  });
};

export const LogoUploadModal = ({
  open,
  onOpenChange,
  onLogoUpdated
}: LogoUploadModalProps) => {
  const { 
    file, 
    previewUrl, 
    setFile, 
    setStatus, 
    setProgress, 
    setError,
    setLogoUrl,
    reset,
    status,
    error,
    progress,
    logoUrl,
    setTemporaryLogoUrl,
    setHasPendingChanges,
    hasPendingChanges,
    temporaryLogoUrl
  } = useLogoUpload();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  
  const onCropComplete = useCallback((croppedArea: any, croppedAreaPixels: CroppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const handleConfirm = async () => {
    if (!previewUrl || !croppedAreaPixels) return;

    try {
      setIsUploading(true);
      setStatus('uploading');
      setProgress(10);

      // Obter a imagem recortada como blob
      const croppedImageBlob = await getCroppedImg(previewUrl, croppedAreaPixels);
      
      // Converter blob para File
      const originalName = file?.name || `logo-${Date.now()}`;
      const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");
      const croppedFile = new File(
        [croppedImageBlob], 
        `${nameWithoutExt}.png`,
        { type: 'image/png' }
      );

      // Atualizar o arquivo no contexto
      setFile(croppedFile);

      // Simular progresso
      let currentProgress = 10;
      const progressInterval = setInterval(() => {
        currentProgress = Math.min(100, currentProgress + 20);
        setProgress(currentProgress);
        
        if (currentProgress >= 100) {
          clearInterval(progressInterval);
        }
      }, 100);

      // Criar URL temporária para preview
      const temporaryUrl = URL.createObjectURL(croppedImageBlob);
      setTemporaryLogoUrl(temporaryUrl);

      // Aguardar um pouco para mostrar o progresso
      await new Promise(resolve => setTimeout(resolve, 500));

      clearInterval(progressInterval);
      setProgress(100);
      setStatus('success');
      setHasPendingChanges(true);
      
      // Fechar modal sem salvar
      onOpenChange(false);
    } catch (error) {
      setStatus('error');
      setError('Ocorreu um erro ao processar a imagem');
      console.error('Erro ao processar a imagem:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSelectFile = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    if (selectedFile) {
      // Validar arquivo
      const validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (!validTypes.includes(selectedFile.type)) {
        setError('Arquivo deve ser SVG, PNG ou JPG');
        return;
      }
      
      if (selectedFile.size > maxSize) {
        setError('Arquivo deve ter no máximo 10MB');
        return;
      }

      setFile(selectedFile);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setError(null);
    }
  };

  const handleCancel = () => {
    // Limpar arquivo atual já que cancelamos a seleção
    setFile(null);

    // Limpar o input file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Fechar o modal
    onOpenChange(false);
  };

  // Resetar estados quando o modal abre
  useEffect(() => {
    if (open) {
      setError(null);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setCroppedAreaPixels(null);
      setIsUploading(false);
      // Limpar o input file
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [open]);

  const handleReset = () => {
    reset();
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen && !isUploading) {
        // Limpar estados ao fechar o modal com o "X" ou ESC
        setTimeout(() => {
          handleCancel();
        }, 0);
      } else {
        onOpenChange(isOpen);
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Logo da Academia</DialogTitle>
        </DialogHeader>
        
        {!previewUrl ? (
          <div className="flex flex-col items-center justify-center py-10">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/jpeg,image/png,image/webp,image/svg+xml"
              onChange={handleFileChange}
            />
            
            <div className="mb-6 p-4 rounded-full bg-primary/10 dark:bg-primary/20">
              <UploadCloud className="w-10 h-10 text-primary dark:text-primary-foreground" />
            </div>
            
            <h3 className="mb-2 text-xl font-medium text-slate-900 dark:text-slate-100">
              Selecione uma imagem
            </h3>
            
            <p className="mb-6 text-center text-sm text-slate-500 dark:text-slate-400">
              Formatos suportados: JPG, PNG, WebP e SVG (máx. 10MB)
            </p>
            
            <div className="flex flex-col gap-2 w-full max-w-xs">
              <Button onClick={handleSelectFile} className="w-full">
                Escolher arquivo
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div className="relative w-full h-64 overflow-hidden rounded-lg my-4">
              <Cropper
                image={previewUrl}
                crop={crop}
                zoom={zoom}
                aspect={1}
                cropShape="round"
                showGrid={false}
                onCropChange={setCrop}
                onCropComplete={onCropComplete}
                onZoomChange={setZoom}
              />
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-2">Zoom</p>
              <Slider
                value={[zoom]}
                min={1}
                max={3}
                step={0.1}
                onValueChange={(value: number[]) => setZoom(value[0])}
                className="w-full"
              />
            </div>
          </>
        )}
        
        {status === 'uploading' && (
          <div className="w-full mt-2 mb-4">
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-center mt-1 text-muted-foreground">
              Preparando logo... {progress}%
            </p>
          </div>
        )}
        
        {status === 'error' && error && (
          <Alert variant="destructive" className="mt-2 mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="ml-2">{error}</AlertDescription>
            <button 
              onClick={handleReset}
              className="ml-auto text-xs underline"
              type="button"
            >
              Tentar novamente
            </button>
          </Alert>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isUploading}>
            Cancelar
          </Button>
          
          {previewUrl && (
            <Button onClick={handleConfirm} disabled={isUploading}>
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Preparando...
                </>
              ) : (
                'Confirmar'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 