"use client"

import React, { useState, useEffect, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { usePageTitle } from '@/contexts/PageTitleContext';
import { Cog6ToothIcon } from '@heroicons/react/24/outline';
import { SettingsNavigation, NavItem } from './components/SettingsNavigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Lazy load general info section (placeholder for now)
const GeneralInfoSection = dynamic(() => import('./components/sections/GeneralInfoSection'));
const AppearanceSection = dynamic(() => import('./components/sections/AppearanceSection'));

const sectionComponents = {
  'general-info': GeneralInfoSection,
  'appearance': AppearanceSection,
};

type ActiveSection = keyof typeof sectionComponents;

export default function ConfiguracoesPage() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('general-info');
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle();

  const settingsItems: NavItem[] = useMemo(() => [
    {
      id: 'general-info',
      title: 'Informações Gerais',
      description: 'Nome da academia, e-mail e telefone.',
    },
    {
      id: 'appearance',
      title: 'Aparencia',
      description: 'Alterar aparência da academia',
    },
    {
      id: 'guardians',
      href: '/academia/configuracoes/responsaveis',
      title: 'Responsáveis de alunos',
      description: 'Gerenciar responsáveis de alunos.',
    },
    {
      id: 'modalidades',
      href: '/academia/configuracoes/modalidades',
      title: 'Modalidades',
      description: 'Ativar ou desativar modalidades oferecidas.',
    },
    {
      id: 'migracao',
      href: '/academia/configuracoes/migracao',
      title: 'Migrar de outro sistema',
      description: 'Importe alunos, planos e mais.',
    }
  ], []);

  useEffect(() => {
    const currentItem = settingsItems.find(item => item.id === activeSection);
    const icon = <Cog6ToothIcon className="h-6 w-6 text-primary" />;
    
    setPageTitle('Configurações da Academia');
    setPageSubtitle('Gerencie as informações e preferências da sua academia.');
    setPageIcon(icon);
    
  }, [activeSection, setPageTitle, setPageSubtitle, setPageIcon, settingsItems]);

  const ActiveComponent = sectionComponents[activeSection];
  const activeItem = settingsItems.find(item => item.id === activeSection);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 items-start">
      <aside className="md:col-span-1">
        <SettingsNavigation 
          items={settingsItems} 
          activeId={activeSection} 
          onItemClick={(id) => setActiveSection(id as ActiveSection)}
        />
      </aside>
      
      <main className="md:col-span-3">
        <Card>
          <CardHeader>
            <CardTitle>{activeItem?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {ActiveComponent && <ActiveComponent />}
          </CardContent>
        </Card>
      </main>
    </div>
  );
} 