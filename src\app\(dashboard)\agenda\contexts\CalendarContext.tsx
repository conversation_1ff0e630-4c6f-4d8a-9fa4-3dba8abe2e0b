'use client'

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { format, startOfWeek, endOfWeek, addWeeks, subWeeks, parseISO, addDays, setHours, setMinutes } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { getAgendaClasses } from '../actions/get-agenda-classes'
import type { AgendaClass, AgendaFilters } from '../types/agenda'

export interface CalendarEvent {
  id: string
  code: string
  title: string
  startTime: Date
  endTime: Date
  customer?: {
    name: string
    avatar: string
  }
  service: string
  status: 'pending' | 'ongoing' | 'completed' | 'cancelled'
  stylist: string
  color: 'green' | 'yellow' | 'red' | 'blue'
  location?: string
  type: 'class' | 'appointment' | 'event'
}

interface CalendarContextType {
  currentDate: Date
  selectedDate: Date | null
  events: CalendarEvent[]
  selectedEvent: CalendarEvent | null
  isModalOpen: boolean
  isLoading: boolean
  error: string | null
  filters: Partial<AgendaFilters>
  
  // Actions
  setCurrentDate: (date: Date) => void
  setSelectedDate: (date: Date | null) => void
  setSelectedEvent: (event: CalendarEvent | null) => void
  setIsModalOpen: (open: boolean) => void
  nextWeek: () => void
  prevWeek: () => void
  goToToday: () => void
  loadEvents: (customFilters?: Partial<AgendaFilters>) => Promise<void>
  getTimeSlots: () => { time: string; hour: number }[]
  refreshEvents: () => Promise<void>
  updateFilters: (newFilters: Partial<AgendaFilters>) => void
  clearFilters: () => void
}

const CalendarContext = createContext<CalendarContextType | undefined>(undefined)

export function CalendarProvider({ children }: { children: React.ReactNode }) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<Partial<AgendaFilters>>({})

  const nextWeek = useCallback(() => {
    setCurrentDate(prev => addWeeks(prev, 1))
  }, [])

  const prevWeek = useCallback(() => {
    setCurrentDate(prev => subWeeks(prev, 1))
  }, [])

  const goToToday = useCallback(() => {
    setCurrentDate(new Date())
  }, [])

  // Função para mapear status do banco para status do calendário
  const mapStatus = (status: string): 'pending' | 'ongoing' | 'completed' | 'cancelled' => {
    switch (status) {
      case 'scheduled':
        return 'pending'
      case 'ongoing':
        return 'ongoing'
      case 'completed':
        return 'completed'
      case 'cancelled':
        return 'cancelled'
      case 'rescheduled':
        return 'pending'
      default:
        return 'pending'
    }
  }

  // Função para mapear status para cor
  const getStatusColor = (status: string): 'green' | 'yellow' | 'red' | 'blue' => {
    switch (status) {
      case 'completed':
        return 'green'
      case 'scheduled':
        return 'blue'
      case 'ongoing':
        return 'yellow'
      case 'cancelled':
        return 'red'
      case 'rescheduled':
        return 'yellow'
      default:
        return 'blue'
    }
  }

  // Função para transformar AgendaClass em CalendarEvent
  const transformAgendaClassToEvent = (agendaClass: AgendaClass): CalendarEvent => {
    return {
      id: agendaClass.id,
      code: `#${agendaClass.id.slice(0, 8)}`,
      title: agendaClass.name,
      startTime: parseISO(agendaClass.start_time),
      endTime: parseISO(agendaClass.end_time),
      service: agendaClass.class_group_name || 'Aula',
      status: mapStatus(agendaClass.status),
      stylist: agendaClass.instructor_name,
      color: getStatusColor(agendaClass.status),
      location: agendaClass.branch_name,
      type: 'class'
    }
  }

  const loadEvents = useCallback(async (customFilters?: Partial<AgendaFilters>) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 })
      const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 })
      
      const activeFilters = customFilters || filters
      
      const result = await getAgendaClasses({
        startDate: weekStart.toISOString(),
        endDate: weekEnd.toISOString(),
        ...activeFilters
      })
      
      if (result.success && result.data) {
        const transformedEvents = result.data.map(transformAgendaClassToEvent)
        setEvents(transformedEvents)
      } else {
        setError(result.errors?._form || 'Erro ao carregar eventos')
        setEvents([])
      }
    } catch (error) {
      console.error('Erro ao carregar eventos:', error)
      setError('Erro inesperado ao carregar eventos')
      setEvents([])
    } finally {
      setIsLoading(false)
    }
  }, [currentDate, filters])

  const refreshEvents = useCallback(async () => {
    await loadEvents()
  }, [loadEvents])

  const updateFilters = useCallback((newFilters: Partial<AgendaFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({})
  }, [])

  // Função para gerar horários fixos de 00:00 às 23:00
  const getTimeSlots = useCallback(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      time: `${i.toString().padStart(2, '0')}:00`,
      hour: i
    }))
  }, [])

  useEffect(() => {
    loadEvents()
  }, [loadEvents])

  const value: CalendarContextType = {
    currentDate,
    selectedDate,
    events,
    selectedEvent,
    isModalOpen,
    isLoading,
    error,
    filters,
    setCurrentDate,
    setSelectedDate,
    setSelectedEvent,
    setIsModalOpen,
    nextWeek,
    prevWeek,
    goToToday,
    loadEvents,
    getTimeSlots,
    refreshEvents,
    updateFilters,
    clearFilters
  }

  return (
    <CalendarContext.Provider value={value}>
      {children}
    </CalendarContext.Provider>
  )
}

export function useCalendar() {
  const context = useContext(CalendarContext)
  if (context === undefined) {
    throw new Error('useCalendar must be used within a CalendarProvider')
  }
  return context
} 