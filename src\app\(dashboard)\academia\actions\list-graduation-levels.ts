'use server';

import { requireAuth } from '@/services/auth/actions/auth-actions';
import { listGraduationLevels } from '@/services/belts/levels';

interface ActionResult {
  success: boolean;
  levels?: Awaited<ReturnType<typeof listGraduationLevels>>;
  errors?: any;
}

/**
 * Server Action: lista todos os níveis de graduação para uma modalidade específica
 * 
 * Requisitos:
 *  • Usuário autenticado com role `admin`
 *  • Tenant identificado no app_metadata do usuário
 */
export async function listGraduationLevelsAction(modalitySlug: string = 'jiujitsu_adulto'): Promise<ActionResult> {
  const { user } = await requireAuth();

  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    return { success: false, errors: { _form: 'Tenant não identificado' } };
  }
  if (role !== 'admin') {
    return { success: false, errors: { _form: 'Acesso negado' } };
  }

  try {
    const levels = await listGraduationLevels(tenantId, modalitySlug);
    return { success: true, levels };
  } catch (error: any) {
    console.error('[listGraduationLevelsAction] erro:', error);
    return { success: false, errors: { _form: 'Falha ao listar níveis' } };
  }
} 