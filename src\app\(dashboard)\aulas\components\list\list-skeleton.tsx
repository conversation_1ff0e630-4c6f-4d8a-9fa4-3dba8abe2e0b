import { Skeleton } from '@/components/ui/skeleton';

interface ListSkeletonProps {
  mode?: 'card' | 'table';
  count?: number;
}

export function ListSkeleton({ mode = 'card', count = 6 }: ListSkeletonProps) {
  if (mode === 'table') {
    return (
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b bg-muted/50">
              <tr>
                <th className="h-12 px-4 text-left">
                  <Skeleton className="h-4 w-16" />
                </th>
                <th className="h-12 px-4 text-left">
                  <Skeleton className="h-4 w-20" />
                </th>
                <th className="h-12 px-4 text-left">
                  <Skeleton className="h-4 w-16" />
                </th>
                <th className="h-12 px-4 text-left">
                  <Skeleton className="h-4 w-20" />
                </th>
                <th className="h-12 px-4 text-left">
                  <Skeleton className="h-4 w-12" />
                </th>
                <th className="h-12 px-4 text-left">
                  <Skeleton className="h-4 w-20" />
                </th>
                <th className="h-12 px-4 text-right">
                  <Skeleton className="h-4 w-12" />
                </th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: count }).map((_, index) => (
                <tr key={index} className="border-b">
                  <td className="p-4">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                  </td>
                  <td className="p-4">
                    <Skeleton className="h-6 w-16" />
                  </td>
                  <td className="p-4">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-2 w-full" />
                    </div>
                  </td>
                  <td className="p-4">
                    <Skeleton className="h-6 w-16" />
                  </td>
                  <td className="p-4">
                    <Skeleton className="h-4 w-20" />
                  </td>
                  <td className="p-4 text-right">
                    <Skeleton className="h-8 w-8" />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="rounded-lg border p-6 space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-48" />
            </div>
            <Skeleton className="h-6 w-16" />
          </div>

          {/* Instructor and Branch */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-24" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>

          {/* Capacity */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>

          {/* Details */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center pt-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      ))}
    </div>
  );
} 