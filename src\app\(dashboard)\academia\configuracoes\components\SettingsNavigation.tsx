'use client';

import { ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import Link from 'next/link';

export interface NavItem {
  id: string;
  title: string;
  description: string;
  href?: string;
}

interface SettingsNavigationProps {
  items: NavItem[];
  activeId: string;
  onItemClick: (id: string) => void;
}

export function SettingsNavigation({ items, activeId, onItemClick }: SettingsNavigationProps) {
  return (
    <nav className="flex flex-col bg-card border border-border rounded-xl shadow-sm" aria-label="Settings navigation">
      {items.map((item) => {
        const isActive = activeId === item.id;

        const content = (
          <div
            className={clsx(
              'group flex w-full items-center justify-between gap-2 rounded-md px-4 py-3 transition-colors duration-150 ease-in-out',
              {
                'bg-muted': isActive,
                'hover:bg-muted/50 text-foreground': !isActive,
              }
            )}
          >
            <div className="flex flex-col text-left">
              <span
                className={clsx('text-sm font-medium', {
                  'text-primary': isActive,
                  'text-foreground': !isActive,
                })}
              >
                {item.title}
              </span>
              <span
                className={clsx('text-xs mt-1', {
                  'text-muted-foreground': !isActive,
                  'text-foreground/80': isActive,
                })}
              >
                {item.description}
              </span>
            </div>

            {item.href ? (
              <ArrowTopRightOnSquareIcon className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
            ) : null}
          </div>
        );

        if (item.href) {
          return (
            <Link href={item.href} key={item.id} className="w-full no-underline">
              {content}
            </Link>
          );
        }

        return (
          <button
            key={item.id}
            type="button"
            className="w-full"
            onClick={() => onItemClick(item.id)}
            aria-current={isActive ? 'page' : undefined}
          >
            {content}
          </button>
        );
      })}
    </nav>
  );
} 