export interface AgendaClass {
  id: string
  name: string
  description?: string
  start_time: string
  end_time: string
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled' | 'rescheduled'
  instructor_id: string
  instructor_name: string
  class_group_id?: string
  class_group_name?: string
  branch_id: string
  branch_name: string
  max_capacity?: number
  attendance_recorded: boolean
  notes?: string
}

export interface AgendaFilters {
  startDate: string
  endDate: string
  branchId?: string
  instructorId?: string
  classGroupId?: string
  status?: string[]
}

export interface AgendaResponse {
  success: boolean
  data?: AgendaClass[]
  errors?: Record<string, string>
} 