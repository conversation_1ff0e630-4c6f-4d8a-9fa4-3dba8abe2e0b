'use client'

import React from 'react'
import { format } from 'date-fns'
import { CalendarEvent } from '../contexts/CalendarContext'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface StackedEventsTooltipProps {
  events: CalendarEvent[]
  isVisible: boolean
  position: { x: number; y: number }
}

export function StackedEventsTooltip({ events, isVisible, position }: StackedEventsTooltipProps) {
  if (!isVisible || events.length <= 1) return null

  const getStatusColor = (status: CalendarEvent['status']) => {
    switch (status) {
      case 'ongoing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusText = (status: CalendarEvent['status']) => {
    switch (status) {
      case 'ongoing':
        return 'Em Andamento'
      case 'pending':
        return 'Agendado'
      case 'completed':
        return 'Concluído'
      case 'cancelled':
        return 'Cancelado'
      default:
        return status
    }
  }

  return (
    <div
      className="fixed z-50 pointer-events-none"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)'
      }}
    >
      <Card className="shadow-lg border-2 max-w-sm">
        <CardContent className="p-3">
          <div className="text-sm font-semibold mb-2 text-center">
            {events.length} eventos no mesmo horário
          </div>
          
          <div className="space-y-2">
            {events.map((event, index) => (
              <div
                key={event.id}
                className={cn(
                  "p-2 rounded-md border-l-4 bg-muted/50",
                  event.color === 'blue' && "border-l-blue-500",
                  event.color === 'green' && "border-l-green-500",
                  event.color === 'yellow' && "border-l-yellow-500",
                  event.color === 'red' && "border-l-red-500"
                )}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-bold text-muted-foreground">
                    {event.code}
                  </span>
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs", getStatusColor(event.status))}
                  >
                    {getStatusText(event.status)}
                  </Badge>
                </div>
                
                <div className="font-medium text-sm mb-1">
                  {event.title}
                </div>
                
                <div className="text-xs text-muted-foreground mb-1">
                  {format(event.startTime, 'HH:mm')} - {format(event.endTime, 'HH:mm')}
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">
                    {event.stylist}
                  </span>
                  {event.location && (
                    <span className="text-muted-foreground">
                      📍 {event.location}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-xs text-center text-muted-foreground mt-2">
            Clique em qualquer evento para ver detalhes
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 