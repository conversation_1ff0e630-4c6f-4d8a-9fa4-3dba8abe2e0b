'use client'

import React, { useState, useEffect } from 'react'
import { format, isSameDay } from 'date-fns'
import { CalendarEvent } from '../contexts/CalendarContext'
import { EventBlock } from './EventBlock'
import { StackedEventsTooltip } from './StackedEventsTooltip'
import { cn } from '@/lib/utils'

interface DayColumnProps {
  day: Date
  events: CalendarEvent[]
  timeSlots: Array<{ time: string; hour: number }>
}

interface EventPosition {
  eventId: string
  top: number
  height: number
  width: string
  left: string
  zIndex: number
  stackIndex: number
  totalInStack: number
  stackedEvents?: CalendarEvent[]
}

export function DayColumn({ day, events, timeSlots }: DayColumnProps) {
  const isToday = isSameDay(day, new Date())
  const [currentTime, setCurrentTime] = useState(new Date())
  const [tooltipState, setTooltipState] = useState<{
    isVisible: boolean
    events: CalendarEvent[]
    position: { x: number; y: number }
  }>({
    isVisible: false,
    events: [],
    position: { x: 0, y: 0 }
  })

  // Atualizar a hora atual a cada minuto
  useEffect(() => {
    const updateCurrentTime = () => {
      setCurrentTime(new Date())
    }

    // Atualizar imediatamente
    updateCurrentTime()

    // Configurar intervalo para atualizar a cada minuto
    const interval = setInterval(updateCurrentTime, 60000)

    return () => clearInterval(interval)
  }, [])

  // Altura de cada slot de horário (48px fixo)
  const SLOT_HEIGHT = 48

  // Calcular a posição da linha de hora atual
  const getCurrentTimePosition = () => {
    const currentHour = currentTime.getHours()
    const currentMinutes = currentTime.getMinutes()
    
    // Posição em pixels baseada no horário atual
    return (currentHour * SLOT_HEIGHT) + (currentMinutes / 60 * SLOT_HEIGHT)
  }

  // Função para verificar se dois eventos têm sobreposição temporal
  const eventsOverlap = (event1: CalendarEvent, event2: CalendarEvent): boolean => {
    const start1 = event1.startTime.getTime()
    const end1 = event1.endTime.getTime()
    const start2 = event2.startTime.getTime()
    const end2 = event2.endTime.getTime()
    
    return (start1 < end2 && end1 > start2)
  }

  // Função para verificar se dois eventos começam no mesmo horário exato
  const sameStartTime = (event1: CalendarEvent, event2: CalendarEvent): boolean => {
    return event1.startTime.getTime() === event2.startTime.getTime()
  }

  // Algoritmo avançado para organizar eventos sobrepostos
  const organizeOverlappingEvents = (): EventPosition[] => {
    const sortedEvents = [...events].sort((a, b) => {
      // Primeiro por horário de início, depois por duração (mais longos primeiro)
      const timeDiff = a.startTime.getTime() - b.startTime.getTime()
      if (timeDiff !== 0) return timeDiff
      
      const durationA = a.endTime.getTime() - a.startTime.getTime()
      const durationB = b.endTime.getTime() - b.startTime.getTime()
      return durationB - durationA
    })

    const eventPositions: EventPosition[] = []
    const processedEvents = new Set<string>()

    sortedEvents.forEach((currentEvent, index) => {
      if (processedEvents.has(currentEvent.id)) return

      // Encontrar todos os eventos que se sobrepõem com o atual
      const overlappingGroup = sortedEvents.filter(event => 
        !processedEvents.has(event.id) && eventsOverlap(currentEvent, event)
      )

      // Separar eventos que começam no mesmo horário exato
      const sameTimeEvents = overlappingGroup.filter(event => 
        sameStartTime(currentEvent, event)
      )

      // Se há múltiplos eventos no mesmo horário exato
      if (sameTimeEvents.length > 1) {
        sameTimeEvents.forEach((event, stackIndex) => {
          const position = calculateEventPosition(event, stackIndex, sameTimeEvents.length, overlappingGroup.length, true)
          position.stackedEvents = sameTimeEvents
          eventPositions.push(position)
          processedEvents.add(event.id)
        })
      } else {
        // Processar grupo de sobreposição normal
        overlappingGroup.forEach((event, groupIndex) => {
          if (!processedEvents.has(event.id)) {
            const position = calculateEventPosition(event, groupIndex, overlappingGroup.length, overlappingGroup.length, false)
            eventPositions.push(position)
            processedEvents.add(event.id)
          }
        })
      }
    })

    return eventPositions
  }

  const calculateEventPosition = (
    event: CalendarEvent, 
    stackIndex: number, 
    totalInStack: number,
    totalOverlapping: number,
    isSameTime: boolean
  ): EventPosition => {
    const startHour = event.startTime.getHours()
    const startMinutes = event.startTime.getMinutes()
    const endHour = event.endTime.getHours()
    const endMinutes = event.endTime.getMinutes()
    
    // Posição vertical baseada no horário
    // Cada slot de hora tem SLOT_HEIGHT px de altura
    const topPosition = (startHour * SLOT_HEIGHT) + (startMinutes / 60 * SLOT_HEIGHT)
    
    // Calcular duração em horas
    const startTimeInHours = startHour + (startMinutes / 60)
    const endTimeInHours = endHour + (endMinutes / 60)
    const durationInHours = endTimeInHours - startTimeInHours
    
    // Altura baseada na duração real
    const height = Math.max(durationInHours * SLOT_HEIGHT, 32) // Altura mínima de 32px
    
    let width: string
    let leftOffset: string
    let zIndex: number

    if (isSameTime && totalInStack > 1) {
      // Eventos no mesmo horário exato - empilhamento visual
      const baseWidth = 90 // Largura base em %
      const stackOffset = stackIndex * 8 // Offset em % para criar efeito de empilhamento
      const stackWidth = Math.max(baseWidth - (totalInStack - 1) * 5, 60) // Reduzir largura proporcionalmente
      
      width = `${stackWidth}%`
      leftOffset = `${stackOffset}%`
      zIndex = 20 + stackIndex // Z-index mais alto para empilhamento
    } else if (totalOverlapping > 1) {
      // Sobreposição temporal normal - divisão lado a lado
      const eventWidth = Math.floor(95 / totalOverlapping)
      width = `${eventWidth}%`
      leftOffset = `${stackIndex * eventWidth}%`
      zIndex = 10 + stackIndex
    } else {
      // Evento único
      width = '95%'
      leftOffset = '2.5%'
      zIndex = 10
    }
    
    return {
      eventId: event.id,
      top: topPosition,
      height,
      width,
      left: leftOffset,
      zIndex,
      stackIndex,
      totalInStack
    }
  }

  const handleStackHover = (
    stackedEvents: CalendarEvent[], 
    event: React.MouseEvent<HTMLDivElement>
  ) => {
    if (stackedEvents.length > 1) {
      const rect = event.currentTarget.getBoundingClientRect()
      setTooltipState({
        isVisible: true,
        events: stackedEvents,
        position: {
          x: rect.left + rect.width / 2,
          y: rect.top
        }
      })
    }
  }

  const handleStackLeave = () => {
    setTooltipState(prev => ({ ...prev, isVisible: false }))
  }

  const eventPositions = organizeOverlappingEvents()

  return (
    <>
      <div className={cn(
        "relative border-r border-border last:border-r-0 min-h-full",
        isToday && "bg-primary/5"
      )}>
        {/* Grid de horários */}
        {timeSlots.map((slot) => (
          <div 
            key={slot.time}
            className="border-b border-border calendar-grid-cell"
            style={{ height: `${SLOT_HEIGHT}px` }}
          />
        ))}
        
        {/* Linha indicadora da hora atual - apenas no dia atual */}
        {isToday && (
          <div
            className="absolute left-0 right-0 z-30 pointer-events-none"
            style={{ 
              top: `${getCurrentTimePosition()}px`,
              height: '2px'
            }}
          >
            {/* Linha vermelha com animação */}
            <div className="h-full current-time-line" />
            
            {/* Indicador circular no início da linha */}
            <div className="absolute -left-1 -top-1 w-4 h-4 current-time-indicator rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full" />
            </div>
            
            {/* Label com a hora atual */}
            <div className="absolute -left-12 -top-3 current-time-label text-xs px-2 py-1 rounded">
              {format(currentTime, 'HH:mm')}
            </div>
          </div>
        )}
        
        {/* Eventos posicionados absolutamente */}
        <div className="absolute inset-0 p-1">
          {eventPositions.map((position) => {
            const event = events.find(e => e.id === position.eventId)
            
            if (!event) return null

            return (
              <div 
                key={`${event.id}-${position.stackIndex}`} 
                className="relative"
                onMouseEnter={(e) => position.stackedEvents && handleStackHover(position.stackedEvents, e)}
                onMouseLeave={handleStackLeave}
              >
                <EventBlock
                  event={event}
                  stackInfo={{
                    stackIndex: position.stackIndex,
                    totalInStack: position.totalInStack,
                    isStacked: position.totalInStack > 1
                  }}
                  style={{
                    position: 'absolute',
                    top: `${position.top}px`,
                    height: `${position.height}px`,
                    width: position.width,
                    left: position.left,
                    zIndex: position.zIndex,
                    minHeight: '32px'
                  }}
                />
              </div>
            )
          })}
        </div>
      </div>

      {/* Tooltip para eventos empilhados */}
      <StackedEventsTooltip
        events={tooltipState.events}
        isVisible={tooltipState.isVisible}
        position={tooltipState.position}
      />
    </>
  )
} 