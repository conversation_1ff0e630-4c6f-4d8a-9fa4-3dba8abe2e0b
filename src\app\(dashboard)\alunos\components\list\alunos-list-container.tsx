'use client';

import { useState } from 'react';
import { AlunosList } from "./alunos-list";
import { ListSkeleton } from './list-skeleton';
import { User } from '../selectusers/types';
import { useStudentsQuery } from "../../hooks/use-students-query";
import { RefreshIndicator } from './refresh-indicator';
import { RowActionProvider } from "../../hooks/use-row-action-animation";

export function AlunosListContainer() {
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const { isLoading, isFetching } = useStudentsQuery();

  const handleToggleSelectionMode = () => {
    setIsSelecting(prev => !prev);
    // Limpar seleção ao desativar o modo de seleção
    if (isSelecting) {
      setSelectedUsers([]);
    }
  };

  // Mostrar o loading completo apenas na primeira carga
  if (isLoading) {
    return <ListSkeleton />;
  }

  return (
    <RowActionProvider>
      <div className="relative">
        {/* Indicador de atualização quando está buscando mas não no carregamento inicial */}
        {isFetching && !isLoading && <RefreshIndicator />}
        
        <AlunosList 
          isSelecting={isSelecting} 
          selectedUsers={selectedUsers} 
          setSelectedUsers={setSelectedUsers} 
          onCloseSelectionMode={() => setIsSelecting(false)}
          onActivateSelectionMode={() => setIsSelecting(true)}
        />
      </div>
    </RowActionProvider>
  );
}
