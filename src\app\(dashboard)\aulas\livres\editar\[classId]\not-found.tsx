import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, ChevronLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-muted-foreground" />
            </div>
            <CardTitle className="text-xl">Aula Livre Não Encontrada</CardTitle>
            <CardDescription>
              A aula livre que você está tentando editar não foi encontrada ou não existe.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p>Possíveis causas:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>A aula foi excluída</li>
                <li>Você não tem permissão para editá-la</li>
                <li>A aula não é uma aula livre</li>
                <li>Link inválido ou expirado</li>
              </ul>
            </div>
            
            <div className="flex flex-col gap-2">
              <Button asChild className="w-full">
                <Link href="/aulas/livres">
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Voltar às Aulas Livres
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full">
                <Link href="/aulas">
                  Ver Todas as Aulas
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 