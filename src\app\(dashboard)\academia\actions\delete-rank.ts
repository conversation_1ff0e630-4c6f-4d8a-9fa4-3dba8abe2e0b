'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { deleteBeltLevel } from '@/services/belts/levels';
import { requireAuth } from '@/services/auth/actions/auth-actions';

// Schema de validação para a entrada
const schema = z.object({
  beltLevelId: z.string().min(1, 'ID da faixa é obrigatório'),
});

type Input = z.infer<typeof schema>;

/**
 * Action para deletar um nível de graduação (rank/faixa)
 */
export async function deleteRankAction(
  input: Input
): Promise<{ success: boolean; errors?: Record<string, string> }> {
  try {
    // Autenticação
    const { user } = await requireAuth();
    const tenantId = (user.app_metadata as any)?.tenant_id;
    const role = (user.app_metadata as any)?.role;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado.' },
      };
    }

    if (role !== 'admin') {
      return {
        success: false,
        errors: { _form: 'Permissão negada. Somente administradores podem excluir graduações.' },
      };
    }

    // Validação dos dados de entrada
    const validationResult = schema.safeParse(input);
    if (!validationResult.success) {
      const errors = validationResult.error.format();
      return {
        success: false,
        errors: { _form: 'Dados inválidos.' },
      };
    }

    // Executar a remoção
    const result = await deleteBeltLevel(tenantId, input.beltLevelId);

    if (!result.success) {
      return {
        success: false,
        errors: { _form: result.error?.message || 'Falha ao excluir a graduação.' },
      };
    }

    // Revalidar o caminho para atualizar os dados
    revalidatePath('/academia/configuracoes/modalidades/[slug]/editar');

    return { success: true };
  } catch (error: any) {
    console.error('Erro ao deletar graduação:', error);
    return {
      success: false,
      errors: { _form: 'Ocorreu um erro inesperado. Tente novamente.' },
    };
  }
} 