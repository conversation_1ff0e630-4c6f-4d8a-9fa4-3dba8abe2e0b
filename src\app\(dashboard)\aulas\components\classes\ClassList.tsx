'use client';

import { useState } from 'react';
import { ClassWithDetails } from '../../types';
import { ClassCard } from './ClassCard';
import { ClassesTable } from './ClassesTable';
import { Button } from '@/components/ui/button';
import { LayoutGrid, List, RefreshCw } from 'lucide-react';
import { ListSkeleton } from '../list/list-skeleton';

interface ClassListProps {
  classes: ClassWithDetails[];
  isLoading?: boolean;
  showGroupInfo?: boolean;
  onRefresh?: () => void;
  onCancel?: (classId: string) => void;
  onReschedule?: (classId: string) => void;
  emptyMessage?: string;
  emptyDescription?: string;
}

export function ClassList({
  classes,
  isLoading = false,
  showGroupInfo = true,
  onRefresh,
  onCancel,
  onReschedule,
  emptyMessage = "Nenhuma aula encontrada",
  emptyDescription = "Não há aulas para exibir no momento."
}: ClassListProps) {
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');

  if (isLoading) {
    return <ListSkeleton />;
  }

  if (classes.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
          <List className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="mt-4 text-lg font-semibold">{emptyMessage}</h3>
        <p className="mt-2 text-sm text-muted-foreground max-w-sm">{emptyDescription}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Controles da Lista */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            {classes.length} aula{classes.length !== 1 ? 's' : ''} encontrada{classes.length !== 1 ? 's' : ''}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="h-8"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
          )}

          {/* Toggle de Visualização */}
          <div className="flex items-center rounded-md border border-input bg-background">
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="h-8 rounded-r-none"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="h-8 rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Lista de Aulas */}
      {viewMode === 'cards' ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {classes.map((classItem) => (
            <ClassCard
              key={classItem.id}
              class={classItem}
              showGroupInfo={showGroupInfo}
              onCancel={onCancel}
              onReschedule={onReschedule}
            />
          ))}
        </div>
      ) : (
        <ClassesTable
          classes={classes}
          showGroupInfo={showGroupInfo}
          onCancel={onCancel}
          onReschedule={onReschedule}
        />
      )}
    </div>
  );
} 