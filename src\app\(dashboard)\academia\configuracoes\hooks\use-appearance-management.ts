'use client';

import { useEffect, useState, useCallback } from 'react';
import {
  getAppearance,
  updateAppearance,
  AppearanceFormData,
} from '../actions/appearance-actions';
import { useToast } from '@/hooks/ui/use-toast';

interface AppearanceData {
  primaryColor: string | null;
  description: string | null; // JSON string com slides
}

export function useAppearanceManagement() {
  const [data, setData] = useState<AppearanceData>({
    primaryColor: null,
    description: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSavingColors, setIsSavingColors] = useState(false);
  const [isSavingCarousel, setIsSavingCarousel] = useState(false);

  const { toast } = useToast();

  const extractErrorMessage = (errors: unknown): string => {
    if (errors && typeof errors === 'object' && '_form' in errors) {
      return (errors as { _form: string })._form;
    }
    return 'Erro desconhecido';
  };

  const loadAppearance = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await getAppearance();
      if (result.success && result.data) {
        // Ignora secondaryColor pois não é mais configurável
        setData({
          primaryColor: result.data.primaryColor,
          description: result.data.description,
        });
      } else {
        toast({
          title: 'Erro ao carregar aparência',
          description: extractErrorMessage(result.errors),
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Erro ao carregar aparência',
        description: 'Erro interno do servidor',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadAppearance();
  }, [loadAppearance]);

  const savePartialAppearance = async (
    payload: Partial<AppearanceFormData>,
    setIsSaving: (isSaving: boolean) => void
  ): Promise<boolean> => {
    setIsSaving(true);
    try {
      const fullPayload = { ...data, ...payload };
      const result = await updateAppearance(fullPayload);

      if (result.success) {
        toast({ title: 'Aparência atualizada com sucesso.' });
        setData((currentData) => ({ ...currentData, ...payload }));
        return true;
      } else {
        toast({
          title: 'Erro ao salvar',
          description: extractErrorMessage(result.errors),
          variant: 'destructive',
        });
        return false;
      }
    } catch (err) {
      toast({
        title: 'Erro ao salvar',
        description: 'Erro interno do servidor',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const saveColors = (payload: { primaryColor: string | null }) => {
    return savePartialAppearance(payload, setIsSavingColors);
  };

  const saveCarousel = (payload: { description: string | null }) => {
    return savePartialAppearance(payload, setIsSavingCarousel);
  };

  return {
    data,
    isLoading,
    isSavingColors,
    isSavingCarousel,
    loadAppearance,
    saveColors,
    saveCarousel,
    setLocalData: setData,
  };
} 