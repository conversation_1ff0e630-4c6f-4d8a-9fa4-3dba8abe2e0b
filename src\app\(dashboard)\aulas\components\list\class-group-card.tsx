'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  TooltipContent,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Users, 
  MapPin, 
  Calendar,
  MoreHorizontal,
  UserPlus,
  Settings,
  Eye,
  Edit,
  GraduationCap,
  Trash2,
  PowerOff,
  Power,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ClassGroupWithDetails } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import { BeltDisplay, beltColorTranslation } from '@/components/belt';
import { useState, useTransition } from 'react';
import { toast } from 'sonner';
import { deleteClassGroup } from '@/app/(dashboard)/turmas/actions/class-group/delete-class-group';
import { changeClassGroupStatus } from '@/app/(dashboard)/turmas/actions/class-group/change-class-group-status';

interface ClassGroupCardProps {
  classGroup: ClassGroupWithDetails;
  onUpdate?: () => void;
}

export function ClassGroupCard({ classGroup, onUpdate }: ClassGroupCardProps) {
  const [isPending, startTransition] = useTransition();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  
  const capacityPercentage = classGroup.capacity_usage_percentage || 0;
  const enrollmentCount = classGroup.current_enrollment_count || 0;
  const maxCapacity = classGroup.max_capacity || 0;
  const waitlistCount = classGroup._count.waitlist || 0;

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-500' : 'bg-red-500';
  };

  const getCategoryColor = (category: string | null) => {
    switch (category) {
      case 'kids': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'teens': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'adults': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'seniors': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getCategoryLabel = (category: string | null) => {
    switch (category) {
      case 'kids': return 'Infantil';
      case 'teens': return 'Adolescente';
      case 'adults': return 'Adulto';
      case 'seniors': return 'Sênior';
      default: return 'Geral';
    }
  };

  const handleDeleteGroup = () => {
    console.log('🔥 Iniciando exclusão da turma:', classGroup.id);
    
    startTransition(async () => {
      let dialogClosed = false;
      
      // Garantir que o dialog será fechado mesmo em caso de erro
      const closeDialog = () => {
        if (!dialogClosed) {
          dialogClosed = true;
          console.log('🚪 Fechando dialog...');
          setShowDeleteDialog(false);
        }
      };

      try {
        console.log('📞 Chamando deleteClassGroup...');
        const result = await deleteClassGroup(classGroup.id);
        console.log('📦 Resultado recebido:', result);
        
        if (result && result.success) {
          console.log('✅ Exclusão bem-sucedida');
          
          // Fechar dialog imediatamente
          closeDialog();
          
          toast.success("Turma excluída", {
            description: 'message' in result ? result.message : "Turma excluída completamente com sucesso",
          });
          
          // Aguardar um pouco antes de atualizar
          setTimeout(() => {
            console.log('🔄 Chamando onUpdate...');
            onUpdate?.();
          }, 300);
          
        } else if (result) {
          console.log('❌ Exclusão falhou:', result);
          let errorMessage = "Erro inesperado";
          
          if ('errors' in result && result.errors && typeof result.errors === 'object') {
            if ('_form' in result.errors && typeof result.errors._form === 'string') {
              errorMessage = result.errors._form;
            }
          }
          
          console.log('📝 Mensagem de erro:', errorMessage);
          
          // Fechar dialog antes de mostrar o erro
          closeDialog();
          
          // Verificar se o erro é devido a alunos matriculados
          const isStudentEnrollmentError = typeof errorMessage === 'string' && 
            errorMessage.includes('alunos matriculados');
          
          if (isStudentEnrollmentError) {
            toast.error("Não é possível excluir a turma", {
              description: "Esta turma possui alunos matriculados. Para excluir a turma, primeiro remova todos os alunos ou use a opção 'Desativar Turma' para preservar o histórico.",
              duration: 6000,
            });
          } else {
            toast.error("Erro ao excluir turma", {
              description: errorMessage,
            });
          }
        } else {
          console.error('❌ Resultado inválido:', result);
          closeDialog();
          
          toast.error("Erro inesperado", {
            description: "Resposta inválida do servidor",
          });
        }
      } catch (error) {
        console.error('💥 Erro capturado:', error);
        closeDialog();
        
        toast.error("Erro inesperado", {
          description: "Não foi possível excluir a turma",
        });
      }
      
      // Garantir que o dialog seja fechado mesmo que algo dê errado
      setTimeout(closeDialog, 100);
    });
  };

  const handleChangeStatus = () => {
    console.log('🔄 Alterando status da turma:', classGroup.id, 'para:', !classGroup.is_active);
    
    startTransition(async () => {
      let dialogClosed = false;
      
      // Garantir que o dialog será fechado mesmo em caso de erro
      const closeDialog = () => {
        if (!dialogClosed) {
          dialogClosed = true;
          console.log('🚪 Fechando dialog de status...');
          setShowStatusDialog(false);
        }
      };

      try {
        const result = await changeClassGroupStatus({
          id: classGroup.id,
          is_active: !classGroup.is_active
        });
        
        console.log('📦 Resultado alteração status:', result);
        
        if (result && result.success) {
          console.log('✅ Status alterado com sucesso');
          
          // Fechar dialog imediatamente
          closeDialog();
          
          toast.success(classGroup.is_active ? "Turma desativada" : "Turma ativada", {
            description: 'message' in result ? result.message : "Status alterado com sucesso",
          });
          
          setTimeout(() => {
            console.log('🔄 Atualizando lista...');
            onUpdate?.();
          }, 300);
          
        } else if (result) {
          console.log('❌ Falha ao alterar status:', result);
          let errorMessage = "Erro inesperado";
          
          if ('errors' in result && result.errors && typeof result.errors === 'object') {
            if ('_form' in result.errors && typeof result.errors._form === 'string') {
              errorMessage = result.errors._form;
            }
          }
          
          console.log('📝 Mensagem de erro:', errorMessage);
          
          // Fechar dialog antes de mostrar o erro
          closeDialog();
          
          toast.error("Erro ao alterar status", {
            description: errorMessage,
          });
        } else {
          console.error('❌ Resultado inválido:', result);
          closeDialog();
          
          toast.error("Erro inesperado", {
            description: "Resposta inválida do servidor",
          });
        }
      } catch (error) {
        console.error('💥 Erro ao alterar status:', error);
        closeDialog();
        
        toast.error("Erro inesperado", {
          description: "Não foi possível alterar o status da turma",
        });
      }
      
      // Garantir que o dialog seja fechado mesmo que algo dê errado
      setTimeout(closeDialog, 100);
    });
  };

  return (
    <>
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div 
                  className={`w-2 h-2 rounded-full ${getStatusColor(classGroup.is_active || false)}`} 
                />
                <CardTitle className="text-lg font-semibold line-clamp-1">
                  {classGroup.name}
                </CardTitle>
              </div>
              
              {classGroup.category && (
                <Badge 
                  variant="secondary" 
                  className={getCategoryColor(classGroup.category)}
                >
                  {getCategoryLabel(classGroup.category)}
                </Badge>
              )}
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled={isPending}>
                  {isPending ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <MoreHorizontal className="h-4 w-4" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/turmas/${classGroup.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    Ver Detalhes
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/turmas/${classGroup.id}/editar`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Editar Turma
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/turmas/${classGroup.id}/alunos`}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Gerenciar Matrículas
                  </Link>
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem
                  onClick={() => setShowStatusDialog(true)}
                  disabled={isPending}
                >
                  {classGroup.is_active ? (
                    <>
                      <PowerOff className="mr-2 h-4 w-4 text-orange-500" />
                      Desativar Turma
                    </>
                  ) : (
                    <>
                      <Power className="mr-2 h-4 w-4 text-green-500" />
                      Ativar Turma
                    </>
                  )}
                </DropdownMenuItem>
                
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                  disabled={isPending}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Excluir Turma
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {classGroup.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {classGroup.description}
            </p>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Informações do Instrutor */}
          <div className="flex items-center space-x-2 text-sm">
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">
              {classGroup.instructor.full_name || 
               `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`.trim()}
            </span>
          </div>

          {/* Filial */}
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{classGroup.branch.name}</span>
          </div>

          {/* Capacidade */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span>Capacidade</span>
              </div>
              <span className="font-medium">
                {enrollmentCount}{maxCapacity > 0 ? ` / ${maxCapacity}` : ''}
              </span>
            </div>
            
            {maxCapacity > 0 && (
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all ${
                    capacityPercentage >= 90 ? 'bg-red-500 dark:bg-red-400' :
                    capacityPercentage >= 75 ? 'bg-yellow-500 dark:bg-yellow-400' :
                    'bg-green-500 dark:bg-green-400'
                  }`}
                  style={{ width: `${Math.min(capacityPercentage, 100)}%` }}
                />
              </div>
            )}
          </div>

          {/* Lista de Espera */}
          {waitlistCount > 0 && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Lista de espera</span>
              <Badge variant="outline" className="text-orange-600 dark:text-orange-400 border-orange-600 dark:border-orange-400">
                {waitlistCount} aguardando
              </Badge>
            </div>
          )}

          {/* Faixa Etária e Graduações */}
          {(classGroup.min_age || classGroup.max_age || classGroup.min_belt_level) && (
            <div className="space-y-1 text-sm">
              {(classGroup.min_age || classGroup.max_age) && (
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Idade</span>
                  <span>
                    {classGroup.min_age && classGroup.max_age 
                      ? `${classGroup.min_age} - ${classGroup.max_age} anos`
                      : classGroup.min_age 
                      ? `A partir de ${classGroup.min_age} anos`
                      : `Até ${classGroup.max_age} anos`
                    }
                  </span>
                </div>
              )}
              
              {classGroup.min_belt_level && (
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Graduação mín.</span>
                  <TooltipProvider>
                    <TooltipRoot>
                      <TooltipTrigger asChild>
                        <div className="cursor-help">
                          <BeltDisplay 
                            belt={classGroup.min_belt_level as any}
                            size="sm"
                            showTranslation={true}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Graduação mínima: {beltColorTranslation[classGroup.min_belt_level as keyof typeof beltColorTranslation]}</p>
                      </TooltipContent>
                    </TooltipRoot>
                  </TooltipProvider>
                </div>
              )}
            </div>
          )}

          {/* Data de Criação */}
          {classGroup.created_at && (
            <div className="flex items-center space-x-2 text-xs text-muted-foreground pt-2 border-t">
              <Calendar className="h-3 w-3" />
              <span>
                Criado {formatDistanceToNow(new Date(classGroup.created_at), { 
                  addSuffix: true, 
                  locale: ptBR 
                })}
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog de Confirmação para Alterar Status */}
      <AlertDialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              {classGroup.is_active ? 'Desativar Turma' : 'Ativar Turma'}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              {classGroup.is_active ? (
                <div>
                  Você está prestes a <strong>desativar</strong> a turma "{classGroup.name}".
                  <br /><br />
                  <strong>A turma será desativada, mas:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Todos os dados históricos serão preservados</li>
                    <li>Alunos não precisam ser removidos</li>
                    <li>A turma pode ser reativada a qualquer momento</li>
                  </ul>
                </div>
              ) : (
                <div>
                  Você está prestes a <strong>reativar</strong> a turma "{classGroup.name}".
                  <br /><br />
                  A turma ficará ativa novamente e poderá receber novas matrículas.
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleChangeStatus}
              disabled={isPending}
              className={classGroup.is_active ? 
                "bg-orange-600 hover:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-700" :
                "bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700"
              }
            >
              {isPending ? 'Processando...' : classGroup.is_active ? 'Desativar' : 'Ativar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de Confirmação para Exclusão */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              Excluir Turma
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div>
                Você está prestes a <strong>excluir permanentemente</strong> a turma "{classGroup.name}".
                <br /><br />

                {/* Mostrar aviso se houver alunos matriculados */}
                {enrollmentCount > 0 && (
                  <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800 mb-4">
                    <div className="flex items-center gap-2 text-amber-800 dark:text-amber-300">
                      <Users className="h-4 w-4" />
                      <strong>Esta turma possui {enrollmentCount} aluno{enrollmentCount !== 1 ? 's' : ''} matriculado{enrollmentCount !== 1 ? 's' : ''}</strong>
                    </div>
                    <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                      A exclusão só será permitida após remover todas as matrículas ativas.
                    </p>
                  </div>
                )}

                <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                  <strong className="text-red-800 dark:text-red-300">⚠️ ATENÇÃO - Esta ação:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-red-700 dark:text-red-400">
                    <li><strong>NÃO PODE ser desfeita</strong></li>
                    <li>Remove a turma e todas as aulas associadas</li>
                    <li>Só é permitida se não houver alunos matriculados</li>
                    <li>Dados históricos serão perdidos</li>
                  </ul>
                </div>
                <br />
                <strong>Alternativa recomendada:</strong> Use "Desativar Turma" para preservar o histórico.
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteGroup}
              disabled={isPending || enrollmentCount > 0}
              className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isPending ? 'Excluindo...' : 
               enrollmentCount > 0 ? 'Não é possível excluir' : 
               'Excluir Permanentemente'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 