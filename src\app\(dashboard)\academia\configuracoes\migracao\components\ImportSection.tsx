import { Dumbbell } from 'lucide-react';
import ImportCard from './ImportCard';

export default function ImportSection() {
  const importItems = [
    {
      icon: <Dumbbell className="h-6 w-6" />,
      title: 'Importar Alunos',
      description: 'Importe os dados dos seus alunos do seu provedor anterior.',
    },
    // {
    //   icon: <IdCard className="h-6 w-6" />,
    //   title: 'Importar Matrículas',
    //   description: 'Importe a estrutura e os dados dos seus planos do seu provedor anterior.',
    // },
    // {
    //   icon: <CalendarCheck className="h-6 w-6" />,
    //   title: 'Importar Presenças',
    //   description: 'Importe o histórico de dados de presença do seu provedor anterior.',
    // },
    // {
    //   icon: <Filter className="h-6 w-6" />,
    //   title: 'Importar Leads',
    //   description: 'Importe os leads do seu provedor anterior.',
    // },
    // {
    //   icon: <CircleDollarSign className="h-6 w-6" />,
    //   title: 'Importar Pagamentos',
    //   description: 'Importe os dados de transação do seu provedor anterior.',
    // },
    // {
    //   icon: <Shirt className="h-6 w-6" />,
    //   title: 'Importar Produtos',
    //   description: 'Importe seu catálogo de produtos do seu provedor anterior.',
    // },
  ];

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {importItems.map((item) => (
        <ImportCard key={item.title} {...item} />
      ))}
    </div>
  );
} 