'use server'

import { createClient } from '@/services/supabase/server'


export async function getBranches() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'Usuário não autenticado' }
  }

  const { data: userInfo, error: userError } = await supabase
    .from('users')
    .select('tenant_id')
    .eq('id', user.id)
    .single()

  if (userError || !userInfo?.tenant_id) {
    return { error: 'Não foi possível identificar a academia do usuário.' }
  }

  const { data: branches, error: branchesError } = await supabase
    .from('branches')
    .select('id, name')
    .eq('tenant_id', userInfo.tenant_id)
    .is('deleted_at', null)
    .order('name')

  if (branchesError) {
    console.error('Erro ao buscar filiais:', branchesError)
    return { error: 'Ocorreu um erro ao buscar as filiais.' }
  }

  return { data: branches || [] }
} 