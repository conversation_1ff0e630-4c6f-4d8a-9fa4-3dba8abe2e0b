'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { NovoAlunoFormValues } from "../../../actions/schemas/aluno-schema";
import { HeartPulse, Pill, AlertCircle } from "lucide-react";

export default function MedicalInfoSection() {
  const { control } = useFormContext<NovoAlunoFormValues>();

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Informações Médicas */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <HeartPulse className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Informações Médicas
          </h2>
        </div>
        
        <CardContent className="p-6 space-y-4">
          <FormField
            control={control}
            name="health_notes"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <div className="flex items-center">
                  <HeartPulse className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                  <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Observações Médicas
                  </FormLabel>
                </div>
                <FormControl>
                  <Textarea 
                    placeholder="Informações gerais sobre a saúde do aluno" 
                    className="resize-none border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full" 
                    rows={3}
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="allergies"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <AlertCircle className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Alergias
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Textarea 
                      placeholder="Liste alergias conhecidas" 
                      className="resize-none border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full" 
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="medical_conditions"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <HeartPulse className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Condições Médicas
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Textarea 
                      placeholder="Liste condições médicas relevantes" 
                      className="resize-none border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full" 
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={control}
            name="medications"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <div className="flex items-center">
                  <Pill className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                  <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Medicamentos
                  </FormLabel>
                </div>
                <FormControl>
                  <Textarea 
                    placeholder="Liste medicamentos em uso regular" 
                    className="resize-none border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full" 
                    rows={3}
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
} 