"use client";

/**
 * Componente de Gráfico de Fluxo de Caixa Mensal - Fase 5
 * Exibe entradas e saídas mensais em formato de gráfico de barras
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { TrendingUp, TrendingDown, Calendar, DollarSign } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getMonthlyCashFlowData } from '../../actions/metrics/cashflow-actions';
import { MonthlyCashFlowData, DateRange } from '../../types/dashboard-types';
import { formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface MonthlyCashFlowChartProps {
  dateRange: DateRange;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const inflows = payload.find(p => p.dataKey === 'inflows');
    const outflows = payload.find(p => p.dataKey === 'outflows');
    const netFlow = payload.find(p => p.dataKey === 'netFlow');

    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          {label}
        </p>
        {inflows && (
          <p className="text-sm text-emerald-600 dark:text-emerald-400">
            <span className="font-medium">Entradas: </span>
            {formatCurrency(inflows.value)}
          </p>
        )}
        {outflows && (
          <p className="text-sm text-red-600 dark:text-red-400">
            <span className="font-medium">Saídas: </span>
            {formatCurrency(outflows.value)}
          </p>
        )}
        {netFlow && (
          <p className={cn(
            "text-sm font-medium",
            netFlow.value >= 0 
              ? "text-emerald-600 dark:text-emerald-400" 
              : "text-red-600 dark:text-red-400"
          )}>
            <span>Saldo: </span>
            {formatCurrency(netFlow.value)}
          </p>
        )}
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const MonthlyCashFlowChart: React.FC<MonthlyCashFlowChartProps> = ({
  dateRange,
  className
}) => {
  const [data, setData] = useState<MonthlyCashFlowData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar dados
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getMonthlyCashFlowData(dateRange);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        // Formatar dados para o gráfico
        const chartData = (result.data || []).map(item => ({
          ...item,
          month: new Date(item.month + '-01').toLocaleDateString('pt-BR', { 
            month: 'short', 
            year: '2-digit' 
          })
        }));

        setData(chartData);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar gráfico de fluxo de caixa:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateRange]);

  // Calcular estatísticas
  const totalInflows = data.reduce((sum, item) => sum + item.inflows, 0);
  const totalOutflows = data.reduce((sum, item) => sum + item.outflows, 0);
  const netCashFlow = totalInflows - totalOutflows;

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Fluxo de Caixa Mensal
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Entradas e saídas por mês
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Resumo */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <TrendingUp className="h-4 w-4 text-emerald-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Entradas
              </span>
            </div>
            <p className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">
              {formatCurrency(totalInflows)}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <TrendingDown className="h-4 w-4 text-red-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Saídas
              </span>
            </div>
            <p className="text-lg font-semibold text-red-600 dark:text-red-400">
              {formatCurrency(totalOutflows)}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <DollarSign className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Saldo
              </span>
            </div>
            <p className={cn(
              "text-lg font-semibold",
              netCashFlow >= 0 
                ? "text-emerald-600 dark:text-emerald-400" 
                : "text-red-600 dark:text-red-400"
            )}>
              {formatCurrency(netCashFlow)}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400 mb-2">Erro ao carregar dados</p>
              <p className="text-sm text-gray-500">{error}</p>
            </div>
          </div>
        ) : data.length === 0 ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Nenhum dado disponível para o período selecionado
              </p>
            </div>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="month" 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    // Para valores grandes, usar formatação compacta
                    if (Math.abs(value) >= 1000) {
                      return new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL',
                        notation: 'compact',
                        maximumFractionDigits: 1
                      }).format(value);
                    }
                    return formatCurrency(value);
                  }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar 
                  dataKey="inflows" 
                  name="Entradas"
                  fill="#10b981" 
                  radius={[2, 2, 0, 0]}
                />
                <Bar 
                  dataKey="outflows" 
                  name="Saídas"
                  fill="#ef4444" 
                  radius={[2, 2, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
