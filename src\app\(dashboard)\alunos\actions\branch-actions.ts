'use server';

import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';
import { Branch } from '../types';

// Definição local da interface
interface BranchesResponse {
  success: boolean;
  data: Branch[];
  error?: string;
}

/**
 * Busca as filiais disponíveis para o tenant atual
 */
export async function getBranchesByTenant(): Promise<BranchesResponse> {
  try {
    // Obtém o usuário atual
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado',
        data: []
      };
    }

    // Obtém o tenant do usuário
    const tenantExtractor = new TenantExtractorServer();
    const tenantInfo = await tenantExtractor.getTenantIdentifier();

    // Busca as filiais do tenant
    const supabase = await createClient();
    
    const { data: userData } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();
    
    if (!userData?.tenant_id) {
      return {
        success: false,
        error: 'Usuário sem tenant_id associado',
        data: []
      };
    }

    // Busca filiais do tenant específico
    const { data: branches, error } = await supabase
      .from('branches')
      .select('id, name')
      .eq('tenant_id', userData.tenant_id)
      .order('name');
    
    if (error) {
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
    
    // Se não tiver filiais, retorna lista vazia mas com sucesso
    if (!branches || branches.length === 0) {
      return {
        success: false,
        error: 'Nenhuma filial encontrada para este tenant',
        data: []
      };
    }
    
    return {
      success: true,
      data: branches
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar filiais',
      data: []
    };
  }
} 