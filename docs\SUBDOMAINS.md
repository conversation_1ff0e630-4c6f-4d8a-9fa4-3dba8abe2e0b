# Configuração de Subdomínios para Multi-Tenancy

Este documento explica como configurar e usar subdomínios para o sistema multi-tenant do ApexSaaS.

## Visão Geral

O ApexSaaS utiliza subdomínios para separar os diferentes tenants (academias) no sistema. Cada tenant tem seu próprio subdomínio, por exemplo:

- `vilhena.domain.com` - Academia de Vilhena
- `cacoal.domain.com` - Academia de Cacoal
- `ji-parana.domain.com` - Academia de Ji-Paraná

Em ambiente de desenvolvimento, usamos `localhost:3000` como domínio base, então os subdomínios ficam:

- `vilhena.localhost:3000`
- `cacoal.localhost:3000`
- `ji-parana.localhost:3000`

## Configuração para Desenvolvimento Local

Para que os subdomínios funcionem localmente, é necessário configurar o arquivo hosts do seu sistema operacional. Fornecemos um script para automatizar esse processo.

### Passo 1: Executar o Script de Configuração

Execute o seguinte comando com privilégios de administrador:

**Windows (PowerShell como Administrador):**
```
npm run setup-subdomains
```

**Linux/Mac (com sudo):**
```
sudo npm run setup-subdomains
```

Este script adicionará entradas no arquivo hosts do seu sistema para mapear os subdomínios para o endereço IP local (127.0.0.1).

### Passo 2: Iniciar o Servidor de Desenvolvimento

Após configurar os subdomínios, inicie o servidor de desenvolvimento normalmente:

```
npm run dev
```

### Passo 3: Acessar os Tenants

Agora você pode acessar os diferentes tenants usando os subdomínios:

- http://vilhena.localhost:3000
- http://cacoal.localhost:3000
- http://ji-parana.localhost:3000

## Como Funciona

O sistema utiliza o middleware do Next.js para:

1. Detectar o subdomínio na URL
2. Verificar se o subdomínio corresponde a um tenant válido
3. Garantir que os usuários só possam acessar dados do seu próprio tenant

Além disso, implementamos um cliente Supabase personalizado que automaticamente filtra todas as consultas por `tenant_id`, garantindo a segurança e o isolamento dos dados entre tenants.

## Adicionando Novos Tenants

Para adicionar um novo tenant:

1. Adicione o novo subdomínio ao array `subdomains` no arquivo `scripts/setup-local-subdomains.js`
2. Execute novamente o script de configuração
3. Insira o novo tenant no banco de dados com um `slug` correspondente ao subdomínio

## Configuração para Produção

Em ambiente de produção, é necessário configurar o DNS para apontar os subdomínios para o servidor da aplicação. Isso geralmente é feito através de registros CNAME ou configuração de wildcard DNS.

Exemplo de configuração DNS:
- Registro A: `*.domain.com` → [IP do servidor]
- Ou registros CNAME individuais para cada subdomínio

## Solução de Problemas

Se você encontrar problemas com os subdomínios:

1. Verifique se o arquivo hosts foi configurado corretamente
2. Certifique-se de que o tenant existe no banco de dados com o slug correto
3. Limpe o cache do navegador ou tente em uma janela anônima
4. Verifique os logs do servidor para mensagens de erro específicas 