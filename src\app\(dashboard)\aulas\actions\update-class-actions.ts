"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import {
  UpdateClassSchema,
  type UpdateClass,
} from "./schemas/index";

/**
 * Atualiza uma aula existente
 */
export async function updateClass(classId: string, data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = UpdateClassSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const updateData = validationResult.data;
    const supabase = await createClient();

    // Verificar se a aula existe e pertence ao tenant
    const { data: existingClass, error: existingError } = await supabase
      .from("classes")
      .select("id, status, attendance_recorded, class_group_id")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (existingError || !existingClass) {
      return { success: false, errors: { _form: "Aula não encontrada" } };
    }

    // Verificar permissão: apenas administradores podem editar aulas livres (sem turma associada)
    const userRole = user.app_metadata?.role as string | undefined;
    if (existingClass.class_group_id === null && userRole !== "admin") {
      return {
        success: false,
        errors: { _form: "Apenas administradores podem editar aulas livres" },
      };
    }

    // Não permitir edição de aulas já completadas ou com presença registrada
    if (existingClass.status === "completed" || existingClass.attendance_recorded) {
      return { 
        success: false, 
        errors: { _form: "Não é possível editar aulas concluídas ou com presença registrada" } 
      };
    }

    // Se há mudança de instrutor, verificar se é válido
    if (updateData.instructor_id) {
      const { data: instructor, error: instructorError } = await supabase
        .from("users")
        .select("id, role, status")
        .eq("id", updateData.instructor_id)
        .eq("tenant_id", tenantId)
        .single();

      if (instructorError || !instructor || instructor.role !== "instructor" || instructor.status !== "active") {
        return { success: false, errors: { instructor_id: "Instrutor não encontrado ou inativo" } };
      }

      // Verificar conflitos de horário se mudando instrutor ou horário
      if (updateData.start_time || updateData.end_time) {
        const startTime = updateData.start_time ? new Date(updateData.start_time) : null;
        const endTime = updateData.end_time ? new Date(updateData.end_time) : null;
        
        if (startTime && endTime) {
          const { data: conflictingClasses, error: conflictError } = await supabase
            .from("classes")
            .select("id, name, start_time, end_time")
            .eq("instructor_id", updateData.instructor_id)
            .eq("tenant_id", tenantId)
            .neq("id", classId) // Excluir a própria aula
            .in("status", ["scheduled", "ongoing"])
            .or(`and(start_time.lte.${endTime.toISOString()},end_time.gte.${startTime.toISOString()})`);

          if (!conflictError && conflictingClasses && conflictingClasses.length > 0) {
            return { 
              success: false, 
              errors: { 
                start_time: "Instrutor já possui aula agendada neste horário",
                _conflicts: conflictingClasses 
              } 
            };
          }
        }
      }
    }

    // Atualizar a aula
    const { data: updatedClass, error } = await supabase
      .from("classes")
      .update({
        ...updateData,
        start_time: updateData.start_time ? new Date(updateData.start_time).toISOString() : undefined,
        end_time: updateData.end_time ? new Date(updateData.end_time).toISOString() : undefined,
        updated_at: new Date().toISOString(),
      })
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar aula:", error);
      return { success: false, errors: { _form: "Erro ao atualizar aula" } };
    }

    revalidatePath("/aulas");
    return { success: true, data: updatedClass };
  } catch (error) {
    console.error("Erro ao atualizar aula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 