'use server';

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { updateClassesStatus, updateSingleClassStatus, type StatusUpdateResult } from '@/services/classes/auto-status-update';
import { getCurrentUser } from '@/services/auth/server';
import { checkPermission } from '@/services/permissions/service';

/**
 * Schema para validação da atualização de aula única
 */
const SingleClassUpdateSchema = z.object({
  classId: z.string().uuid('ID da aula deve ser um UUID válido'),
});

/**
 * Server Action para atualizar status de todas as aulas
 * Esta ação é usada pelo cron job e requer permissões de administrador
 */
export async function updateAllClassesStatusAction(): Promise<{
  success: boolean;
  data?: StatusUpdateResult;
  error?: string;
}> {
  try {
    // Verificar autenticação
    const user = await getCurrentUser();
    if (!user.success || !user.data) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Verificar permissões de administrador para operações em lote
    const hasPermission = await checkPermission('system:admin', { 
      userId: user.data.id,
      context: 'auto_update_classes'
    });

    if (!hasPermission) {
      return {
        success: false,
        error: 'Permissão insuficiente para atualizar status de aulas'
      };
    }

    console.log(`🔐 Atualização de status autorizada para admin: ${user.data.email}`);

    // Executar atualização
    const result = await updateClassesStatus();

    // Revalidar caches relacionados a aulas se houve atualizações
    if (result.totalProcessed > 0) {
      revalidatePath('/aulas');
      revalidatePath('/dashboard');
      revalidatePath('/presenca');
      console.log('🔄 Cache revalidado para páginas de aulas');
    }

    return {
      success: true,
      data: result
    };

  } catch (error) {
    console.error('❌ Erro na Server Action de atualização de status:', error);
    return {
      success: false,
      error: 'Erro interno do servidor'
    };
  }
}

/**
 * Server Action para atualizar status de uma aula específica
 * Permite que usuários com permissão atualizem aulas individuais
 */
export async function updateSingleClassStatusAction(input: unknown): Promise<{
  success: boolean;
  message: string;
  oldStatus?: string;
  newStatus?: string;
  error?: string;
}> {
  try {
    // Validar entrada
    const validation = SingleClassUpdateSchema.safeParse(input);
    if (!validation.success) {
      return {
        success: false,
        message: 'Dados inválidos',
        error: validation.error.errors.map(e => e.message).join(', ')
      };
    }

    const { classId } = validation.data;

    // Verificar autenticação
    const user = await getCurrentUser();
    if (!user.success || !user.data) {
      return {
        success: false,
        message: 'Usuário não autenticado',
        error: 'Authentication required'
      };
    }

    // Verificar permissão para atualizar aulas
    const hasPermission = await checkPermission('class:update', { 
      userId: user.data.id,
      resourceId: classId
    });

    if (!hasPermission) {
      return {
        success: false,
        message: 'Permissão insuficiente para atualizar esta aula',
        error: 'Insufficient permissions'
      };
    }

    // Obter tenant do usuário
    const tenantId = user.data.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        message: 'Tenant não identificado',
        error: 'Missing tenant information'
      };
    }

    console.log(`🔐 Atualização de aula autorizada: ${classId} por ${user.data.email}`);

    // Executar atualização
    const result = await updateSingleClassStatus(classId, tenantId);

    // Revalidar cache se a aula foi atualizada
    if (result.success && result.oldStatus !== result.newStatus) {
      revalidatePath('/aulas');
      revalidatePath(`/aulas/${classId}`);
      revalidatePath('/dashboard');
      console.log(`🔄 Cache revalidado para aula: ${classId}`);
    }

    return {
      success: result.success,
      message: result.message,
      oldStatus: result.oldStatus,
      newStatus: result.newStatus
    };

  } catch (error) {
    console.error('❌ Erro na Server Action de atualização de aula:', error);
    return {
      success: false,
      message: 'Erro interno do servidor',
      error: 'Internal server error'
    };
  }
}

/**
 * Server Action para verificar aulas que precisam de atualização (somente leitura)
 * Útil para debugging e monitoramento
 */
export async function checkClassesNeedingUpdateAction(): Promise<{
  success: boolean;
  data?: {
    toOngoing: number;
    toCompleted: number;
    total: number;
  };
  error?: string;
}> {
  try {
    // Verificar autenticação
    const user = await getCurrentUser();
    if (!user.success || !user.data) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Verificar permissão para visualizar estatísticas
    const hasPermission = await checkPermission('class:read', { 
      userId: user.data.id,
      context: 'statistics'
    });

    if (!hasPermission) {
      return {
        success: false,
        error: 'Permissão insuficiente'
      };
    }

    // Importar dinamicamente para evitar dependências circulares
    const { getAllClassesNeedingStatusUpdate } = await import('@/services/classes/status-update-queries');
    const { toOngoing, toCompleted } = await getAllClassesNeedingStatusUpdate();

    return {
      success: true,
      data: {
        toOngoing: toOngoing.length,
        toCompleted: toCompleted.length,
        total: toOngoing.length + toCompleted.length
      }
    };

  } catch (error) {
    console.error('❌ Erro ao verificar aulas para atualização:', error);
    return {
      success: false,
      error: 'Erro interno do servidor'
    };
  }
} 