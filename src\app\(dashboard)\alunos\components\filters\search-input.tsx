"use client";

import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { useAlunosFilter } from "../../hooks/use-alunos-filter";

interface SearchInputProps {
  placeholder?: string;
  className?: string;
}

export function SearchInput({ placeholder = "Buscar alunos...", className }: SearchInputProps) {
  const { filters, updateFilters } = useAlunosFilter();
  const [value, setValue] = useState(filters.search || '');

  // Sincronizar com filtros quando eles mudam externamente
  useEffect(() => {
    if (filters.search !== value) {
      setValue(filters.search || '');
    }
  }, [filters.search, value]);

  const handleSearch = (term: string) => {
    setValue(term);
    updateFilters({ search: term });
  };

  const handleClear = () => {
    handleSearch('');
  };

  return (
    <div className={`relative ${className}`}>
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      
      <Input
        placeholder={placeholder}
        className="pl-9 pr-10 h-10"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            handleSearch(value);
          }
        }}
        onBlur={() => handleSearch(value)}
      />
      
      {value && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-0 top-0 h-full aspect-square rounded-l-none"
          onClick={handleClear}
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
} 