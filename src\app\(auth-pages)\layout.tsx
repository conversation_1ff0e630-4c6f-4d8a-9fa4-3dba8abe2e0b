import { createAdminClient } from '@/services/supabase/server';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';
import { Providers } from '@/app/providers';
import { redirectAuthenticatedUser } from './middleware';

// Função otimizada para obter cores e dados do tenant
async function getTenantColorsOptimized(): Promise<{
  primaryColor: string | null,
  secondaryColor: string | null,
  logoUrl: string | null,
  tenantName: string | null,
  description: string | null
}> {
  try {
    // Importar next/headers dinamicamente
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    const extractor = new TenantExtractorServer();
    const slug = await extractor.extractTenantSlug(headersList, cookieStore);
    
    if (!slug) {
      return {
        primaryColor: null,
        secondaryColor: null,
        logoUrl: null,
        tenantName: null,
        description: null
      };
    }

    // Usar admin client para buscar dados de tenant (dados públicos)
    const supabase = await createAdminClient();
    const { data: tenant } = await supabase
      .from('tenants')
      .select('name, primary_color, secondary_color, logo_url, description')
      .eq('slug', slug)
      .single();
    
    return {
      primaryColor: tenant?.primary_color || null,
      secondaryColor: tenant?.secondary_color || null,
      logoUrl: tenant?.logo_url || null,
      tenantName: tenant?.name || null,
      description: tenant?.description || null
    };
  } catch (error) {
    console.error('Erro ao obter informações do tenant:', error);
    return {
      primaryColor: null,
      secondaryColor: null,
      logoUrl: null,
      tenantName: null,
      description: null
    };
  }
}

export default async function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Redirecionar usuários autenticados (dashboard para admin, home para outros)
  await redirectAuthenticatedUser();
  
  const { primaryColor, secondaryColor, logoUrl, tenantName, description } = await getTenantColorsOptimized();
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Providers 
        primaryColor={primaryColor} 
        secondaryColor={secondaryColor} 
        logoUrl={logoUrl}
        tenantName={tenantName}
        description={description}
      >
        {children}
      </Providers>
    </div>
  )
} 