---
alwaysApply: false
---
# Análise Antes de Propor Soluções

## Análise de Código Existente

Antes de propor qualquer solução ou mudança no código, siga estas etapas:

1. **Analise o código existente** detalhadamente para entender sua estrutura e funcionamento
2. **Explique o funcionamento atual** do código para demonstrar compreensão
3. **Identifique padrões de design** já utilizados no projeto
4. **Observe convenções de nomenclatura** e estilo de código seguidos

## Diagnóstico de Problemas

Quando apresentado com um problema:

1. **Identifique a causa raiz** antes de sugerir qualquer solução
2. **Analise o contexto completo** incluindo dependências e integrações
3. **Forneça diagnósticos precisos** baseados em evidências do código
4. **Considere impactos potenciais** de qualquer mudança proposta

## Proposta de Soluções

Somente após completar a análise e diagnóstico:

1. **Proponha soluções alinhadas** com a arquitetura existente
2. **Mantenha consistência** com os padrões já estabelecidos
3. **Explique o raciocínio** por trás das soluções sugeridas
4. **Ofereça alternativas** quando apropriado, com prós e contras

## Implementação

Ao implementar mudanças:

1. **Respeite a estrutura do projeto** e organização de arquivos
2. **Mantenha padrões de código** consistentes com o resto da base
3. **Documente alterações significativas** com comentários claros
4. **Considere refatorações graduais** em vez de mudanças drásticas

