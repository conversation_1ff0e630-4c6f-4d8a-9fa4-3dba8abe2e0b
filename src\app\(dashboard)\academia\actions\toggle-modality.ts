'use server';

import { z } from 'zod';
import { toggleModality } from '@/services/modalities';
import { requireAuth } from '@/services/auth/actions/auth-actions';

const ToggleSchema = z.object({
  modalityId: z.string().uuid({ message: 'Modality inválida' }),
  enabled: z.boolean(),
});

interface ToggleInput {
  modalityId: string;
  enabled: boolean;
}

interface ActionResult {
  success: boolean;
  errors?: any;
}

/**
 * Server Action para habilitar ou desabilitar uma modalidade para o tenant corrente.
 * Apenas administradores podem chamar.
 */
export async function toggleModalityAction(data: unknown): Promise<ActionResult> {
  const { user } = await requireAuth();

  // Validação dos dados
  const parsed = ToggleSchema.safeParse(data);
  if (!parsed.success) {
    return {
      success: false,
      errors: parsed.error.format(),
    };
  }

  const { modalityId, enabled } = parsed.data as ToggleInput;

  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    return {
      success: false,
      errors: { _form: 'Tenant não identificado' },
    };
  }

  if (role !== 'admin') {
    return {
      success: false,
      errors: { _form: 'Acesso negado' },
    };
  }

  const result = await toggleModality(tenantId, modalityId, enabled);

  if (!result.success) {
    return {
      success: false,
      errors: { _form: 'Falha ao atualizar modalidade' },
    };
  }

  return { success: true };
} 