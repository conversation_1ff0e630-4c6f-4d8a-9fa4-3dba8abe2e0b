import Link from "next/link";
import { Button } from "@/components/ui/button";

interface EmptyStateProps {
  search?: string;
}

export function EmptyState({ search }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="rounded-full bg-muted p-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-8 w-8 text-muted-foreground"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>
      </div>
      
      <h3 className="mt-4 text-lg font-semibold">Nenhum aluno encontrado</h3>
      
      {search ? (
        <div className="mt-2 max-w-md">
          <p className="text-sm text-muted-foreground">
            Não encontramos nenhum aluno correspondente à busca "{search}". 
            Tente utilizar outros termos ou adicione um novo aluno.
          </p>
        </div>
      ) : (
        <div className="mt-2 max-w-md">
          <p className="text-sm text-muted-foreground">
            Não há alunos cadastrados com os filtros selecionados. 
            Tente alterar os filtros ou adicione um novo aluno.
          </p>
        </div>
      )}
      
      <div className="mt-6">
        <Button asChild>
          <Link href="/alunos/novo">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><line x1="19" x2="19" y1="8" y2="14"></line><line x1="22" x2="16" y1="11" y2="11"></line></svg>
            Adicionar Aluno
          </Link>
        </Button>
      </div>
    </div>
  );
} 