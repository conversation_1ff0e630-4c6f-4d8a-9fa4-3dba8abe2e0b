import { ArrowRight, PanelTop } from 'lucide-react';
import Image from 'next/image';

export default function MigrationFlowDiagram() {
  return (
    <div className="inline-flex items-center gap-6 rounded-lg p-2">
      <div className="flex h-14 w-14 items-center justify-center rounded-md">
        <PanelTop className="h-8 w-8 text-muted-foreground" />
      </div>
      <ArrowRight className="h-7 w-7 text-muted-foreground" />
      <div className="flex h-14 w-14 items-center justify-center rounded-md border bg-white">
        <Image src="/logo2.svg" alt="ApexDojo Logo" width={28} height={28} />
      </div>
    </div>
  );
} 