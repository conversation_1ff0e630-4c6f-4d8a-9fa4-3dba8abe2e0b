'use client';

import { motion } from 'framer-motion';
import { X, Trash2, UserX, User<PERSON>heck, Refresh<PERSON>w, Crown, Building2, Loader2, Pause, Play } from 'lucide-react';

interface RowActionAnimationProps {
  action: 'delete' | 'delete-permanently' | 'inactivate' | 'activate' | 'change-belt' | 'change-branch' | 'generic' | 'pause' | 'resume';
  onAnimationComplete?: () => void;
  isVisible: boolean;
}

const actionConfig = {
  'delete': {
    icon: X,
    text: 'Excluindo...',
    bgColor: 'bg-orange-500/5 dark:bg-orange-500/10',
    iconColor: 'text-orange-500',
  },
  'delete-permanently': {
    icon: Trash2,
    text: 'Deletando...',
    bgColor: 'bg-red-500/5 dark:bg-red-500/10',
    iconColor: 'text-red-500',
  },
  'inactivate': {
    icon: UserX,
    text: 'Inativando...',
    bgColor: 'bg-yellow-500/5 dark:bg-yellow-500/10',
    iconColor: 'text-yellow-500',
  },
  'activate': {
    icon: UserCheck,
    text: 'Ativando...',
    bgColor: 'bg-green-500/5 dark:bg-green-500/10',
    iconColor: 'text-green-500',
  },
  'change-belt': {
    icon: Crown,
    text: 'Alterando faixa...',
    bgColor: 'bg-purple-500/5 dark:bg-purple-500/10',
    iconColor: 'text-purple-500',
  },
  'change-branch': {
    icon: Building2,
    text: 'Alterando filial...',
    bgColor: 'bg-blue-500/5 dark:bg-blue-500/10',
    iconColor: 'text-blue-500',
  },
  'generic': {
    icon: Loader2,
    text: 'Processando...',
    bgColor: 'bg-gray-500/5 dark:bg-gray-500/10',
    iconColor: 'text-gray-500',
  },
  'pause': {
    icon: Pause,
    text: 'Pausando...',
    bgColor: 'bg-orange-500/5 dark:bg-orange-500/10',
    iconColor: 'text-orange-500',
  },
  'resume': {
    icon: Play,
    text: 'Retomando...',
    bgColor: 'bg-green-500/5 dark:bg-green-500/10',
    iconColor: 'text-green-500',
  },
};

export const RowActionAnimation = ({ action, onAnimationComplete, isVisible }: RowActionAnimationProps) => {
  const config = actionConfig[action];
  const IconComponent = config.icon;

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onAnimationComplete={onAnimationComplete}
      className={`absolute inset-0 ${config.bgColor} backdrop-blur-sm flex items-center justify-center z-10`}
    >
      <div className="flex items-center gap-3">
        <motion.div
          initial={{ scale: 0.5, rotate: 0 }}
          animate={{
            scale: 1,
            rotate: action === 'delete' || action === 'delete-permanently' || action === 'inactivate' ? 360 : 0
          }}
          transition={{
            duration: 0.5,
            ease: "easeOut",
            rotate: {
              duration: 1,
              repeat: Infinity,
              ease: "linear"
            }
          }}
        >
          <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
        </motion.div>
        <motion.span
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className={`text-sm font-medium ${config.iconColor}`}
        >
          {config.text}
        </motion.span>
      </div>
    </motion.div>
  );
};

// Table-specific row action animation that renders as a table cell overlay
export const TableRowActionAnimation = ({ action, onAnimationComplete, isVisible, colSpan = 10 }: RowActionAnimationProps & { colSpan?: number }) => {
  const config = actionConfig[action];
  const IconComponent = config.icon;

  if (!isVisible) return null;

  return (
    <motion.td
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onAnimationComplete={onAnimationComplete}
      colSpan={colSpan}
      className={`${config.bgColor} backdrop-blur-sm relative z-10 h-16`}
    >
      <div className="flex items-center justify-center h-full">
        <div className="flex items-center gap-3">
          <motion.div
            initial={{ scale: 0.5, rotate: 0 }}
            animate={{
              scale: 1,
              rotate: action === 'delete' || action === 'delete-permanently' || action === 'inactivate' ? 360 : 0
            }}
            transition={{
              duration: 0.5,
              ease: "easeOut",
              rotate: {
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }
            }}
          >
            <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
          </motion.div>
          <motion.span
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className={`text-sm font-medium ${config.iconColor}`}
          >
            {config.text}
          </motion.span>
        </div>
      </div>
    </motion.td>
  );
};

// Row removal animation adapted for tables – renders a <tr> to respect HTML structure
export const RowRemovalAnimation = ({ onComplete, colSpan = 10 }: { onComplete: () => void; colSpan?: number }) => {
  return (
    <motion.tr
      initial={{ opacity: 1, height: 'auto' }}
      animate={{ opacity: 0, height: 0 }}
      exit={{ opacity: 0, height: 0 }}
      transition={{
        duration: 0.4,
        ease: "easeInOut",
        height: { delay: 0.1 },
      }}
      onAnimationComplete={onComplete}
      className="overflow-hidden"
    >
      {/* Colspan para abranger todas as colunas da tabela */}
      <td colSpan={colSpan} className="p-0"></td>
    </motion.tr>
  );
}; 