import { SettingsItem } from '../SettingsItem';

interface SectionItemBase {
  title: string;
  description: string;
}

type SectionItem = 
  | (SectionItemBase & { href: string; onClick?: never })
  | (SectionItemBase & { href?: never; onClick: () => void });

interface SettingsSectionProps {
  title: string;
  items: SectionItem[];
}

export function SettingsSection({ title, items }: SettingsSectionProps) {
  return (
    <section className="bg-card border border-border shadow-sm rounded-2xl overflow-hidden">
      <h2 className="px-6 py-4 text-lg font-medium">{title}</h2>
      <ul className="divide-y divide-border">
        {items.map((item) => (
          <SettingsItem key={item.title} {...item} />
        ))}
      </ul>
    </section>
  );
} 