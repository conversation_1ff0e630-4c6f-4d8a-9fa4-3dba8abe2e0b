import { Skeleton } from "@/components/ui/skeleton";

export default function NovoAlunoLoading() {
  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Skeleton className="h-6 w-20 mr-4" />
        <Skeleton className="h-8 w-40" />
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-3/4" />
        <div className="flex justify-end space-x-4 pt-4">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
    </div>
  );
}