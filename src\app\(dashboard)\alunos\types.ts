import { BeltColor } from "@/components/belt";
import { FinancialStatus } from "./server/financial-status";

// Interface do estudante
export interface Student {
  id: string; // ID da tabela users (para links de perfil)
  student_id: string; // ID da tabela students (para operações específicas)
  name: string;
  email: string;
  avatar: string | null;
  belt: BeltColor;
  role: string;
  createdAt: string;
  financialStatus: FinancialStatus | string;
  phone: string;
  branch: string;
  birthDate?: string;
  lastVisit?: string;
  degree?: number;
  // Dados completos da faixa da tabela belt_levels
  beltDetails?: {
    label?: string | null;
    stripe_color?: string | null;
    show_center_line?: boolean | null;
    center_line_color?: string | null;
  };
  // Status agora vem da tabela users através do join
  user?: {
    status: 'active' | 'inactive' | 'suspended';
  };
  // Informações do plano de matrícula
  enrollmentPlan?: {
    id: string;
    title: string;
    type: 'individual' | 'family' | 'corporate';
    status: 'active' | 'paused' | 'canceled' | 'expired';
  } | null;
}

// Interface para paginação
export interface PaginationResult {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

// Interface para parâmetros de busca/filtro
export interface FetchStudentsParams {
  page: number;
  pageSize: number;
  query?: string;
  status?: "active" | "inactive" | "all";
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
  startDate?: Date;
  endDate?: Date;
}

// Interface para o estado de filtros
export interface FilterState {
  search?: string;
  belt?: string[];
  status?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

// Interface para representar uma filial
export interface Branch {
  id: string;
  name: string;
  address?: string;
  active?: boolean;
}

// Interface para parâmetros de filtro usados no fetchStudents
export interface FilterParams {
  skip?: number;
  take?: number;
  search?: string;
  status?: string[];
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
}

