'use server';

import { requireAuth } from '@/services/auth/actions/auth-actions';
import { listGraduationLevels } from '@/services/belts/levels';
import { getRankRequirements } from '@/services/belts/requirements';
import { getModalitySettings } from '@/services/modalities/settings';
import { type ModalitySettings } from '@/services/modalities/settings';
import { type GraduationLevel } from '@/services/belts/levels';
import { type BeltLevelRequirements } from '@/services/belts/requirements';

interface GraduationLevelWithRequirements extends GraduationLevel {
  requirements?: BeltLevelRequirements;
  stripe_color?: string | null;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

interface RefetchResult {
  success: boolean;
  modalitySettings?: ModalitySettings | null;
  levelsWithRequirements?: GraduationLevelWithRequirements[];
  error?: string;
}

export async function refetchModalityDataAction(
  modalityId: string,
  modalitySlug: string
): Promise<RefetchResult> {
  try {
    const { user } = await requireAuth();
    const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
    const role: string | undefined = (user.app_metadata as any)?.role;

    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não identificado',
      };
    }

    if (role !== 'admin') {
      return {
        success: false,
        error: 'Acesso negado',
      };
    }

    console.log('🔄 Refetching modality data via server action...');

    // Buscar configurações da modalidade
    const modalitySettings = await getModalitySettings(tenantId, modalityId);

    // Buscar níveis de graduação da modalidade
    const graduationLevels = await listGraduationLevels(tenantId, modalitySlug);

    // Buscar requisitos dos níveis (filtrar IDs válidos)
    const beltLevelIds = graduationLevels
      .map(level => level.id)
      .filter(id => id && id.trim() !== ''); // Filtrar IDs vazios ou inválidos
    
    console.log('🔍 Belt level IDs para buscar requirements:', beltLevelIds);
    
    const rankRequirements = beltLevelIds.length > 0 
      ? await getRankRequirements(tenantId, beltLevelIds)
      : [];

    // Combinar dados
    const levelsWithRequirements = graduationLevels.map(level => ({
      ...level,
      requirements: rankRequirements.find(req => req.belt_level_id === level.id),
    }));

    console.log('✅ Modality data refetched successfully via server action');

    return {
      success: true,
      modalitySettings,
      levelsWithRequirements,
    };
  } catch (error) {
    console.error('❌ Error refetching modality data:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}
