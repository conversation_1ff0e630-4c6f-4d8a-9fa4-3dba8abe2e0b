# Funções de Criação de Cobranças (Tabela Payments)

Este documento lista todas as funções relacionadas à criação de cobranças na tabela `payments` do banco de dados, incluindo server actions, serviços e funções RPC.

## 1. Server Actions (payment-actions.ts)

### 1.1 Cobrança Manual
```typescript
export async function createManualPayment(data: unknown): Promise<ActionResult>
```
- **Descrição**: Cria uma cobrança manual para um estudante
- **Validação**: `criarCobrancaManualSchema`
- **Serviço**: `paymentService.createManualPayment()`
- **RPC**: `create_manual_payment`

### 1.2 Taxa de Graduação
```typescript
export async function createGraduationFeePayment(data: unknown): Promise<ActionResult>
```
- **Descrição**: Cria cobrança de taxa de graduação baseada na configuração da faixa
- **Validação**: `criarTaxaGraduacaoSchema`
- **Serviço**: `paymentService.createGraduationFeePayment()`
- **RPC**: `create_graduation_fee_payment`

### 1.3 Taxa de Inscrição
```typescript
export async function createSignupFeePayment(data: unknown): Promise<ActionResult>
```
- **Descrição**: Cria cobrança de taxa de inscrição para uma matrícula
- **Validação**: `criarTaxaInscricaoSchema`
- **Serviço**: `paymentService.createSignupFeePayment()`
- **RPC**: `create_signup_fee_payment`

### 1.4 Taxa de Cancelamento
```typescript
export async function createCancellationFeePayment(data: unknown): Promise<ActionResult>
```
- **Descrição**: Cria cobrança de taxa de cancelamento baseada na configuração do plano
- **Validação**: `criarTaxaCancelamentoSchema`
- **Serviço**: `paymentService.createCancellationFeePayment()`
- **RPC**: `create_cancellation_fee_payment`

## 2. Serviços (payment-service.ts)

### 2.1 PaymentService.createManualPayment()
```typescript
async createManualPayment(data: CreateManualPaymentData): Promise<PaymentRPCResult>
```
- **Parâmetros**: `alunoId`, `valor`, `descricao`, `dataVencimento`, `metadata`
- **RPC**: `create_manual_payment`

### 2.2 PaymentService.createGraduationFeePayment()
```typescript
async createGraduationFeePayment(data: CreateGraduationFeeData): Promise<PaymentRPCResult>
```
- **Parâmetros**: `alunoId`, `beltLevelId`, `graduationId`
- **RPC**: `create_graduation_fee_payment`

### 2.3 PaymentService.createSignupFeePayment()
```typescript
async createSignupFeePayment(data: CreateSignupFeeData): Promise<PaymentRPCResult>
```
- **Parâmetros**: `membershipId`
- **RPC**: `create_signup_fee_payment`

### 2.4 PaymentService.createInitialPayment()
```typescript
async createInitialPayment(data: CreateSignupFeeData): Promise<PaymentRPCResult>
```
- **Parâmetros**: `membershipId`
- **RPC**: `create_initial_payment`
- **Uso**: Para planos one-time

### 2.5 PaymentService.createRecurringPayments()
```typescript
async createRecurringPayments(data: { membershipId: string; paymentsCount?: number }): Promise<PaymentRPCResult>
```
- **Parâmetros**: `membershipId`, `paymentsCount` (opcional, default: 1)
- **RPC**: `create_recurring_payments`
- **Uso**: Para planos recorrentes

### 2.6 PaymentService.createCancellationFeePayment()
```typescript
async createCancellationFeePayment(data: CreateCancellationFeeData): Promise<PaymentRPCResult>
```
- **Parâmetros**: `membershipId`, `motivo`
- **Implementação**: Busca taxa no plano e cria pagamento diretamente na tabela

## 3. Membership Actions (membership-actions.ts)

### 3.1 createMembership()
```typescript
export async function createMembership(data: unknown, tenantId?: string): Promise<ActionResult>
```
- **Criação de Pagamentos**: Automática após criar matrícula
- **Planos Recorrentes**: Usa `create_recurring_payments` RPC
- **Planos One-time**: Usa `create_initial_payment` RPC
- **Fallback**: Usa `paymentService.createRecurringPayments()` ou `paymentService.createInitialPayment()`

### 3.2 cancelMembership()
```typescript
export async function cancelMembership(data: unknown, tenantId?: string): Promise<ActionResult>
```
- **Cancelamento de Pagamentos**: Usa `cancel_membership_payments` RPC
- **Taxa de Cancelamento**: Usa `create_cancellation_fee_payment` RPC (se configurada)

## 4. Funções RPC do Banco de Dados

### 4.1 create_manual_payment
```sql
create_manual_payment(
  p_tenant_id uuid, 
  p_student_id uuid, 
  p_amount numeric, 
  p_description text, 
  p_due_date date DEFAULT CURRENT_DATE, 
  p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS jsonb
```
- **Tipo**: `manual`
- **Status**: `pending`

### 4.2 create_graduation_fee_payment
```sql
create_graduation_fee_payment(
  p_tenant_id uuid, 
  p_student_id uuid, 
  p_belt_level_id uuid, 
  p_graduation_id uuid
) RETURNS jsonb
```
- **Tipo**: `graduation_fee`
- **Status**: `pending`
- **Valor**: Baseado na configuração da faixa (`promotion_fee`)

### 4.3 create_signup_fee_payment
```sql
create_signup_fee_payment(p_membership_id uuid) RETURNS jsonb
```
- **Tipo**: `signup_fee`
- **Status**: `pending`
- **Valor**: Baseado na configuração do plano (`taxaInscricao`)

### 4.4 create_initial_payment
```sql
create_initial_payment(p_membership_id uuid) RETURNS jsonb
```
- **Tipo**: `initial_payment`
- **Status**: `pending`
- **Valor**: Valor do plano + taxa de inscrição (se configurada)
- **Uso**: Planos one-time

### 4.5 create_recurring_payments
```sql
create_recurring_payments(
  p_membership_id uuid, 
  p_payments_count integer DEFAULT 1
) RETURNS jsonb
```
- **Tipo**: `recurring`
- **Status**: `pending`
- **Valor**: Baseado na configuração do plano
- **Frequência**: Baseada na configuração do plano (`frequency`)
- **Uso**: Planos recorrentes

### 4.6 create_cancellation_fee_payment
```sql
create_cancellation_fee_payment(
  p_membership_id uuid, 
  p_reason text DEFAULT NULL::text
) RETURNS jsonb
```
- **Tipo**: `cancellation_fee`
- **Status**: `pending`
- **Valor**: Baseado na configuração do plano (`taxaCancelamento`)

### 4.7 cancel_membership_payments
```sql
cancel_membership_payments(
  p_membership_id uuid, 
  p_reason text DEFAULT 'Membership canceled'::text
) RETURNS jsonb
```
- **Ação**: Cancela todos os pagamentos pendentes de uma matrícula
- **Status**: Altera de `pending` para `canceled`

### 4.8 create_next_recurring_payment
```sql
create_next_recurring_payment(p_paid_payment_id uuid) RETURNS jsonb
```
- **Trigger**: Automático quando pagamento recorrente é marcado como `paid`
- **Tipo**: `recurring`
- **Status**: `pending`
- **Data**: Calculada baseada na frequência do plano

### 4.9 process_membership_billing
```sql
process_membership_billing(p_membership_id uuid) RETURNS jsonb
```
- **Uso**: Processamento de cobrança recorrente
- **Tipo**: `recurring`
- **Status**: `pending`
- **Atualiza**: `next_billing_date` da matrícula

## 5. Processamento Automático

### 5.1 Triggers
- **trigger_auto_create_next_recurring_payment**: Cria próximo pagamento quando recorrente é pago
- **update_payment_overdue_date**: Atualiza data de vencimento quando status muda para `overdue`

### 5.2 Funções de Processamento em Lote
- **process_all_recurring_billing**: Processa todas as cobranças recorrentes pendentes
- **process_overdue_payments**: Processa pagamentos em atraso e aplica multas
- **mark_overdue_payments**: Marca pagamentos vencidos como `overdue`

## 6. Tipos de Pagamento Criados

1. **manual**: Cobranças manuais criadas pelo admin
2. **graduation_fee**: Taxas de graduação
3. **signup_fee**: Taxas de inscrição
4. **initial_payment**: Pagamento inicial para planos one-time
5. **recurring**: Mensalidades recorrentes
6. **cancellation_fee**: Taxas de cancelamento
7. **late_fee**: Multas por atraso (criadas automaticamente)

## 7. Status dos Pagamentos

- **pending**: Aguardando pagamento
- **paid**: Pago
- **awaiting_confirmation**: Aguardando confirmação do admin
- **overdue**: Em atraso
- **failed**: Falhou
- **canceled**: Cancelado

## 8. Fluxo de Criação de Pagamentos

### 8.1 Ao Criar Matrícula
1. Verifica tipo do plano (`pricing_config.type`)
2. Se `recurring`: Chama `create_recurring_payments`
3. Se `one-time`: Chama `create_initial_payment`
4. Inclui taxa de inscrição se configurada

### 8.2 Ao Cancelar Matrícula
1. Cancela pagamentos pendentes: `cancel_membership_payments`
2. Aplica taxa de cancelamento se configurada: `create_cancellation_fee_payment`

### 8.3 Processamento Recorrente
1. Pagamento marcado como `paid`
2. Trigger cria próximo pagamento automaticamente
3. Atualiza `next_billing_date` da matrícula

Este documento serve como referência completa para todas as funções relacionadas à criação de cobranças no sistema.
