'use client';

import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface RefreshIndicatorProps {
  className?: string;
}

export function RefreshIndicator({ className }: RefreshIndicatorProps) {
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    try {
      router.refresh();
      
      // Simulamos um pequeno delay para dar feedback visual
      await new Promise(resolve => setTimeout(resolve, 500));
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={cn("gap-2", className)}
      aria-label="Atualizar lista"
    >
      <RefreshCw 
        className={cn(
          "h-4 w-4 transition-transform", 
          isRefreshing && "animate-spin"
        )} 
      />
      {isRefreshing ? 'Atualizando...' : 'Atualizar'}
    </Button>
  );
} 