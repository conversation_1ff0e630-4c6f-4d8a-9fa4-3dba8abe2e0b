import { z } from 'zod';
import { ClassStatusEnum } from '../../../turmas/actions/schemas/enums';
import { PaginationSchema, SortOrderSchema, SearchFilterSchema, DateRangeFilterSchema, validateDateRange } from '../../../turmas/actions/schemas/shared';
import { datetimeLocalToBrasiliaISO } from '@/utils/timezone-utils';

// Helper para transformar datetime-local em ISO string considerando timezone de Brasília
const datetimeLocalToISO = datetimeLocalToBrasiliaISO;

// Helper para tratar max_capacity (converte NaN em undefined)
const numberOrUndefined = z.union([
  z.number().min(1).max(100),
  z.nan().transform(() => undefined),
  z.undefined()
]).optional();

// Schema base para Class
const BaseClassSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  branch_id: z.string().uuid(),
  instructor_id: z.string().uuid(),
  class_group_id: z.string().uuid().optional(),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100),
  description: z.string().max(500).nullish().transform(val => val || undefined),
  start_time: z.string().transform(datetimeLocalToISO).pipe(z.string().datetime()),
  end_time: z.string().transform(datetimeLocalToISO).pipe(z.string().datetime()),
  max_capacity: numberOrUndefined,
  recurring: z.boolean().default(false),
  recurrence_pattern: z.string().optional(),
  status: ClassStatusEnum.default('scheduled'),
  attendance_recorded: z.boolean().default(false),
  notes: z.string().max(1000).nullish().transform(val => val || undefined),
  metadata: z.record(z.any()).optional(),
});

// Schema completo com validação de horário
export const ClassSchema = BaseClassSchema.refine(data => {
  return new Date(data.end_time) > new Date(data.start_time);
}, {
  message: 'Horário final deve ser posterior ao horário inicial',
  path: ['end_time']
});

// Schema para criação
export const CreateClassSchema = BaseClassSchema.omit({ 
  id: true,
  tenant_id: true 
}).refine(data => {
  return new Date(data.end_time) > new Date(data.start_time);
}, {
  message: 'Horário final deve ser posterior ao horário inicial',
  path: ['end_time']
});

// Schema para atualização
export const UpdateClassSchema = BaseClassSchema.partial().omit({ 
  id: true,
  tenant_id: true 
});

// Schema para criação em lote (múltiplas aulas)
export const CreateBulkClassesSchema = z.object({
  branch_id: z.string().uuid(),
  instructor_id: z.string().uuid(),
  class_group_id: z.string().uuid().optional(),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100),
  description: z.string().max(500).nullish().transform(val => val || undefined),
  duration_minutes: z.number().min(15).max(480), // 15 min a 8 horas
  max_capacity: numberOrUndefined,
  dates_and_times: z.array(z.object({
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Data deve estar no formato YYYY-MM-DD'),
    start_time: z.string().regex(/^\d{2}:\d{2}$/, 'Hora deve estar no formato HH:MM'),
  })).min(1, 'Selecione pelo menos uma data e horário'),
  notes: z.string().max(1000).nullish().transform(val => val || undefined),
});

// Schema para cancelar aula
export const CancelClassSchema = z.object({
  id: z.string().uuid({ message: 'ID da aula é obrigatório' }),
  reason: z.string().max(500).optional(),
  notify_students: z.boolean().default(true),
});

// Schema para reagendar aula
export const RescheduleClassSchema = z.object({
  id: z.string().uuid({ message: 'ID da aula é obrigatório' }),
  new_start_time: z.string().transform(datetimeLocalToISO).pipe(z.string().datetime()),
  new_end_time: z.string().transform(datetimeLocalToISO).pipe(z.string().datetime()),
  reason: z.string().max(500).optional(),
  notify_students: z.boolean().default(true),
}).refine(data => {
  return new Date(data.new_end_time) > new Date(data.new_start_time);
}, {
  message: 'Novo horário final deve ser posterior ao horário inicial',
  path: ['new_end_time']
});

// Schema para filtros de aulas
export const ClassFilterSchema = SearchFilterSchema.extend({
  instructor_id: z.string().uuid().optional(),
  branch_id: z.string().uuid().optional(),
  class_group_id: z.string().uuid().nullable().optional(),
  status: ClassStatusEnum.optional(),
  attendance_recorded: z.boolean().optional(),
  // Filtro especial para permissões de instrutor (processado internamente)
  _instructorFilter: z.object({
    instructorId: z.string().uuid(),
    classGroupIds: z.array(z.string().uuid())
  }).optional(),
}).merge(DateRangeFilterSchema).merge(PaginationSchema).extend({
  sort_by: z.enum(['name', 'start_time', 'created_at', 'instructor_name']).default('start_time'),
  sort_order: SortOrderSchema,
});

// Schema para estatísticas de aulas
export const ClassStatsRequestSchema = z.object({
  instructor_id: z.string().uuid().optional(),
  class_group_id: z.string().uuid().optional(),
  period_start: z.string().datetime().optional(),
  period_end: z.string().datetime().optional(),
});

export const ClassStatsSchema = z.object({
  total_classes: z.number(),
  classes_this_week: z.number(),
  completed_classes: z.number(),
  cancelled_classes: z.number(),
  average_attendance: z.number(),
  classes_by_status: z.record(z.number()),
});

// Tipos inferidos
export type Class = z.infer<typeof ClassSchema>;
export type CreateClass = z.infer<typeof CreateClassSchema>;
export type UpdateClass = z.infer<typeof UpdateClassSchema>;
export type CreateBulkClasses = z.infer<typeof CreateBulkClassesSchema>;
export type CancelClass = z.infer<typeof CancelClassSchema>;
export type RescheduleClass = z.infer<typeof RescheduleClassSchema>;
export type ClassFilter = z.infer<typeof ClassFilterSchema>;
export type ClassStatsRequest = z.infer<typeof ClassStatsRequestSchema>;
export type ClassStats = z.infer<typeof ClassStatsSchema>; 