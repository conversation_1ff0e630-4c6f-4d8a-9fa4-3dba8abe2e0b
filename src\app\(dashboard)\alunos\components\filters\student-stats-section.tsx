'use client'

import { useStudentStats } from '../../hooks/use-student-stats'
import { StudentStats } from '../../actions/student-stats'
import { Skeleton } from '@/components/ui/skeleton'

interface StudentStatsSectionProps {
  initialStats?: StudentStats
}

export function StudentStatsSection({ initialStats }: StudentStatsSectionProps) {
  const { stats, isLoading, error } = useStudentStats(initialStats)

  if (error) {
    return (
      <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="rounded-md border bg-destructive/10 p-3">
          <div className="text-sm text-destructive">Erro ao carregar estatísticas</div>
          <div className="text-xs text-muted-foreground mt-1">Tente recarregar a página</div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Total de Alunos</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold">{stats.total}</div>
        )}
      </div>
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Ativos</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold text-primary">{stats.active}</div>
        )}
      </div>
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Inativos</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold text-muted-foreground">{stats.inactive}</div>
        )}
      </div>
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Atrasados</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold text-destructive">{stats.pending}</div>
        )}
      </div>
    </div>
  )
} 