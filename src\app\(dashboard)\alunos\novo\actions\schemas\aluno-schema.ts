import { z } from "zod";

export const novoAlunoSchema = z.object({
  full_name: z.string().min(1, "Nome completo é obrigatório"),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string().email("Email inválido"),
  phone: z.string().min(1, "Telefone é obrigatório"),
  avatar_url: z.string().optional(),
  
  branch_id: z.string().uuid("ID de filial inválido"),
  birth_date: z.string().optional().refine(data => !data || /^\d{4}-\d{2}-\d{2}$/.test(data), {
    message: "Data deve estar no formato YYYY-MM-DD"
  }),
  gender: z.enum(["masculino", "feminino", "outro", "prefiro_nao_informar"]).optional(),
  
  street: z.string().optional(),
  street_number: z.string().optional(),
  complement: z.string().optional(),
  neighborhood: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
  
  emergency_contact_name: z.string().optional(),
  emergency_contact_phone: z.string().optional(),
  emergency_contact_relationship: z.string().optional(),
  
  health_notes: z.string().optional(),
  allergies: z.string().optional(),
  medical_conditions: z.string().optional(),
  medications: z.string().optional(),
});

export type NovoAlunoFormValues = z.infer<typeof novoAlunoSchema>; 