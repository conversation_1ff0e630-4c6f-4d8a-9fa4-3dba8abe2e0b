"use client"

import { FilterPopover } from "./filter-popover"
import { motion, AnimatePresence } from "framer-motion"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { X, ChevronUp, ChevronDown, RefreshCcw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"
import { Branch, FilterState } from "../../types"
import { StudentStatsSection } from "./student-stats-section"
import { StudentStats } from "../../actions/student-stats"
import { useStudentsCache } from "../../hooks/use-students-cache"
import { useIsFetching } from "@tanstack/react-query"

interface ActiveFilter {
  id: string
  type: 'belt' | 'status' | 'financialStatus' | 'date' | 'branch' | 'enrollmentStatus'
  label: string
  value: string
}

interface StudentFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  branches: Branch[];
  initialStats?: StudentStats;
}

const filterLabels = {
  belt: {
    white: "Faixa Branca",
    blue: "Faixa Azul",
    purple: "Faixa Roxa",
    brown: "Faixa Marrom",
    black: "Faixa Preta",
  },
  status: {
    active: "Ativo",
    inactive: "Inativo",
  },
  financialStatus: {
    up_to_date: "Em dia",
    pending: "Pendente",
    overdue: "Atrasado",
  },
  enrollmentStatus: {
    active: "Ativo",
    inactive: "Inativo",
    paused: "Pausado",
  },
  branch: {} as Record<string, string>,
}

const getBeltColorClass = (belt: string) => {
  switch (belt) {
    case 'white':
      return 'bg-white text-black hover:bg-white/90'
    case 'blue':
      return 'bg-blue-500 text-white hover:bg-blue-600'
    case 'purple':
      return 'bg-purple-500 text-white hover:bg-purple-600'
    case 'brown':
      return 'bg-amber-800 text-white hover:bg-amber-900'
    case 'black':
      return 'bg-black text-white hover:bg-black/90'
    default:
      return 'bg-secondary hover:bg-secondary/80'
  }
}

export function FilterBar({ filters, onFilterChange, branches, initialStats }: StudentFiltersProps) {
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [isFilterBarVisible, setIsFilterBarVisible] = useState(false)
  const { forceRefresh } = useStudentsCache();
  const isFetching = useIsFetching({ queryKey: ['students'] }) > 0;
  
  useEffect(() => {
    if (branches.length > 0) {
      const branchLabels = branches.reduce((acc, branch) => {
        acc[branch.id] = branch.name;
        return acc;
      }, {} as Record<string, string>);
      
      filterLabels.branch = branchLabels;
    }
  }, [branches]);

  // Sincronizar activeFilters quando filters prop mudar
  useEffect(() => {
    const newActiveFilters: ActiveFilter[] = []

    if (filters.belt?.length) {
      filters.belt.forEach(belt => {
        newActiveFilters.push({
          id: `belt-${belt}`,
          type: 'belt',
          label: 'Faixa',
          value: filterLabels.belt[belt as keyof typeof filterLabels.belt],
        })
      })
    }

    if (filters.status?.length) {
      filters.status.forEach(status => {
        newActiveFilters.push({
          id: `status-${status}`,
          type: 'status',
          label: 'Status',
          value: filterLabels.status[status as keyof typeof filterLabels.status],
        })
      })
    }

    if (filters.financialStatus?.length) {
      filters.financialStatus.forEach(status => {
        newActiveFilters.push({
          id: `financialStatus-${status}`,
          type: 'financialStatus',
          label: 'Situação',
          value: filterLabels.financialStatus[status as keyof typeof filterLabels.financialStatus],
        })
      })
    }

    if (filters.branch?.length) {
      filters.branch.forEach(branch => {
        newActiveFilters.push({
          id: `branch-${branch}`,
          type: 'branch',
          label: 'Filial',
          value: filterLabels.branch[branch],
        })
      })
    }

    if (filters.enrollmentStatus?.length) {
      filters.enrollmentStatus.forEach(status => {
        newActiveFilters.push({
          id: `enrollmentStatus-${status}`,
          type: 'enrollmentStatus',
          label: 'Status de Matrícula',
          value: filterLabels.enrollmentStatus[status as keyof typeof filterLabels.enrollmentStatus],
        })
      })
    }

    if (filters.startDate || filters.endDate) {
      const dateLabel = filters.startDate && filters.endDate
        ? `${filters.startDate.toLocaleDateString()} - ${filters.endDate.toLocaleDateString()}`
        : filters.startDate
          ? `A partir de ${filters.startDate.toLocaleDateString()}`
          : `Até ${filters.endDate!.toLocaleDateString()}`

      newActiveFilters.push({
        id: 'date-range',
        type: 'date',
        label: 'Período',
        value: dateLabel,
      })
    }

    setActiveFilters(newActiveFilters)
  }, [filters, filterLabels.branch]);

  const handleRemoveFilter = (filterId: string) => {
    const [type, value] = filterId.split('-')
    
    setActiveFilters(prev => prev.filter(f => f.id !== filterId))
    
    // Update filter state
    if (type === 'date') {
      onFilterChange({
        ...filters,
        startDate: undefined,
        endDate: undefined,
      })
    } else {
      onFilterChange({
        ...filters,
        [type]: Array.isArray(filters[type as keyof FilterState])
          ? (filters[type as keyof FilterState] as string[])?.filter(v => v !== value)
          : undefined,
      })
    }
  }

  const handleFilterChange = (newFilters: FilterState) => {
    onFilterChange(newFilters)
  }

  const handleClearAll = () => {
    setActiveFilters([])
    onFilterChange({})
  }

  const toggleFilterBar = () => {
    setIsFilterBarVisible(prev => !prev)
  }

  return (
    <div className="rounded-lg border bg-card">
      <div className="p-2 flex justify-between items-center">
        <div className="flex items-center">
          <FilterPopover 
            filters={filters}
            onFilterChange={handleFilterChange}
            branches={branches}
          />
          
          {activeFilters.length > 0 && (
            <div className="ml-2 flex flex-wrap gap-1">
              <AnimatePresence>
                {activeFilters.map((filter) => (
                  <motion.div
                    key={filter.id}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.15 }}
                  >
                    <Badge 
                      variant="secondary"
                      className={cn(
                        "pl-2 h-7 gap-1 pr-1 transition-colors",
                        filter.type === 'belt' && getBeltColorClass(filter.id.split('-')[1]),
                        filter.type === 'status' && "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/40 dark:text-green-200 dark:border-green-700",
                        filter.type === 'financialStatus' && "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/40 dark:text-red-200 dark:border-red-700",
                        filter.type === 'enrollmentStatus' && "bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/40 dark:text-orange-200 dark:border-orange-700",
                        filter.type === 'date' && "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/40 dark:text-blue-200 dark:border-blue-600",
                        filter.type === 'branch' && "bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/40 dark:text-purple-200 dark:border-purple-600"
                      )}
                    >
                      <span className="text-xs font-medium opacity-70">
                        {filter.label}:
                      </span>
                      <span className="text-xs font-medium">
                        {filter.value}
                      </span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 hover:bg-secondary/20 rounded-full"
                        onClick={() => handleRemoveFilter(filter.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </motion.div>
                ))}
              </AnimatePresence>
              {activeFilters.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground"
                  onClick={handleClearAll}
                >
                  Limpar todos
                </Button>
              )}
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {/* Refresh Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => forceRefresh()}
            disabled={isFetching}
            className="h-8 w-8 p-0"
          >
            <RefreshCcw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
          </Button>

          {/* Toggle stats button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFilterBar}
            className="h-8 w-8 p-0"
          >
            {isFilterBarVisible ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
      
      <AnimatePresence>
        {isFilterBarVisible && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <Separator className="bg-border" />
            <StudentStatsSection initialStats={initialStats} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 
