import { Suspense } from 'react';
import { Metadata } from 'next';
import { getClasses, getInstructorsForForm, getBranchesForForm } from '../actions';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Calendar, Grid, List } from 'lucide-react';
import Link from 'next/link';
import { ClassCard } from '../components/classes/ClassCard';
import { ClassesTable } from '../components/classes/ClassesTable';
import { ClassFilters } from '../components/classes/ClassFilters';
import { checkAdminPermission } from '@/services/permissions/utils/role-verification';

export const metadata: Metadata = {
  title: 'Aulas Livres | Apex SaaS',
  description: 'Visualize e gerencie aulas livres disponíveis para inscrição',
};

interface PageProps {
  searchParams: Promise<{
    status?: string;
    instructor_id?: string;
    branch_id?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
    page?: string;
    limit?: string;
    view?: 'grid' | 'table';
  }>;
}



async function FreeClasses({ searchParams }: { searchParams: Awaited<PageProps['searchParams']> }) {
  // Verificar permissões antes de carregar dados
  await checkAdminPermission();
  
  // Buscar apenas aulas livres (sem class_group_id)
  const classFilters = {
    ...searchParams,
    class_group_id: null // Filtrar por aulas sem turma (aulas livres)
  };

  const [classesResult, instructorsResult, branchesResult] = await Promise.all([
    getClasses(classFilters),
    getInstructorsForForm(),
    getBranchesForForm()
  ]);

  const classes = classesResult.success && classesResult.data ? (Array.isArray(classesResult.data) ? classesResult.data : classesResult.data.data) : [];
  const instructors = instructorsResult.success ? (instructorsResult.data || []).map(instructor => ({
    id: instructor.id,
    name: instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()
  })) : [];
  const branches = branchesResult.success ? (branchesResult.data || []).map(branch => ({
    id: branch.id,
    name: branch.name
  })) : [];

  // Estatísticas das aulas livres
  const totalClasses = classes.length;
  const scheduledClasses = classes.filter(c => c.status === 'scheduled').length;
  const completedClasses = classes.filter(c => c.status === 'completed').length;
  const totalAttendance = classes.reduce((sum, c) => sum + (c._count?.attendance || 0), 0);

  const viewMode = searchParams.view || 'grid';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        {/* <div>
          <h1 className="text-2xl font-bold text-foreground">
            Aulas Livres
          </h1>
          <p className="text-muted-foreground">
            Gerencie e participe de aulas não vinculadas a turmas específicas
          </p>
        </div> */}
        <div className="flex space-x-2 ml-auto">
          {/* <Button variant="outline" asChild>
            <Link href="/aulas/calendario">
              <Calendar className="h-4 w-4 mr-2" />
              Ver Calendário
            </Link>
          </Button> */}
          <Button asChild>
            <Link href="/aulas/livres/nova">
              <Plus className="h-4 w-4 mr-2" />
              Nova Aula Livre
            </Link>
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-500">{totalClasses}</div>
              <div className="text-sm text-muted-foreground">Total de aulas</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-500">
                {scheduledClasses}
              </div>
              <div className="text-sm text-muted-foreground">Próximas aulas</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-500">
                {completedClasses}
              </div>
              <div className="text-sm text-muted-foreground">Aulas concluídas</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-500">
                {classes.filter(c => c.max_capacity !== null).length}
              </div>
              <div className="text-sm text-muted-foreground">Com capacidade limitada</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <ClassFilters 
        instructors={instructors}
        branches={branches}
        classGroups={[]} // Não mostrar filtro de turma para aulas livres
        showGroupFilter={false}
      />

      {/* Controles de visualização */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            {totalClasses} aula{totalClasses !== 1 ? 's' : ''} encontrada{totalClasses !== 1 ? 's' : ''}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            asChild
          >
            <Link href={`?${new URLSearchParams({ ...searchParams, view: 'grid' }).toString()}`}>
              <Grid className="h-4 w-4" />
            </Link>
          </Button>
          <Button
            variant={viewMode === 'table' ? 'default' : 'outline'}
            size="sm"
            asChild
          >
            <Link href={`?${new URLSearchParams({ ...searchParams, view: 'table' }).toString()}`}>
              <List className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>

      {/* Lista de aulas */}
      {classes.length > 0 ? (
        viewMode === 'grid' ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {classes.map((classItem) => (
              <ClassCard
                key={classItem.id}
                class={classItem}
                showGroupInfo={false} // Não mostrar info de turma para aulas livres
              />
            ))}
          </div>
        ) : (
          <ClassesTable
            classes={classes}
            showGroupInfo={false} // Não mostrar coluna de turma para aulas livres
          />
        )
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Nenhuma aula livre encontrada
            </h3>
            <p className="text-muted-foreground mb-4">
              {Object.keys(searchParams).some(key => searchParams[key as keyof typeof searchParams] && key !== 'view')
                ? 'Não há aulas livres que correspondam aos filtros aplicados. Tente ajustar os critérios de busca.'
                : 'Ainda não há aulas livres cadastradas no sistema.'
              }
            </p>
            <Button asChild>
              <Link href="/aulas/livres/nova">
                <Plus className="h-4 w-4 mr-2" />
                Criar primeira aula livre
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default async function FreeClassesPage({ searchParams }: PageProps) {
  const resolvedSearchParams = await searchParams;

  return (
    <Suspense fallback={<div>Carregando aulas livres...</div>}>
      <FreeClasses searchParams={resolvedSearchParams} />
    </Suspense>
  );
} 