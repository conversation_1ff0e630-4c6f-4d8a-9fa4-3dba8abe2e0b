'use client';

import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserIcon, AlertTriangleIcon, CheckCircleIcon, PlusIcon, EditIcon, PhoneIcon, MailIcon } from 'lucide-react';
import { UserWithGuardianInfo } from '@/src/types/guardian';
import { GuardianForm } from '@/app/(dashboard)/perfil/[userId]/components/GuardianForm';
import { formatPhoneNumberDisplay } from '@/utils/phone-utils';
import { formatGuardianRelationship } from '@/utils/guardian-utils';
import Link from 'next/link';

interface GuardianManagementListProps {
  initialMinorsWithoutGuardian: UserWithGuardianInfo[];
  initialMinorsWithGuardian: UserWithGuardianInfo[];
}

export function GuardianManagementList({ 
  initialMinorsWithoutGuardian, 
  initialMinorsWithGuardian 
}: GuardianManagementListProps) {
  const [minorsWithoutGuardian, setMinorsWithoutGuardian] = useState(initialMinorsWithoutGuardian);
  const [minorsWithGuardian, setMinorsWithGuardian] = useState(initialMinorsWithGuardian);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [editingType, setEditingType] = useState<'setup' | 'edit' | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSetupGuardian = (userId: string) => {
    setEditingUserId(userId);
    setEditingType('setup');
  };

  const handleEditGuardian = (userId: string) => {
    setEditingUserId(userId);
    setEditingType('edit');
  };

  const handleCancelEdit = () => {
    setEditingUserId(null);
    setEditingType(null);
  };

  const handleGuardianSetupSuccess = (userId: string) => {
    setEditingUserId(null);
    setEditingType(null);
    // Mover o menor da lista "sem responsável" para "com responsável"
    const minor = minorsWithoutGuardian.find(m => m.id === userId);
    if (minor) {
      setMinorsWithoutGuardian(prev => prev.filter(m => m.id !== userId));
      // Recarregar dados para obter informações atualizadas do responsável
      window.location.reload();
    }
  };

  const handleGuardianEditSuccess = (userId: string) => {
    setEditingUserId(null);
    setEditingType(null);
    // Recarregar dados para obter informações atualizadas
    window.location.reload();
  };

  const getAgeFromBirthDate = (birthDate: string | null): number | null => {
    if (!birthDate) return null;
    
    const birth = new Date(birthDate);
    const today = new Date();
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1;
    }
    return age;
  };

  const isEditing = (userId: string) => editingUserId === userId;

  // Se não há nenhum menor (nem com nem sem responsável)
  if (minorsWithoutGuardian.length === 0 && minorsWithGuardian.length === 0) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <CheckCircleIcon className="h-12 w-12 text-green-600 dark:text-green-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Nenhum estudante menor de idade encontrado
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Não há estudantes menores de 18 anos cadastrados no momento.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Seção: Menores SEM Responsável */}
      {minorsWithoutGuardian.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Menores sem Responsável Configurado ({minorsWithoutGuardian.length})
            </h2>
          </div>

          <Alert variant="destructive">
            <AlertTriangleIcon className="h-4 w-4" />
            <AlertDescription>
              <strong>Atenção:</strong> Os estudantes listados abaixo são menores de idade e precisam 
              ter um responsável legal configurado para usar a plataforma adequadamente.
            </AlertDescription>
          </Alert>

          <div className="grid gap-4">
            {minorsWithoutGuardian.map((minor) => {
              const age = getAgeFromBirthDate(minor.birth_date || null);
              const isEditingThis = isEditing(minor.id) && editingType === 'setup';

              if (isEditingThis) {
                return (
                  <Card key={minor.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
                        <UserIcon className="h-5 w-5" />
                        Configurar Responsável para {minor.first_name} {minor.last_name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <GuardianForm
                        userId={minor.id}
                        onCancel={handleCancelEdit}
                        onSuccess={() => handleGuardianSetupSuccess(minor.id)}
                      />
                    </CardContent>
                  </Card>
                );
              }

              return (
                <Card key={minor.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                        </div>
                        
                        <div>
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                            <Link
                              href={`/perfil/${minor.id}`}
                              className="hover:text-blue-800 dark:hover:text-blue-200 transition-colors cursor-pointer"
                              aria-label={`Ver perfil de ${minor.first_name} ${minor.last_name}`}
                            >
                              {minor.first_name} {minor.last_name}
                            </Link>
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">{minor.email}</p>
                          
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="destructive">
                              Menor de Idade
                            </Badge>
                            {age !== null && (
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {age} anos
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline" className="text-red-600 dark:text-red-400 border-red-300 dark:border-red-600">
                          Responsável Necessário
                        </Badge>
                        
                        <Button
                          onClick={() => handleSetupGuardian(minor.id)}
                          disabled={loading}
                          size="sm"
                        >
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Configurar Responsável
                        </Button>
                      </div>
                    </div>

                    {minor.birth_date && (
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <strong>Data de Nascimento:</strong> {new Date(minor.birth_date).toLocaleDateString('pt-BR')}
                          {age !== null && (
                            <span className="ml-2">
                              (Completa 18 anos em {new Date(new Date(minor.birth_date).setFullYear(new Date(minor.birth_date).getFullYear() + 18)).toLocaleDateString('pt-BR')})
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Seção: Menores COM Responsável */}
      {minorsWithGuardian.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Menores com Responsável Configurado ({minorsWithGuardian.length})
            </h2>
          </div>

          <Alert>
            <CheckCircleIcon className="h-4 w-4" />
            <AlertDescription>
              <strong>Sucesso:</strong> Os estudantes abaixo têm responsáveis legais configurados. 
              Você pode editar as informações do responsável se necessário.
            </AlertDescription>
          </Alert>

          <div className="grid gap-4">
            {minorsWithGuardian.map((minor) => {
              const age = getAgeFromBirthDate(minor.birth_date || null);
              const isEditingThis = isEditing(minor.id) && editingType === 'edit';

              if (isEditingThis) {
                return (
                  <Card key={minor.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
                        <EditIcon className="h-5 w-5" />
                        Editar Responsável de {minor.first_name} {minor.last_name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <GuardianForm
                        userId={minor.id}
                        initialData={{
                          guardian_name: minor.guardian_name || '',
                          guardian_email: minor.guardian_email || '',
                          guardian_phone: minor.guardian_phone || '',
                          guardian_relationship: minor.guardian_relationship || '',
                          guardian_document: minor.guardian_document || ''
                        }}
                        onCancel={handleCancelEdit}
                        onSuccess={() => handleGuardianEditSuccess(minor.id)}
                      />
                    </CardContent>
                  </Card>
                );
              }

              return (
                <Card key={minor.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                          <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                        </div>
                        
                        <div>
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                            <Link
                              href={`/perfil/${minor.id}`}
                              className="hover:text-blue-800 dark:hover:text-blue-200 transition-colors cursor-pointer"
                              aria-label={`Ver perfil de ${minor.first_name} ${minor.last_name}`}
                            >
                              {minor.first_name} {minor.last_name}
                            </Link>
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400">{minor.email}</p>
                          
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="secondary">
                              Menor de Idade
                            </Badge>
                            {age !== null && (
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {age} anos
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-300 dark:border-green-600">
                          Responsável Configurado
                        </Badge>
                        
                        <Button
                          onClick={() => handleEditGuardian(minor.id)}
                          disabled={loading}
                          variant="outline"
                          size="sm"
                        >
                          <EditIcon className="h-4 w-4 mr-2" />
                          Editar Responsável
                        </Button>
                      </div>
                    </div>

                    {/* Informações do Responsável */}
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="space-y-2">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                          Informações do Responsável:
                        </h4>
                        
                        <div className="grid md:grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Nome:</span>
                            <span className="ml-2 text-gray-900 dark:text-gray-100 font-medium">
                              {minor.guardian_name}
                            </span>
                          </div>
                          
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Relação:</span>
                            <span className="ml-2 text-gray-900 dark:text-gray-100">
                              {formatGuardianRelationship(minor.guardian_relationship || '')}
                            </span>
                          </div>
                          
                          <div className="flex items-center">
                            <MailIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-gray-600 dark:text-gray-400">
                              {minor.guardian_email}
                            </span>
                          </div>
                          
                          <div className="flex items-center">
                            <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-gray-600 dark:text-gray-400">
                              {formatPhoneNumberDisplay(minor.guardian_phone || '')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {minor.birth_date && (
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <strong>Data de Nascimento:</strong> {new Date(minor.birth_date).toLocaleDateString('pt-BR')}
                          {age !== null && (
                            <span className="ml-2">
                              (Completa 18 anos em {new Date(new Date(minor.birth_date).setFullYear(new Date(minor.birth_date).getFullYear() + 18)).toLocaleDateString('pt-BR')})
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Mensagem quando só há menores com responsável configurado */}
      {minorsWithoutGuardian.length === 0 && minorsWithGuardian.length > 0 && (
        <Card className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
          <CardContent className="py-6 text-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400 mx-auto mb-2" />
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-1">
              Excelente! Todos os menores têm responsáveis configurados
            </h3>
            <p className="text-green-800 dark:text-green-200 text-sm">
              Não há estudantes menores de idade que precisem de responsável legal no momento.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 