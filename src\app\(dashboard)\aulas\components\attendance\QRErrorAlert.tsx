import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Clock, UserCheck, Info, UserX, QrCode, AlertTriangle } from 'lucide-react';

interface QRErrorMeta {
  expired_at?: string;
  hours_expired?: number;
  minutes_expired?: number;
  can_regenerate?: boolean;
  duplicate_checkin?: boolean;
  existing_checkin_time?: string;
  checked_in_by?: string;
  student_name?: string;
  constraint_violation?: boolean;
  is_self_checkin?: boolean;
  is_instructor_checkin?: boolean;
  current_user_role?: string;
}

interface QRErrorAlertProps {
  error: string;
  meta?: QRErrorMeta;
  onRegenerateQR?: () => void;
  isRegenerating?: boolean;
  className?: string;
}

// Função para determinar o tipo de erro baseado na mensagem e metadata
function getErrorType(error: string, meta?: QRErrorMeta) {
  if (meta?.duplicate_checkin) return 'duplicate';
  if (meta?.expired_at || error.includes('expirou') || error.includes('expired')) return 'expired';
  if (error.includes('inválido') && error.includes('código')) return 'invalid_student';
  if (error.includes('inativo') || error.includes('desativado')) return 'inactive_student';
  if (error.includes('QR') && error.includes('inválido')) return 'invalid_qr';
  return 'generic';
}

// Configurações visuais para cada tipo de erro
function getErrorConfig(type: string) {
  switch (type) {
    case 'duplicate':
      return {
        variant: 'default' as const,
        icon: UserCheck,
        title: 'Check-in já realizado',
        className: 'border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20',
        iconColor: 'text-orange-600 dark:text-orange-400',
        titleColor: 'text-orange-800 dark:text-orange-200',
        descriptionColor: 'text-orange-700 dark:text-orange-300',
        boxColor: 'border-orange-200 dark:border-orange-700 bg-orange-100/50 dark:bg-orange-900/30'
      };
    case 'expired':
      return {
        variant: 'destructive' as const,
        icon: Clock,
        title: 'QR Code Expirado',
        className: '',
        iconColor: 'text-destructive',
        titleColor: 'text-destructive-foreground',
        descriptionColor: 'text-destructive-foreground',
        boxColor: 'border-destructive-foreground/20 bg-destructive/5'
      };
    case 'invalid_student':
      return {
        variant: 'destructive' as const,
        icon: UserX,
        title: 'Código de Aluno Inválido',
        className: '',
        iconColor: 'text-destructive',
        titleColor: 'text-destructive-foreground',
        descriptionColor: 'text-destructive-foreground',
        boxColor: 'border-destructive-foreground/20 bg-destructive/5'
      };
    case 'inactive_student':
      return {
        variant: 'destructive' as const,
        icon: UserX,
        title: 'Aluno Inativo',
        className: '',
        iconColor: 'text-destructive',
        titleColor: 'text-destructive-foreground',
        descriptionColor: 'text-destructive-foreground',
        boxColor: 'border-destructive-foreground/20 bg-destructive/5'
      };
    case 'invalid_qr':
      return {
        variant: 'destructive' as const,
        icon: QrCode,
        title: 'QR Code Inválido',
        className: '',
        iconColor: 'text-destructive',
        titleColor: 'text-destructive-foreground',
        descriptionColor: 'text-destructive-foreground',
        boxColor: 'border-destructive-foreground/20 bg-destructive/5'
      };
    default:
      return {
        variant: 'destructive' as const,
        icon: AlertCircle,
        title: 'Erro no Check-in',
        className: '',
        iconColor: 'text-destructive',
        titleColor: 'text-destructive-foreground',
        descriptionColor: 'text-destructive-foreground',
        boxColor: 'border-destructive-foreground/20 bg-destructive/5'
      };
  }
}

// Função para gerar dicas específicas para cada tipo de erro
function getErrorTip(type: string, meta?: QRErrorMeta) {
  switch (type) {
    case 'duplicate':
      return {
        title: 'ℹ️ Informação: Check-in já confirmado',
        content: meta?.is_self_checkin 
          ? 'Você já possui presença registrada nesta aula. Cada aluno pode fazer check-in apenas uma vez por aula.'
          : `${meta?.student_name || 'O aluno'} já possui presença registrada nesta aula. Cada aluno pode fazer check-in apenas uma vez por aula.`
      };
    case 'expired':
      return {
        title: '💡 Dica: QR Codes expiram após 1 hora para segurança',
        content: 'Gere um novo código para continuar com os check-ins. Isso garante que apenas códigos recentes sejam utilizados.'
      };
    case 'invalid_student':
      return {
        title: '🔍 Verificação: Código do aluno',
        content: 'Verifique se o código foi digitado corretamente. O código deve ter exatamente 6 caracteres alfanuméricos.'
      };
    case 'inactive_student':
      return {
        title: '⚠️ Atenção: Aluno inativo',
        content: 'Este aluno está marcado como inativo no sistema. Entre em contato com a administração para reativar o cadastro.'
      };
    case 'invalid_qr':
      return {
        title: '📱 Verificação: QR Code',
        content: 'Verifique se o QR Code foi copiado corretamente ou se não foi modificado. Tente gerar um novo código.'
      };
    default:
      return {
        title: '🛠️ Suporte: Erro inesperado',
        content: 'Se o problema persistir, entre em contato com o suporte técnico ou tente novamente em alguns minutos.'
      };
  }
}

export function QRErrorAlert({ 
  error, 
  meta, 
  onRegenerateQR, 
  isRegenerating = false,
  className 
}: QRErrorAlertProps) {
  const errorType = getErrorType(error, meta);
  const config = getErrorConfig(errorType);
  const tip = getErrorTip(errorType, meta);
  const IconComponent = config.icon;
  
  return (
    <Alert 
      variant={config.variant}
      className={`${className} ${config.className}`}
    >
      <IconComponent className={`h-4 w-4 ${config.iconColor}`} />
      
      <AlertTitle className={`flex items-center gap-2 ${config.titleColor}`}>
        {config.title}
        <Info className="h-4 w-4" />
      </AlertTitle>
      
      <AlertDescription className={`space-y-3 ${config.descriptionColor}`}>
        <p className="font-medium">{error}</p>
        
        {/* Informações específicas para check-in duplicado */}
        {errorType === 'duplicate' && meta?.existing_checkin_time && (
          <div className="text-xs">
            <p className={`${config.descriptionColor} opacity-80`}>
              Check-in realizado em: {new Date(meta.existing_checkin_time).toLocaleString('pt-BR')}
              {meta?.checked_in_by && ` com ${meta.checked_in_by}`}
            </p>
          </div>
        )}
        
        {/* Botão para regenerar QR Code (apenas para erros expirados) */}
        {errorType === 'expired' && onRegenerateQR && (
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRegenerateQR}
              disabled={isRegenerating}
              className="border-destructive-foreground/20 bg-destructive/10 hover:bg-destructive/20 text-destructive-foreground"
            >
              {isRegenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Gerando...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Gerar Novo QR Code
                </>
              )}
            </Button>
          </div>
        )}
        
        {/* Dica específica para o tipo de erro */}
        <div className={`text-xs border ${config.boxColor} p-3 rounded`}>
          <p className={`font-medium ${config.titleColor} mb-1`}>
            <strong>{tip.title}</strong>
          </p>
          <p className={`${config.descriptionColor} opacity-90`}>
            {tip.content}
          </p>
        </div>
      </AlertDescription>
    </Alert>
  );
} 