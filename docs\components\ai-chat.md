# Componente AI Chat

O componente `AIChat` é um chat minimalista e responsivo que aparece na direita da tela, integrado com o sistema de temas do projeto.

## Características

- **Design Minimalista**: Interface limpa e focada na conversação
- **Responsivo**: Adapta-se perfeitamente para desktop e mobile
- **Integração com Tema**: Usa as cores primárias do tenant automaticamente
- **Animações Suaves**: Transições fluidas com Framer Motion
- **Simulação de IA**: Inclui sistema de simulação para demonstração
- **Estado Global**: Gerenciado via Context API

## Como Usar

### 1. Integração Automática no Dashboard

O chat já está integrado automaticamente no layout do dashboard através do `AIChatProvider` e pode ser acessado via menu do usuário.

### 2. Programaticamente

```tsx
import { useAIChat } from '@/hooks/ui/use-ai-chat';

function MeuComponente() {
  const { openChat, closeChat, toggleChat, isOpen } = useAIChat();

  return (
    <button onClick={openChat}>
      Abrir Chat IA
    </button>
  );
}
```

### 3. Como Componente Standalone

```tsx
import { AIChat } from '@/components/ui/ai-chat';

function MeuComponente() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <AIChat 
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
    />
  );
}
```

## Hook useAIChat

### Métodos Disponíveis

- `openChat()`: Abre o chat
- `closeChat()`: Fecha o chat  
- `toggleChat()`: Alterna entre aberto/fechado
- `isOpen`: Boolean indicando se o chat está aberto

## Personalização

### Integração com IA Real

Para integrar com uma API de IA real, substitua a função `simulateAIResponse` no componente:

```tsx
// Substituir esta função no componente AIChat
const callRealAI = async (userMessage: string): Promise<string> => {
  const response = await fetch('/api/ai-chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ message: userMessage })
  });
  
  const data = await response.json();
  return data.response;
};
```

### Cores e Temas

O componente usa automaticamente as cores do tenant configuradas via `useTenantTheme()`. As cores são aplicadas em:

- Ícone do header
- Mensagens do usuário
- Botão de envio
- Indicador de carregamento

## Estrutura do Componente

- **Header**: Título, subtítulo e botões de ação
- **Messages Area**: Lista de mensagens com scroll automático
- **Input Area**: Campo de texto e botão de envio
- **Empty State**: Tela inicial quando não há mensagens

## Responsividade

- **Desktop**: Aparece como painel fixo na direita (320px de largura)
- **Mobile**: Ocupa a tela inteira com overlay
- **Tablet**: Largura adaptativa (384px)

## Acessibilidade

- Labels ARIA para screen readers
- Navegação por teclado (Enter para enviar)
- Foco automático no input quando aberto
- Indicadores visuais de estado

## Performance

- Auto-scroll suave para novas mensagens
- Debounce interno para prevenir spam
- Componente memoizado para evitar re-renders desnecessários
- Lazy loading automático via Context API 