'use client'

import { useQuery } from '@tanstack/react-query'
import { StudentStats } from '../actions/student-stats'

export interface StudentStatsResponse {
  stats: StudentStats;
  isLoading: boolean;
  error: Error | null;
}

export function useStudentStats(initialStats?: StudentStats): StudentStatsResponse {
  // Usar React Query para cache persistente das estatísticas
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['student-stats'],
    queryFn: async () => {
      // Importação dinâmica para evitar problemas com Server Components
      const { getStudentStats } = await import('../actions/student-stats')
      return await getStudentStats()
    },
    // Se temos dados iniciais, usar como dados padrão
    initialData: initialStats,
    staleTime: 2 * 60 * 1000, // 2 minutos - dados considerados frescos
    gcTime: 15 * 60 * 1000, // 15 minutos em cache
    refetchOnWindowFocus: false, // Não refetch quando voltar à janela
    refetchOnMount: false, // Não refetch automático no mount
    refetchOnReconnect: true, // Apenas refetch quando reconectar
    // Retry em caso de erro
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Backoff exponencial
  })

  return {
    stats: stats || {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0
    },
    isLoading,
    error: error as Error | null
  }
} 