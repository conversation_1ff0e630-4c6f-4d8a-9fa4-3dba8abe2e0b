'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { QrCode, Camera, Smartphone, Keyboard, RefreshCw, AlertTriangle, Search, Plus, Users } from 'lucide-react';
import { checkInByQR } from '../../../presenca/actions/check-in-actions';
import { toast } from 'sonner';
import { QRErrorAlert } from './QRErrorAlert';
import { QRCodeDisplay } from './QRCodeDisplay';
import { useQRCode } from '../../hooks/useQRCode';
import { processCheckInError } from '../../utils/error-handling';
import { StudentSelectionModal } from './StudentSelectionModal';
import type { Student } from '../../hooks/useStudentSelection';

interface QRCodeScannerProps {
  classId: string;
  classGroupId?: string | null;
  tenantId?: string;
  className?: string;
  onCheckInSuccess?: () => void;
  onAuthError?: () => void;
  autoLoadQR?: boolean;
}

export function QRCodeScanner({ 
  classId, 
  classGroupId,
  tenantId,
  className, 
  onCheckInSuccess, 
  onAuthError,
  autoLoadQR = true
}: QRCodeScannerProps) {
  const [scannerMode, setScannerMode] = useState<'manual' | 'camera'>('manual');
  const [qrCode, setQrCode] = useState('');
  const [studentCode, setStudentCode] = useState('');
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [checkInError, setCheckInError] = useState<{ message: string; meta?: any } | null>(null);
  const [isStudentModalOpen, setIsStudentModalOpen] = useState(false);
  const [selectedStudentName, setSelectedStudentName] = useState('');

  // Hook do QR Code - COM autoLoad para carregar automaticamente QR codes ativos
  const qrCodeState = useQRCode(classId, {
    autoLoad: true, // Habilitado para carregar automaticamente
    onError: (error) => {
      if (error.includes('não autenticado') || error.includes('Usuário não autenticado')) {
        onAuthError?.();
      }
    }
  });

  // Carregar QR code ativo quando o componente for montado e autoLoadQR estiver habilitado
  // Usar uma função silenciosa que não mostra toast para carregamento automático
  useEffect(() => {
    if (autoLoadQR && classId && !qrCodeState.hasQRCode && !qrCodeState.isLoading && !qrCodeState.error) {
      // Usar função silenciosa para carregamento automático apenas uma vez
      qrCodeState.loadQRCodeSilently();
    }
  }, [classId, autoLoadQR, qrCodeState.hasQRCode]); // Removido qrCodeState.isLoading das dependências para evitar loops

  useEffect(() => {
    if (autoLoadQR && qrCodeState.hasQRCode && qrCodeState.isValid && !qrCode.trim()) {
      setQrCode(qrCodeState.qrData!.qr_code);
    }
  }, [autoLoadQR, qrCodeState.hasQRCode, qrCodeState.isValid, qrCodeState.qrData, qrCode]);

  const handleManualCheckIn = async () => {
    if (!qrCode.trim() || !studentCode.trim()) {
      toast.error('Preencha todos os campos obrigatórios');
      return;
    }

    setIsProcessing(true);
    setCheckInError(null);

    try {
      const result = await checkInByQR({
        qr_code: qrCode.trim(),
        student_check_in_code: studentCode.trim(),
        notes: notes.trim() || null
      });

      if (result.success) {
        toast.success('Check-in realizado com sucesso!');
        setQrCode('');
        setStudentCode('');
        setNotes('');
        onCheckInSuccess?.();
      } else {
        // Usar a função utilitária para processar o erro
        const errorInfo = processCheckInError(result);
        
        setCheckInError({
          message: errorInfo.message,
          meta: errorInfo.meta
        });

        // Exibir toast apropriado baseado no tipo de erro
        const toastConfig = errorInfo.toastConfig;
        switch (toastConfig.type) {
          case 'info':
            toast.info(errorInfo.message, {
              duration: toastConfig.duration,
              description: toastConfig.description
            });
            break;
          case 'warning':
            toast.warning(errorInfo.message, {
              duration: toastConfig.duration,
              description: toastConfig.description
            });
            break;
          case 'error':
          default:
            toast.error(errorInfo.message, {
              duration: toastConfig.duration,
              description: toastConfig.description
            });
            break;
        }
      }
    } catch (error) {
      setCheckInError({
        message: 'Erro inesperado ao processar check-in'
      });
      toast.error('Erro inesperado ao processar check-in', {
        description: 'Tente novamente ou entre em contato com o suporte'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRegenerateQR = () => {
    qrCodeState.generateQRCode();
    setCheckInError(null);
  };

  const handleLoadExistingQR = () => {
    qrCodeState.loadQRCode();
  };

  const handleStudentSelect = (student: Student) => {
    setStudentCode(student.check_in_code || '');
    setSelectedStudentName(student.user.full_name || student.user.first_name);
    setCheckInError(null);
  };

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Header com Circuit Breaker Status */}
      {qrCodeState.isCircuitOpen && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
          <p className="text-sm text-red-700 dark:text-red-300">
            Sistema temporariamente bloqueado devido a múltiplas falhas. Aguarde alguns minutos.
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={qrCodeState.resetCircuitBreaker}
            className="ml-auto"
          >
            Gerar Novo
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
        {/* Seção de Check-in */}
        <div className="w-full">
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Check-in Manual
                </span>
                <div className="flex gap-2">
                  <Button
                    variant={scannerMode === 'manual' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setScannerMode('manual')}
                  >
                    <Keyboard className="h-4 w-4 mr-1" />
                    Manual
                  </Button>
                  <Button
                    variant={scannerMode === 'camera' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setScannerMode('camera')}
                    disabled
                  >
                    <Camera className="h-4 w-4 mr-1" />
                    Câmera
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {scannerMode === 'manual' ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="qr-code">
                      Código QR da Aula <span className="text-red-500">*</span>
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        id="qr-code"
                        placeholder="Cole o código QR da aula aqui..."
                        value={qrCode}
                        onChange={(e) => {
                          setQrCode(e.target.value);
                          setCheckInError(null);
                        }}
                      />
                      {qrCodeState.hasQRCode && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setQrCode(qrCodeState.qrData!.qr_code)}
                          title="Usar QR Code da aula"
                          disabled={!qrCodeState.isValid}
                        >
                          <QrCode className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    {qrCodeState.hasQRCode && qrCode !== qrCodeState.qrData!.qr_code && qrCodeState.isValid && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Dica: Clique no ícone QR para usar o código da aula atual
                      </p>
                    )}
                    
                    {/* Advertência para QR code próximo da expiração */}
                    {qrCodeState.isNearExpiration && qrCodeState.isValid && (
                      <div className="flex items-center gap-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md mt-2">
                        <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                        <p className="text-xs text-amber-700 dark:text-amber-300">
                          QR Code expira em breve: {qrCodeState.formattedTimeUntilExpiration}
                        </p>
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="student-code">
                      Código do Aluno <span className="text-red-500">*</span>
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        id="student-code"
                        placeholder="Digite o código de check-in do aluno"
                        value={studentCode}
                        onChange={(e) => {
                          setStudentCode(e.target.value);
                          setSelectedStudentName('');
                          setCheckInError(null);
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setIsStudentModalOpen(true)}
                        title="Selecionar aluno da lista"
                        className="flex-shrink-0"
                      >
                        <Users className="h-4 w-4" />
                      </Button>
                    </div>
                    {selectedStudentName && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Aluno selecionado: {selectedStudentName}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="notes">Observações (opcional)</Label>
                                      <Textarea
                    id="notes"
                    placeholder="Adicione observações sobre a presença..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value.slice(0, 500))}
                    className="min-h-[80px]"
                  />
                    <p className="text-xs text-muted-foreground mt-1">
                      {notes.length}/500 caracteres
                    </p>
                  </div>

                  {checkInError && (
                    <QRErrorAlert
                      error={checkInError.message}
                      meta={checkInError.meta}
                      onRegenerateQR={handleRegenerateQR}
                      isRegenerating={qrCodeState.isGenerating}
                      className="mb-4"
                    />
                  )}

                  <Button
                    onClick={handleManualCheckIn}
                    disabled={isProcessing || !qrCode.trim() || !studentCode.trim()}
                    className="w-full"
                  >
                    {isProcessing ? 'Processando...' : 'Confirmar Check-in'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center bg-muted/20 min-h-[300px] flex flex-col items-center justify-center">
                    <Camera className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      Scanner de câmera em desenvolvimento
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Use o modo manual por enquanto
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Seção de QR Code */}
        <div className="w-full space-y-4">
          {/* Estado inicial - nenhum QR Code carregado */}
          {!qrCodeState.hasQRCode && !qrCodeState.isLoading && (
            <Card className="h-fit">
              <CardContent className="text-center py-12">
                <QrCode className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">QR Code da Aula</h3>
                <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                  Carregue um QR Code existente ou gere um novo para que os alunos possam fazer check-in
                </p>
                
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button 
                    onClick={handleLoadExistingQR} 
                    disabled={qrCodeState.isLoading}
                    variant="outline"
                  >
                    {qrCodeState.isLoading ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Carregando...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        Carregar QR Existente
                      </>
                    )}
                  </Button>
                  
                  <Button 
                    onClick={handleRegenerateQR} 
                    disabled={qrCodeState.isGenerating || qrCodeState.isCircuitOpen}
                  >
                    {qrCodeState.isGenerating ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Gerando...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Gerar Novo QR Code
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Loading State - APENAS quando não há QR Code carregado */}
          {qrCodeState.isLoading && !qrCodeState.hasQRCode && (
            <Card className="h-fit">
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 mx-auto animate-spin text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">Carregando QR Code...</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* QR Code Display */}
          {qrCodeState.hasQRCode && qrCodeState.qrData && (
            <QRCodeDisplay
              qrCode={qrCodeState.qrData.qr_code}
              expiresAt={qrCodeState.qrData.expires_at}
              onRegenerateQR={handleRegenerateQR}
              isRegenerating={qrCodeState.isGenerating}
              className="h-fit"
            />
          )}
        </div>
      </div>

      {/* Rodapé com informações de estado */}
      {qrCodeState.lastRefresh && (
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            Última atualização: {qrCodeState.lastRefresh.toLocaleTimeString('pt-BR')}
          </p>
        </div>
      )}

      {/* Instruções */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Como funciona o Check-in via QR Code</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Para Instrutores:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
                <li>Clique em "Carregar QR Existente" para buscar QR Codes ativos</li>
                <li>Ou clique em "Gerar Novo QR Code" para criar um código</li>
                <li>Compartilhe o QR Code ou link com os alunos</li>
                <li>Os códigos são válidos por 1 hora para segurança</li>
                <li>Monitore os check-ins em tempo real</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Para Alunos:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
                <li>Escaneie o QR Code com a câmera do celular</li>
                <li>Ou acesse o link compartilhado pelo instrutor</li>
                <li>Digite seu código de check-in quando solicitado</li>
                <li>Confirme sua presença na aula</li>
                <li>Aguarde a confirmação do sistema</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal de seleção de alunos */}
      <StudentSelectionModal
        classId={classId}
        classGroupId={classGroupId}
        tenantId={tenantId}
        open={isStudentModalOpen}
        onOpenChange={setIsStudentModalOpen}
        onStudentSelect={handleStudentSelect}
      />
    </div>
  );
} 