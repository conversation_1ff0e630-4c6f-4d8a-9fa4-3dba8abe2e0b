"use client";

/**
 * Seção de Alunos e Retenção do Dashboard Financeiro
 * Exibe métricas de alunos ativos, taxa de retenção, churn rate e LTV
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Users,
  UserMinus,
  TrendingUp,
  TrendingDown,
  DollarSign,
  AlertCircle,
  Activity,
  Target,
  Percent
} from 'lucide-react';

import { EnhancedKPICard } from './EnhancedKPICard';
import { LoadingStates } from './LoadingStates';
import {
  StudentGrowthChart,
  StudentStatusChart,
  RetentionCohortChart,
  LTVSegmentChart
} from './charts';
import {
  getStudentStatusChart,
  getStudentGrowthChart,
  getRetentionCohortChart,
  getLTVSegmentChart
} from '../actions/charts/student-chart-actions';
import { getStudentKPIMetrics, getCurrentMonthStudentMetrics } from '../actions/metrics/student-actions';

import type {
  FinancialKPIs,
  DashboardData,
  MetricWithGrowth,
  StudentMetrics
} from '../types/dashboard-types';
import { formatCurrency, formatPercentage, formatNumber } from '../utils/dashboard-utils';

// ============================================================================
// INTERFACES
// ============================================================================

interface StudentsSectionProps {
  kpis: FinancialKPIs;
  data: DashboardData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const StudentsSection: React.FC<StudentsSectionProps> = ({
  kpis,
  data,
  loading = false,
  error = null,
  className = ''
}) => {
  const [chartData, setChartData] = useState<{
    status: any;
    growth: any[];
    cohort: any[];
    ltv: any[];
  } | null>(null);
  const [chartsLoading, setChartsLoading] = useState(true);
  const [kpiMetrics, setKpiMetrics] = useState<{
    retentionRate: MetricWithGrowth;
    churnRate: MetricWithGrowth;
    averageLifetimeValue: MetricWithGrowth;
  } | null>(null);
  const [kpiLoading, setKpiLoading] = useState(true);
  const [currentMonthMetrics, setCurrentMonthMetrics] = useState<StudentMetrics | null>(null);
  const [currentMonthLoading, setCurrentMonthLoading] = useState(true);

  // Carregar dados dos gráficos
  useEffect(() => {
    const loadChartData = async () => {
      try {
        setChartsLoading(true);

        // Primeiro buscar dados de status para garantir consistência
        const statusResult = await getStudentStatusChart();

        const [growthResult, cohortResult, ltvResult] = await Promise.all([
          getStudentGrowthChart({
            startDate: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000),
            endDate: new Date(),
            period: 'custom',
            label: 'Últimos 6 meses'
          }),
          getRetentionCohortChart(),
          getLTVSegmentChart()
        ]);

        // Os dados de crescimento agora já vêm com dados reais, não precisam de ajuste
        const growthData = growthResult.success ? (growthResult.data || []) : [];

        setChartData({
          status: statusResult.success ? statusResult.data : null,
          growth: growthData,
          cohort: cohortResult.success ? (cohortResult.data || []) : [],
          ltv: ltvResult.success ? (ltvResult.data || []) : []
        });
      } catch (err) {
        console.error('Erro ao carregar dados dos gráficos:', err);
      } finally {
        setChartsLoading(false);
      }
    };

    loadChartData();
  }, []);

  // Carregar métricas KPI históricas
  useEffect(() => {
    const loadKPIMetrics = async () => {
      try {
        setKpiLoading(true);
        const result = await getStudentKPIMetrics();

        if (result.success && result.data) {
          setKpiMetrics(result.data);
        } else {
          console.error('Erro ao carregar métricas KPI:', result.error);
        }
      } catch (err) {
        console.error('Erro ao carregar métricas KPI:', err);
      } finally {
        setKpiLoading(false);
      }
    };

    loadKPIMetrics();
  }, []);

  // Carregar métricas do mês atual para o resumo
  useEffect(() => {
    const loadCurrentMonthMetrics = async () => {
      try {
        setCurrentMonthLoading(true);
        const result = await getCurrentMonthStudentMetrics();

        if (result.success && result.data) {
          setCurrentMonthMetrics(result.data);
        } else {
          console.error('Erro ao carregar métricas do mês atual:', result.error);
        }
      } catch (err) {
        console.error('Erro ao carregar métricas do mês atual:', err);
      } finally {
        setCurrentMonthLoading(false);
      }
    };

    loadCurrentMonthMetrics();
  }, []);

  if (loading) {
    return <LoadingStates.Dashboard />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  // Usar métricas do mês atual para o resumo, fallback para data.studentMetrics
  const studentMetrics = currentMonthMetrics || data.studentMetrics;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* KPIs de Alunos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Alunos Ativos */}
        <EnhancedKPICard
          title="Alunos Ativos"
          metric={kpis.activeStudents}
          icon={<Users className="h-5 w-5" />}
          description="Alunos com mensalidades em dia"
          variant="default"
        />

        {/* Taxa de Retenção */}
        <EnhancedKPICard
          title="Taxa de Retenção"
          metric={kpiMetrics?.retentionRate || {
            current: studentMetrics.retentionRate,
            previous: studentMetrics.retentionRate,
            growth: 0,
            trend: studentMetrics.retentionRate >= 80 ? 'up' : studentMetrics.retentionRate >= 60 ? 'stable' : 'down',
            formatted: {
              current: formatPercentage(studentMetrics.retentionRate),
              previous: formatPercentage(studentMetrics.retentionRate),
              growth: '0%'
            }
          }}
          icon={<Target className="h-5 w-5" />}
          description="% de alunos que permanecem"
          variant="default"
        />

        {/* Churn Rate */}
        <EnhancedKPICard
          title="Churn Rate"
          metric={kpiMetrics?.churnRate || {
            current: studentMetrics.churnRate,
            previous: studentMetrics.churnRate,
            growth: 0,
            trend: studentMetrics.churnRate <= 10 ? 'up' : studentMetrics.churnRate <= 20 ? 'stable' : 'down',
            formatted: {
              current: formatPercentage(studentMetrics.churnRate),
              previous: formatPercentage(studentMetrics.churnRate),
              growth: '0%'
            }
          }}
          icon={<UserMinus className="h-5 w-5" />}
          description="% de alunos que cancelam"
          variant="default"
        />

        {/* LTV Médio */}
        <EnhancedKPICard
          title="LTV Médio"
          metric={kpiMetrics?.averageLifetimeValue || {
            current: studentMetrics.averageLifetimeValue,
            previous: studentMetrics.averageLifetimeValue,
            growth: 0,
            trend: 'stable',
            formatted: {
              current: formatCurrency(studentMetrics.averageLifetimeValue),
              previous: formatCurrency(studentMetrics.averageLifetimeValue),
              growth: '0%'
            }
          }}
          icon={<DollarSign className="h-5 w-5" />}
          description="Valor médio por aluno"
          variant="default"
        />
      </div>

      {/* Métricas Detalhadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Resumo de Alunos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Resumo de Alunos
            </CardTitle>
            <CardDescription>
              Visão geral da base de alunos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total de Alunos</span>
                  <span className="font-medium">{formatNumber(studentMetrics.totalStudents)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Alunos Ativos</span>
                  <span className="font-medium text-green-600">{formatNumber(studentMetrics.activeStudents)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Novos Alunos</span>
                  <span className="font-medium text-blue-600">{formatNumber(studentMetrics.newStudents)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Cancelamentos</span>
                  <span className="font-medium text-red-600">{formatNumber(studentMetrics.churnedStudents)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Taxa de Ativação</span>
                  <span className="font-medium">
                    {formatPercentage(studentMetrics.totalStudents > 0 ? 
                      (studentMetrics.activeStudents / studentMetrics.totalStudents) * 100 : 0)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Receita por Aluno</span>
                  <span className="font-medium">{kpis.averageRevenuePerUser.formatted.current}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Análise de Retenção */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5" />
              Análise de Retenção
            </CardTitle>
            <CardDescription>
              Métricas de permanência e fidelidade
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Taxa de Retenção</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-green-600">
                    {formatPercentage(studentMetrics.retentionRate)}
                  </span>
                  {studentMetrics.retentionRate >= 80 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Churn Rate</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-red-600">
                    {formatPercentage(studentMetrics.churnRate)}
                  </span>
                  {studentMetrics.churnRate <= 20 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">LTV Médio</span>
                <span className="font-medium text-purple-600">
                  {formatCurrency(studentMetrics.averageLifetimeValue)}
                </span>
              </div>

              <div className="pt-2 border-t">
                <div className="text-xs text-muted-foreground mb-2">Status da Retenção</div>
                <div className="flex items-center gap-2">
                  {studentMetrics.retentionRate >= 80 ? (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-green-600">Excelente</span>
                    </>
                  ) : studentMetrics.retentionRate >= 60 ? (
                    <>
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm text-yellow-600">Boa</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm text-red-600">Precisa Melhorar</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos de Análise */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Evolução de Alunos */}
        <StudentGrowthChart
          data={chartData?.growth}
          loading={chartsLoading}
        />

        {/* Status dos Alunos */}
        <StudentStatusChart
          data={chartData?.status || {
            active: studentMetrics.activeStudents,
            paused: 0,
            canceled: studentMetrics.churnedStudents,
            total: studentMetrics.totalStudents
          }}
          loading={chartsLoading}
        />
      </div>

      {/* Gráficos Avançados */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Análise de Retenção por Coorte */}
        <RetentionCohortChart
          data={chartData?.cohort}
          loading={chartsLoading}
        />

        {/* LTV por Segmento */}
        <LTVSegmentChart
          data={chartData?.ltv}
          loading={chartsLoading}
        />
      </div>
    </div>
  );
};
