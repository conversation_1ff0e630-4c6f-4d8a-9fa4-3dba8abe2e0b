'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

type LogoUploadStatus = 'idle' | 'uploading' | 'success' | 'error' | 'deleting';

interface LogoUploadContextType {
  file: File | null;
  status: LogoUploadStatus;
  progress: number;
  previewUrl: string | null;
  error: string | null;
  logoUrl: string | null;
  temporaryLogoUrl: string | null;
  isDeleting: boolean;
  hasPendingChanges: boolean;
  setFile: (file: File | null) => void;
  setStatus: (status: LogoUploadStatus) => void;
  setProgress: (progress: number) => void;
  setError: (error: string | null) => void;
  setLogoUrl: (url: string | null) => void;
  setTemporaryLogoUrl: (url: string | null) => void;
  setIsDeleting: (isDeleting: boolean) => void;
  setHasPendingChanges: (hasPending: boolean) => void;
  reset: () => void;
  clearTemporary: () => void;
}

const LogoUploadContext = createContext<LogoUploadContextType | undefined>(undefined);

export const useLogoUpload = () => {
  const context = useContext(LogoUploadContext);
  
  if (context === undefined) {
    throw new Error('useLogoUpload deve ser usado dentro de um LogoUploadProvider');
  }
  
  return context;
};

interface LogoUploadProviderProps {
  children: ReactNode;
  initialLogoUrl?: string | null;
}

export const LogoUploadProvider = ({ 
  children, 
  initialLogoUrl = null 
}: LogoUploadProviderProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState<LogoUploadStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [logoUrl, setLogoUrl] = useState<string | null>(initialLogoUrl);
  const [temporaryLogoUrl, setTemporaryLogoUrl] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);

  const handleSetFile = (newFile: File | null) => {
    setFile(newFile);
    
    if (newFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(newFile);
      setHasPendingChanges(true);
    } else {
      setPreviewUrl(null);
      setHasPendingChanges(false);
    }
  };

  const reset = () => {
    setFile(null);
    setStatus('idle');
    setProgress(0);
    setPreviewUrl(null);
    setError(null);
    setIsDeleting(false);
    setHasPendingChanges(false);
    setTemporaryLogoUrl(null);
  };

  const clearTemporary = () => {
    setFile(null);
    setPreviewUrl(null);
    setTemporaryLogoUrl(null);
    setHasPendingChanges(false);
    setError(null);
    setStatus('idle');
    setProgress(0);
  };

  return (
    <LogoUploadContext.Provider
      value={{
        file,
        status,
        progress,
        previewUrl,
        error,
        logoUrl,
        temporaryLogoUrl,
        isDeleting,
        hasPendingChanges,
        setFile: handleSetFile,
        setStatus,
        setProgress,
        setError,
        setLogoUrl,
        setTemporaryLogoUrl,
        setIsDeleting,
        setHasPendingChanges,
        reset,
        clearTemporary
      }}
    >
      {children}
    </LogoUploadContext.Provider>
  );
}; 