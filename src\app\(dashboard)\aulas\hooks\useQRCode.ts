'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { getActiveClassQR, generateClassQR } from '../../presenca/actions/attendance-actions';
import { toast } from 'sonner';

interface QRCodeData {
  qr_code: string;
  expires_at: string;
  created_at: string;
}

interface UseQRCodeOptions {
  autoLoad?: boolean;
  refreshInterval?: number;
  onSuccess?: (data: QRCodeData) => void;
  onError?: (error: string) => void;
}

export function useQRCode(classId: string, options: UseQRCodeOptions = {}) {
  const { 
    autoLoad = false,
    refreshInterval = 30000,
    onSuccess,
    onError 
  } = options;

  const [qrData, setQrData] = useState<QRCodeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  const failureCount = useRef(0);
  const maxFailures = useRef(3);
  const isCircuitOpen = useRef(false);
  const circuitResetTimer = useRef<NodeJS.Timeout | null>(null);
  const lastFailureTime = useRef<Date | null>(null);

  const loadQRCodeTimeout = useRef<NodeJS.Timeout | null>(null);
  const isLoadingRef = useRef(false);

  const resetCircuitBreaker = useCallback(() => {
    failureCount.current = 0;
    isCircuitOpen.current = false;
    lastFailureTime.current = null;
    if (circuitResetTimer.current) {
      clearTimeout(circuitResetTimer.current);
      circuitResetTimer.current = null;
    }
  }, []);

  const openCircuitBreaker = useCallback(() => {
    isCircuitOpen.current = true;
    lastFailureTime.current = new Date();
    
    circuitResetTimer.current = setTimeout(() => {
      resetCircuitBreaker();
    }, 2 * 60 * 1000);
  }, [resetCircuitBreaker]);

  const recordFailure = useCallback((errorMessage: string) => {
    failureCount.current += 1;
    
    if (errorMessage.includes('não autenticado') || errorMessage.includes('Usuário não autenticado') || failureCount.current >= maxFailures.current) {
      openCircuitBreaker();
    }
  }, [openCircuitBreaker]);

  const isExpired = useCallback(() => {
    if (!qrData?.expires_at) return true;
    return new Date() > new Date(qrData.expires_at);
  }, [qrData]);

  const loadQRCode = useCallback(async (showToast = false) => {
    if (!classId) return;

    if (isCircuitOpen.current) {
      const timeSinceLastFailure = lastFailureTime.current 
        ? Date.now() - lastFailureTime.current.getTime() 
        : 0;
      
      if (timeSinceLastFailure < 2 * 60 * 1000) {
        const errorMessage = 'Muitas tentativas falharam. Aguarde antes de tentar novamente.';
        setError(errorMessage);
        onError?.(errorMessage);
        return;
      } else {
        resetCircuitBreaker();
      }
    }

    if (isLoadingRef.current) {
      return;
    }

    if (qrData && !isExpired() && !showToast) {
      return;
    }

    isLoadingRef.current = true;
    setIsLoading(true);
    setError(null);

    try {
      const result = await getActiveClassQR(classId);
      
      if (result.success) {
        if (result.data) {
          const qrCodeData: QRCodeData = {
            qr_code: result.data.qr_code,
            expires_at: result.data.expires_at,
            created_at: new Date().toISOString()
          };
          setQrData(qrCodeData);
          setLastRefresh(new Date());
          onSuccess?.(qrCodeData);
          
          resetCircuitBreaker();
          
          if (showToast) {
            toast.success('QR Code carregado com sucesso!');
          }
        } else {
          setQrData(null);
          if (showToast) {
            toast.info('Nenhum QR Code ativo encontrado');
          }
        }
      } else {
        const errorMessage = typeof result.errors === 'object' && result.errors && '_form' in result.errors
          ? result.errors._form 
          : 'Erro ao carregar QR Code';
        setError(errorMessage as string);
        onError?.(errorMessage as string);
        
        recordFailure(errorMessage as string);
        
        if (showToast) {
          toast.error(errorMessage as string);
        }
      }
    } catch (err) {
      const errorMessage = 'Erro inesperado ao carregar QR Code';
      setError(errorMessage);
      onError?.(errorMessage);
      
      recordFailure(errorMessage);
      
      if (showToast) {
        toast.error(errorMessage);
      }
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [classId, onSuccess, onError, resetCircuitBreaker, recordFailure, qrData, isExpired]);

  const generateQRCode = useCallback(async (expiresInMinutes = 60) => {
    if (!classId) return;

    if (isCircuitOpen.current) {
      const errorMessage = 'Muitas tentativas falharam. Aguarde antes de tentar novamente.';
      setError(errorMessage);
      onError?.(errorMessage);
      toast.error(errorMessage);
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const result = await generateClassQR({
        class_id: classId,
        expires_in_minutes: expiresInMinutes
      });

      if (result.success && result.data) {
        const newQRData = {
          qr_code: result.data.qr_code,
          expires_at: result.data.expires_at,
          created_at: new Date().toISOString()
        };
        
        setQrData(newQRData);
        setLastRefresh(new Date());
        onSuccess?.(newQRData);
        
        resetCircuitBreaker();
        
        toast.success('Novo QR Code gerado com sucesso!');
        
        return newQRData;
      } else {
        const errorMessage = typeof result.errors === 'object' && result.errors && '_form' in result.errors
          ? result.errors._form 
          : 'Erro ao gerar QR Code';
        setError(errorMessage as string);
        onError?.(errorMessage as string);
        
        recordFailure(errorMessage as string);
        
        toast.error(errorMessage as string);
      }
    } catch (err) {
      const errorMessage = 'Erro inesperado ao gerar QR Code';
      setError(errorMessage);
      onError?.(errorMessage);
      
      recordFailure(errorMessage);
      
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  }, [classId, onSuccess, onError, resetCircuitBreaker, recordFailure]);

  const refresh = useCallback(() => {
    if (loadQRCodeTimeout.current) {
      clearTimeout(loadQRCodeTimeout.current);
    }
    
    loadQRCodeTimeout.current = setTimeout(() => {
      loadQRCode(true);
    }, 300);
  }, [loadQRCode]);

  useEffect(() => {
    if (!autoLoad || !classId || !qrData) return;

    const interval = setInterval(() => {
      if (isCircuitOpen.current) {
        return;
      }
      
      if (qrData && isExpired()) {
        return;
      }
      
      if (isLoadingRef.current) {
        return;
      }
      
      loadQRCode(false);
    }, refreshInterval);

    return () => {
      clearInterval(interval);
      if (loadQRCodeTimeout.current) {
        clearTimeout(loadQRCodeTimeout.current);
      }
    };
  }, [classId, autoLoad, refreshInterval, loadQRCode, qrData, isExpired]);

  useEffect(() => {
    return () => {
      if (loadQRCodeTimeout.current) {
        clearTimeout(loadQRCodeTimeout.current);
      }
      if (circuitResetTimer.current) {
        clearTimeout(circuitResetTimer.current);
      }
    };
  }, []);

  const getTimeUntilExpiration = useCallback(() => {
    if (!qrData?.expires_at) return null;

    const now = new Date();
    const expiration = new Date(qrData.expires_at);
    const diff = expiration.getTime() - now.getTime();

    if (diff <= 0) return null;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return { hours, minutes, seconds, total: diff };
  }, [qrData]);

  const getFormattedTimeUntilExpiration = useCallback(() => {
    const time = getTimeUntilExpiration();
    if (!time) return 'Expirado';

    const { hours, minutes, seconds } = time;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }, [getTimeUntilExpiration]);

  const isNearExpiration = useCallback(() => {
    const time = getTimeUntilExpiration();
    if (!time) return false;
    
    return time.total < 5 * 60 * 1000;
  }, [getTimeUntilExpiration]);

  return {
    qrData,
    isLoading,
    isGenerating,
    error,
    lastRefresh,
    
    isExpired: isExpired(),
    isNearExpiration: isNearExpiration(),
    timeUntilExpiration: getTimeUntilExpiration(),
    formattedTimeUntilExpiration: getFormattedTimeUntilExpiration(),
    
    isCircuitOpen: isCircuitOpen.current,
    failureCount: failureCount.current,
    
    loadQRCode: () => loadQRCode(true),
    loadQRCodeSilently: () => loadQRCode(false),
    generateQRCode,
    refresh,
    resetCircuitBreaker,
    
    hasQRCode: !!qrData,
    isValid: !!qrData && !isExpired(),
  };
} 