'use client'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { XIcon, Loader2, AlertTriangle } from 'lucide-react'
import { ImportSettings } from './import-settings'
import { DataTable } from './data-table'
import { useState, useMemo } from 'react'
import { importStudents } from '../actions/import-students'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { ImportData, systemFields, validateImportData } from '../validation'

interface MappingStepProps {
  headers: string[]
  data: string[][]
  onCancel: () => void
}

export function MappingStep({ headers, data, onCancel }: MappingStepProps) {
  const [isImporting, setIsImporting] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [validatingRows, setValidatingRows] = useState<Set<number>>(new Set())
  const [importingRows, setImportingRows] = useState<Set<number>>(new Set())
  const [importProgressRows, setImportProgressRows] = useState<Set<number>>(new Set())
  const [importSettings, setImportSettings] = useState({
    defaultStatus: 'active' as 'active' | 'inactive',
    duplicateDetection: 'email' as 'email' | 'name',
    duplicateAction: 'do_nothing' as 'do_nothing' | 'overwrite',
    branch_id: '',
    emptyFieldAction: 'error' as 'allow' | 'skip_row' | 'error',
  })

  // State lifted from DataTable
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set())
  const [mappings, setMappings] = useState<Record<string, string>>({})
  const [validationResults, setValidationResults] = useState<ImportData[]>([])
  const [isValidated, setIsValidated] = useState(false)

  const router = useRouter()

  const handleMappingChange = (header: string, fieldValue: string) => {
    const newMappings = { ...mappings }
    if (fieldValue !== 'ignore') {
      const existingHeader = Object.keys(newMappings).find(
        key => newMappings[key] === fieldValue,
      )
      if (existingHeader) {
        delete newMappings[existingHeader]
      }
    }
    if (fieldValue === 'ignore') {
      delete newMappings[header]
    } else {
      newMappings[header] = fieldValue
    }
    setMappings(newMappings)
    setIsValidated(false)
  }

  const validateData = async () => {
    setIsValidating(true)
    
    // Mostrar efeito visual de validação
    const selectedRowsArray = Array.from(selectedRows).sort((a, b) => a - b)
    setValidatingRows(new Set(selectedRowsArray))
    
    // Aguardar um pouco para mostrar o efeito visual
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let results = validateImportData(
      headers,
      data,
      selectedRows,
      mappings,
      importSettings.emptyFieldAction,
    )

    const emailsToCheck = Array.from(
      new Set(
        results
          .filter(r => r.data.email && !r.errors.email)
          .map(r => r.data.email.trim().toLowerCase()),
      ),
    )

    if (emailsToCheck.length > 0) {
      try {
        const response = await fetch('/api/students/email-availability/bulk', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ emails: emailsToCheck }),
        })

        if (response.ok) {
          const json: { unavailable: string[] } = await response.json()
          const unavailableSet = new Set(
            json.unavailable.map(e => e.trim().toLowerCase()),
          )
          results = results.map(r => {
            const normalized = r.data.email?.trim().toLowerCase()
            if (normalized && unavailableSet.has(normalized)) {
              r.errors.email = 'E-mail já cadastrado no sistema'
              r.isValid = false
            }
            return r
          })
        }
      } catch (err) {
        console.error('Erro na verificação em lote de e-mails:', err)
      }
    }

    setValidationResults(results)
    setIsValidated(true)
    setValidatingRows(new Set())
    setIsValidating(false)
  }

  const handleImport = async () => {
    if (!importSettings.branch_id) {
      toast.error('Por favor, selecione uma filial nas configurações')
      return
    }

    setIsImporting(true)
    const validData = validationResults.filter(
      r => r.isValid && selectedRows.has(r.rowIndex),
    )
    
    // Preparar dados de importação com índices originais
    const dataWithIndexes = validData.map(item => ({
      ...item,
      originalIndex: item.rowIndex
    }))

    try {
      const studentsData = dataWithIndexes.map(item => {
        const studentData: any = {}
        Object.entries(item.data).forEach(([field, value]) => {
          if (value && typeof value === 'string' && value.trim() !== '') {
            studentData[field] = value.trim()
          }
        })
        return studentData
      })

      // Mostrar efeito visual de importação
      const importingRowsSet = new Set(dataWithIndexes.map(item => item.originalIndex))
      setImportingRows(importingRowsSet)
      
      const result = await importStudents(studentsData, importSettings)

      // Mostrar progresso de sucesso
      setImportingRows(new Set())
      setImportProgressRows(new Set(dataWithIndexes.map(item => item.originalIndex)))

      if (result.success) {
        toast.success(result.message)
        if (result.failed > 0) {
          const errorMessages = result.errors
            .slice(0, 3)
            .map(error => `Linha ${error.row}: ${error.error}`)
            .join('\n')
          toast.warning(
            `${result.imported} importados com sucesso. ${result.failed} falharam:\n${errorMessages}`,
          )
        }
        
        // Aguardar um pouco para mostrar o estado de sucesso
        await new Promise(resolve => setTimeout(resolve, 1500))
        router.push('/alunos')
      } else {
        toast.error(result.message)
        setImportingRows(new Set())
        setImportProgressRows(new Set())
      }
    } catch (error) {
      console.error('Erro na importação:', error)
      toast.error('Erro inesperado durante a importação')
      setImportingRows(new Set())
      setImportProgressRows(new Set())
    } finally {
      setIsImporting(false)
    }
  }

  // Derived state for UI
  const { canValidate, importableRowsCount, hasUnvalidatedSelection } =
    useMemo(() => {
      const requiredFields = systemFields.filter(f => f.required)
      const mappedRequiredFields = requiredFields.filter(field =>
        Object.values(mappings).includes(field.value),
      )
      const canValidate =
        mappedRequiredFields.length === requiredFields.length &&
        selectedRows.size > 0

      const validatedRowIndexes = new Set(
        validationResults.map(r => r.rowIndex),
      )
      const hasUnvalidatedSelection = Array.from(selectedRows).some(
        rowIndex => !validatedRowIndexes.has(rowIndex),
      )

      const importableRowsCount = validationResults.filter(
        r => r.isValid && selectedRows.has(r.rowIndex),
      ).length

      return { canValidate, importableRowsCount, hasUnvalidatedSelection }
    }, [mappings, selectedRows, validationResults])

  return (
    <div className="space-y-6">
      <div className="grid lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ImportSettings
            settings={importSettings}
            onSettingsChange={update =>
              setImportSettings(prev =>
                typeof update === 'function'
                  ? update(prev)
                  : { ...prev, ...update },
              )
            }
          />
        </div>
        <div className="lg:col-span-1 space-y-6 flex flex-col">
          <Card className="relative bg-teal-50 border-teal-200 dark:bg-teal-900/30 dark:border-teal-800 flex-grow flex flex-col items-center justify-center text-center">
            <div className="absolute top-2 right-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={onCancel}
                disabled={isImporting || isValidating}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
            <div>
              <span className="text-6xl font-bold text-teal-700 dark:text-teal-200">
                {data.length}
              </span>
              <p className="text-base font-medium text-teal-600 dark:text-teal-300 mt-2">
                LINHAS DETECTADAS
              </p>
            </div>
          </Card>
          {(isImporting || isValidating) && (
            <Card>
              <CardContent className="p-6">
                <div className={`flex items-center justify-center p-4 rounded-lg ${
                  isValidating 
                    ? 'bg-blue-50 dark:bg-blue-900/30' 
                    : 'bg-green-50 dark:bg-green-900/30'
                }`}>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className={`text-sm font-medium ${
                    isValidating 
                      ? 'text-blue-700 dark:text-blue-300' 
                      : 'text-green-700 dark:text-green-300'
                  }`}>
                    {isValidating 
                      ? 'Validando dados... Isso pode levar alguns segundos.'
                      : 'Importando alunos... Isso pode levar alguns minutos.'
                    }
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      <div className="w-full">
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    Mapeamento de Campos
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Selecione os campos corretos para cada uma das colunas de
                    dados abaixo.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-3 flex-shrink-0">
                  <Button
                    variant="outline"
                    onClick={validateData}
                    disabled={!canValidate || isImporting || isValidating}
                  >
                    {isValidating && (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    )}
                    Validar Dados
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={
                      !isValidated ||
                      importableRowsCount === 0 ||
                      hasUnvalidatedSelection ||
                      isImporting ||
                      isValidating
                    }
                  >
                    {isImporting && (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    )}
                    Importar{' '}
                    {importableRowsCount > 0
                      ? `${importableRowsCount} Alunos`
                      : 'Alunos'}
                  </Button>
                </div>
              </div>
              {/* {hasUnvalidatedSelection && isValidated && (
                <Alert className="border-orange-300 bg-orange-50 text-orange-900 dark:border-orange-700 dark:bg-orange-950 dark:text-orange-300">
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                  <AlertTitle>Validação necessária</AlertTitle>
                  <AlertDescription>
                    A seleção de linhas foi alterada. Por favor, valide os dados
                    novamente antes de importar.
                  </AlertDescription>
                </Alert>
              )} */}
              <DataTable
                headers={headers}
                data={data}
                mappings={mappings}
                onMappingChange={handleMappingChange}
                selectedRows={selectedRows}
                onSelectedRowsChange={setSelectedRows}
                validationResults={validationResults}
                isValidating={isValidating}
                isImporting={isImporting}
                validatingRows={validatingRows}
                importingRows={importingRows}
                importProgressRows={importProgressRows}
                onImport={handleImport}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 