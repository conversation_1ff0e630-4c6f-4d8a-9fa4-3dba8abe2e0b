{"v": "5.12.0", "fr": 15, "ip": 0, "op": 30, "w": 1000, "h": 1000, "nm": "CHECK", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"k": [{"s": [0], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 29, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 9, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [494, 494], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.180391992307, 0.160783999574, 0.23529399797, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 40, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"k": [{"s": [0], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.195], "t": 2, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.391], "t": 3, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.781], "t": 4, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [1.562], "t": 5, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [3.125], "t": 6, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [6.25], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.5], "t": 8, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [25], "t": 9, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [75], "t": 11, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [87.5], "t": 12, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [93.75], "t": 13, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [96.875], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [98.438], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [99.219], "t": 16, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [99.609], "t": 17, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [99.805], "t": 18, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [100], "t": 20, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"k": [{"s": [-180], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-179.791], "t": 1, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-179.5], "t": 2, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-178.819], "t": 3, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-177.188], "t": 4, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-173.309], "t": 5, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-164.092], "t": 6, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-142.158], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-37.842], "t": 9, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-15.908], "t": 10, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.691], "t": 11, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-2.812], "t": 12, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-1.181], "t": 13, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-0.5], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-0.209], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 16, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 30, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 1.5, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-89, -33.188], [-88.875, -33.062], [-89, -32.5], [-124.5, 3], [-124.437, 3.188], [-88.75, -33.062]], "c": true}], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-62.748, -6.833], [-62.624, -6.708], [-89, -32.5], [-124.5, 3], [-83.642, 43.932], [-47.954, 7.682]], "c": true}], "t": 1, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-41.913, 14.085], [-41.788, 14.21], [-89, -32.5], [-124.5, 3], [-51.263, 76.27], [-15.575, 40.02]], "c": true}], "t": 2, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-25.375, 30.688], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [10.125, 65.688]], "c": true}], "t": 3, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.111, 4.24], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.937], [36.611, 39.24]], "c": true}], "t": 4, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.13, -16.749], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.937], [57.63, 18.251]], "c": true}], "t": 5, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[38.812, -33.406], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [74.312, 1.594]], "c": true}], "t": 6, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[52.055, -46.63], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [87.555, -11.63]], "c": true}], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[62.565, -57.124], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [98.065, -22.124]], "c": true}], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70.906, -65.453], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [106.406, -30.453]], "c": true}], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[77.526, -72.063], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [113.026, -37.063]], "c": true}], "t": 10, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[82.783, -77.312], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [118.283, -42.312]], "c": true}], "t": 11, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[86.953, -81.477], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [122.453, -46.477]], "c": true}], "t": 12, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[90.263, -84.781], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [125.763, -49.781]], "c": true}], "t": 13, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[92.891, -87.406], [-25.25, 30.813], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [128.391, -52.406]], "c": true}], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[94.977, -89.488], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [130.477, -54.488]], "c": true}], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[96.631, -91.141], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [132.131, -56.141]], "c": true}], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[97.944, -92.451], [-25.25, 30.813], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [133.444, -57.451]], "c": true}], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[98.988, -93.494], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [134.488, -58.494]], "c": true}], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[99.816, -94.32], [-25.25, 30.813], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [135.316, -59.32]], "c": true}], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[100.472, -94.975], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [135.972, -59.975]], "c": true}], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[100.994, -95.497], [-25.25, 30.813], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [136.494, -60.497]], "c": true}], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[101.408, -95.91], [-25.25, 30.813], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [136.908, -60.91]], "c": true}], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[101.738, -96.24], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [137.238, -61.24]], "c": true}], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[101.997, -96.499], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [137.497, -61.499]], "c": true}], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[102.206, -96.707], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [137.706, -61.707]], "c": true}], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[102.369, -96.87], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [137.869, -61.87]], "c": true}], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[102.499, -96.999], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [137.999, -61.999]], "c": true}], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[102.603, -97.104], [-25.25, 30.812], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [138.103, -62.104]], "c": true}], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[102.682, -97.183], [-25.25, 30.813], [-89, -32.5], [-124.5, 3], [-25.562, 101.938], [138.182, -62.183]], "c": true}], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.180392156863, 0.160784313725, 0.235294117647, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 16, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}], "ip": 0, "op": 30, "st": 0, "ct": 1, "bm": 0}], "markers": [], "props": {}}