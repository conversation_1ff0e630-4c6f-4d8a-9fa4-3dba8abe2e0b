/**
 * Utilitários para tratamento de erros na aplicação de aulas
 */

export interface ErrorInfo {
  type: 'duplicate' | 'expired' | 'invalid_student' | 'inactive_student' | 'invalid_qr' | 'auth' | 'permission' | 'network' | 'not_found' | 'generic';
  message: string;
  meta?: any;
  toastConfig: {
    type: 'info' | 'warning' | 'error';
    duration: number;
    description: string;
  };
}

/**
 * Processa erros de check-in e retorna informações estruturadas
 */
export function processCheckInError(result: any): ErrorInfo {
  // Verificar se é erro de check-in duplicado
  const isDuplicateCheckin = result.errors && (
    (result.errors as any).student_check_in_code?.includes('já fez check-in') ||
    (result.errors as any).student_check_in_code?.includes('já possui check-in') ||
    (result.errors as any)._meta?.duplicate_checkin
  );

  if (isDuplicateCheckin) {
    const rawError = typeof result.errors === 'object' && result.errors && 'student_check_in_code' in result.errors 
      ? result.errors.student_check_in_code 
      : 'Aluno já fez check-in nesta aula';
    
    const errorMessage = typeof rawError === 'string' ? rawError : 'Aluno já fez check-in nesta aula';
    const metadata = (result.errors as any)?._meta;
    const isSelfCheckIn = metadata?.is_self_checkin === true;
    const studentName = metadata?.student_name || 'O aluno';

    return {
      type: 'duplicate',
      message: errorMessage,
      meta: {
        ...metadata,
        duplicate_checkin: true
      },
      toastConfig: {
        type: isSelfCheckIn ? 'info' : 'warning',
        duration: 6000,
        description: isSelfCheckIn 
          ? 'Sua presença já foi registrada para esta aula'
          : `${studentName} já tem presença confirmada`
      }
    };
  }

  // Verificar se é erro de QR Code expirado
  const isExpiredQR = result.errors && (
    (result.errors as any).qr_code?.includes('expirou') ||
    (result.errors as any).qr_code?.includes('expired') ||
    (result.errors as any)._meta?.expired_at
  );

  if (isExpiredQR) {
    const errorMessage = typeof result.errors === 'object' && result.errors && 'qr_code' in result.errors 
      ? result.errors.qr_code 
      : 'QR Code expirado';
    
    return {
      type: 'expired',
      message: typeof errorMessage === 'string' ? errorMessage : 'QR Code expirado',
      meta: (result.errors as any)?._meta,
      toastConfig: {
        type: 'error',
        duration: 8000,
        description: 'Gere um novo QR Code para continuar com os check-ins'
      }
    };
  }

  // Verificar se é erro de código de aluno inválido
  const isInvalidStudentCode = result.errors && (
    (result.errors as any).student_check_in_code?.includes('inválido') ||
    (result.errors as any).student_check_in_code?.includes('não encontrado')
  );

  if (isInvalidStudentCode) {
    const errorMessage = typeof result.errors === 'object' && result.errors && 'student_check_in_code' in result.errors 
      ? result.errors.student_check_in_code 
      : 'Código de aluno inválido';
    
    return {
      type: 'invalid_student',
      message: typeof errorMessage === 'string' ? errorMessage : 'Código de aluno inválido',
      meta: (result.errors as any)?._meta,
      toastConfig: {
        type: 'error',
        duration: 5000,
        description: 'Verifique se o código foi digitado corretamente'
      }
    };
  }

  // Verificar se é erro de aluno inativo
  const isInactiveStudent = result.errors && (
    (result.errors as any).student_check_in_code?.includes('inativo') ||
    (result.errors as any).student_check_in_code?.includes('desativado')
  );

  if (isInactiveStudent) {
    const errorMessage = typeof result.errors === 'object' && result.errors && 'student_check_in_code' in result.errors 
      ? result.errors.student_check_in_code 
      : 'Aluno inativo';
    
    return {
      type: 'inactive_student',
      message: typeof errorMessage === 'string' ? errorMessage : 'Aluno inativo',
      meta: (result.errors as any)?._meta,
      toastConfig: {
        type: 'error',
        duration: 6000,
        description: 'Entre em contato com a administração para reativar o aluno'
      }
    };
  }

  // Verificar se é erro de QR Code inválido
  const isInvalidQR = result.errors && (
    (result.errors as any).qr_code?.includes('inválido') ||
    (result.errors as any).qr_code?.includes('não encontrada')
  );

  if (isInvalidQR) {
    const errorMessage = typeof result.errors === 'object' && result.errors && 'qr_code' in result.errors 
      ? result.errors.qr_code 
      : 'QR Code inválido';
    
    return {
      type: 'invalid_qr',
      message: typeof errorMessage === 'string' ? errorMessage : 'QR Code inválido',
      meta: (result.errors as any)?._meta,
      toastConfig: {
        type: 'error',
        duration: 5000,
        description: 'Verifique se o QR Code foi copiado corretamente'
      }
    };
  }

  // Erro genérico
  const genericError = typeof result.errors === 'object' && result.errors && '_form' in result.errors 
    ? result.errors._form 
    : 'Erro ao fazer check-in';
  
  return {
    type: 'generic',
    message: typeof genericError === 'string' ? genericError : 'Erro ao fazer check-in',
    meta: (result.errors as any)?._meta,
    toastConfig: {
      type: 'error',
      duration: 5000,
      description: 'Tente novamente ou entre em contato com o suporte'
    }
  };
}

/**
 * Formata mensagens de erro de forma mais amigável
 */
export function formatErrorMessage(errors: any): string {
  if (typeof errors === 'string') {
    return errors;
  }
  
  if (errors?._form) {
    return errors._form;
  }
  
  // Verificar se é erro de autenticação
  if (errors?.message?.includes('não autenticado') || errors?.message?.includes('Usuário não autenticado')) {
    return 'Sessão expirada. Faça login novamente para continuar.';
  }
  
  // Verificar se é erro de permissão
  if (errors?.message?.includes('não autorizado') || errors?.message?.includes('sem permissão')) {
    return 'Você não tem permissão para acessar estes dados.';
  }
  
  // Verificar se é erro de conexão/rede
  if (errors?.message?.includes('fetch') || errors?.message?.includes('network') || errors?.message?.includes('conexão')) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }
  
  // Verificar se é erro de dados não encontrados
  if (errors?.message?.includes('não encontrado') || errors?.message?.includes('not found')) {
    return 'Os dados solicitados não foram encontrados.';
  }
  
  // Se for um objeto de erros do Zod, pegar a primeira mensagem de erro
  if (typeof errors === 'object' && errors !== null) {
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey && errors[firstErrorKey]) {
      const errorValue = errors[firstErrorKey];
      if (typeof errorValue === 'string') {
        return errorValue;
      }
      if (errorValue?._errors && Array.isArray(errorValue._errors) && errorValue._errors.length > 0) {
        return errorValue._errors[0];
      }
      if (errorValue?.message) {
        return errorValue.message;
      }
    }
  }
  
  // Erro genérico mais amigável
  return 'Ocorreu um erro inesperado. Tente novamente ou entre em contato com o suporte.';
}

/**
 * Determina o tipo de erro baseado na mensagem e metadata
 */
export function getErrorType(error: string, meta?: any): ErrorInfo['type'] {
  if (meta?.duplicate_checkin) return 'duplicate';
  if (meta?.expired_at || error.includes('expirou') || error.includes('expired')) return 'expired';
  if (error.includes('não autenticado') || error.includes('Usuário não autenticado')) return 'auth';
  if (error.includes('não autorizado') || error.includes('sem permissão')) return 'permission';
  if (error.includes('fetch') || error.includes('network') || error.includes('conexão')) return 'network';
  if (error.includes('não encontrado') || error.includes('not found')) return 'not_found';
  if (error.includes('inválido') && error.includes('código')) return 'invalid_student';
  if (error.includes('inativo') || error.includes('desativado')) return 'inactive_student';
  if (error.includes('QR') && error.includes('inválido')) return 'invalid_qr';
  return 'generic';
}

/**
 * Gera dicas específicas para cada tipo de erro
 */
export function getErrorTip(type: ErrorInfo['type'], meta?: any) {
  switch (type) {
    case 'duplicate':
      return {
        title: 'ℹ️ Informação: Check-in já confirmado',
        content: meta?.is_self_checkin 
          ? 'Você já possui presença registrada nesta aula. Cada aluno pode fazer check-in apenas uma vez por aula.'
          : `${meta?.student_name || 'O aluno'} já possui presença registrada nesta aula. Cada aluno pode fazer check-in apenas uma vez por aula.`
      };
    case 'expired':
      return {
        title: '💡 Dica: QR Codes expiram após 1 hora para segurança',
        content: 'Gere um novo código para continuar com os check-ins. Isso garante que apenas códigos recentes sejam utilizados.'
      };
    case 'invalid_student':
      return {
        title: '🔍 Verificação: Código do aluno',
        content: 'Verifique se o código foi digitado corretamente. O código deve ter exatamente 6 caracteres alfanuméricos.'
      };
    case 'inactive_student':
      return {
        title: '⚠️ Atenção: Aluno inativo',
        content: 'Este aluno está marcado como inativo no sistema. Entre em contato com a administração para reativar o cadastro.'
      };
    case 'invalid_qr':
      return {
        title: '📱 Verificação: QR Code',
        content: 'Verifique se o QR Code foi copiado corretamente ou se não foi modificado. Tente gerar um novo código.'
      };
    case 'auth':
      return {
        title: '🔐 Autenticação: Sessão expirada',
        content: 'Sua sessão expirou por segurança. Faça login novamente para continuar usando o sistema.'
      };
    case 'permission':
      return {
        title: '🚫 Permissão: Acesso negado',
        content: 'Você não tem permissão para realizar esta ação. Entre em contato com o administrador se necessário.'
      };
    case 'network':
      return {
        title: '🌐 Conexão: Problema de rede',
        content: 'Verifique sua conexão com a internet e tente novamente. Se o problema persistir, entre em contato com o suporte.'
      };
    case 'not_found':
      return {
        title: '🔍 Dados: Não encontrado',
        content: 'Os dados solicitados não foram encontrados. Verifique se as informações estão corretas.'
      };
    default:
      return {
        title: '🛠️ Suporte: Erro inesperado',
        content: 'Se o problema persistir, entre em contato com o suporte técnico ou tente novamente em alguns minutos.'
      };
  }
} 