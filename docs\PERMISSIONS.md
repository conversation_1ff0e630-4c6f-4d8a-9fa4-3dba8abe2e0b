# Sistema de Permissões - ApexSaaS

Este módulo implementa um sistema de permissões baseado em capacidades e condições, que controla o acesso a diferentes recursos e ações no sistema.

## Visão Geral

O sistema de permissões é baseado em três conceitos principais:

1. **Recursos** - Entidades do sistema como `user`, `profile`, `attendance`, etc.
2. **Ações** - Operações que podem ser realizadas em recursos, como `view`, `edit`, `delete`, etc.
3. **Condições** - Restrições aplicadas às permissões, como `self`, `not_self`, `same_tenant`, etc.

## Estrutura de Diretórios

```
permissions/
  ├── actions/           # Lógica de avaliação de permissões
  ├── components/        # Componentes React para UI (PermissionGate, PermissionButton)
  ├── contexts/          # Resolvedores de contexto para avaliação de permissões
  ├── hooks/             # Hooks React para verificar permissões no cliente
  ├── policies/          # Definições de políticas de permissão por papel
  ├── repository/        # Gerenciamento e armazenamento de políticas
  ├── types/             # Definições de tipos para o sistema
  ├── index.ts           # Exportações públicas do módulo
  ├── service.ts         # Serviço principal de permissões
  └── README.md          # Documentação
```

## Como Usar

### Verificação no lado do servidor

```typescript
import { getPermissionService } from '@/services/permissions';

// Em uma função de servidor/API
async function verificarPermissao(userId: string, targetId: string) {
  const permissionService = getPermissionService();
  
  const result = await permissionService.hasPermission(
    userId,
    'profile',
    'view',
    targetId
  );
  
  if (!result.granted) {
    console.log(`Acesso negado: ${result.reason}`);
  }
}

async function protegerRota(userId: string) {
  const permissionService = getPermissionService();
  
  return permissionService.protectRoute(
    userId,
    'user',
    'list',
    undefined,
    '/home?erro=acesso-negado'
  );
}
```

### Componentes de UI

```tsx
import { PermissionGate, PermissionButton } from '@/services/permissions';

function MeuComponente({ userId }) {
  return (
    <div>
      <PermissionGate 
        resource="profile" 
        action="edit" 
        targetId={userId}
      >
        <div>Este conteúdo só é visível para usuários com permissão</div>
      </PermissionGate>
      
      <PermissionButton
        resource="user"
        action="deactivate"
        targetId={userId}
        variant="destructive"
        onClick={handleDeactivate}
      >
        Desativar Usuário
      </PermissionButton>
    </div>
  );
}
```

### Hooks para verificação no cliente

```tsx
import { usePermission } from '@/services/permissions';

function MeuComponente({ userId }) {
  const { isAllowed, reason, isLoading } = usePermission(
    'profile',
    'edit',
    userId
  );
  
  if (isLoading) {
    return <div>Carregando...</div>;
  }
  
  return (
    <div>
      {isAllowed ? (
        <button>Editar Perfil</button>
      ) : (
        <p>Sem permissão: {reason}</p>
      )}
    </div>
  );
}
```

## Políticas de Permissão

As políticas definem quais permissões cada papel de usuário possui. Políticas padrão são definidas para:

- `admin` - Administradores do sistema
- `instructor` - Instrutores
- `teacher` - Professores
- `student` - Alunos

Para modificar ou estender as políticas, edite o arquivo `policies/default-policies.ts`.

## Regras Especiais

O sistema inclui regras específicas para casos comuns:

1. Usuários não podem desativar suas próprias contas (mesmo administradores)
2. Instrutores só podem ver perfis de estudantes
3. Estudantes só podem ver seus próprios perfis e dados

## Condições de Permissão

- `any` - Sem restrições adicionais
- `self` - Apenas para recursos do próprio usuário
- `not_self` - Qualquer usuário exceto o próprio
- `same_tenant` - Apenas recursos do mesmo tenant
- `students_only` - Apenas para recursos de estudantes
- `others_only` - Apenas para recursos que não pertencem ao usuário 