import { Checkbox } from "@/components/ui/checkbox"
import { User } from "./types"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Belt, BeltColor, beltColorTranslation } from "@/components/belt"
import { motion, AnimatePresence } from "framer-motion"
import { RowActionAnimation } from "../list/row-action-animation"
import { useRowActionAnimation } from "../../hooks/use-row-action-animation"

interface UserRowProps {
  user: User
  selected: boolean
  showCheckbox: boolean
  onSelect: (user: User) => void
}

export function UserRow({ user, selected, showCheckbox, onSelect }: UserRowProps) {
  // Hook para animações de ação nas linhas
  const {
    isUserAnimating,
    getUserAction
  } = useRowActionAnimation()

  // Função para gerar as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <motion.div 
      layout
      initial={{ opacity: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className="relative flex items-center gap-4 p-4 hover:bg-gray-50 dark:hover:bg-gray-900"
    >
      {/* Animação de ação sobrepondo a linha */}
      <AnimatePresence>
        {isUserAnimating(user.id) && (
          <RowActionAnimation
            action={getUserAction(user.id) || 'generic'}
            isVisible={isUserAnimating(user.id)}
          />
        )}
      </AnimatePresence>

      {showCheckbox && (
        <Checkbox
          checked={selected}
          disabled={isUserAnimating(user.id)}
          onCheckedChange={() => {
            if (!isUserAnimating(user.id)) {
              onSelect(user)
            }
          }}
          className="border-gray-300 dark:border-gray-500"
        />
      )}
      
      <Avatar className="h-8 w-8">
        <AvatarImage src={user.avatar || ""} alt={user.name} />
        <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
      </Avatar>
      
      <div className="flex-1">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          {user.name}
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {user.email}
        </p>
      </div>

      {user.belt && (
        <div className="flex items-center gap-2">
          <Belt color={user.belt as BeltColor} />
          <span className="text-xs text-muted-foreground">
            {beltColorTranslation[user.belt as BeltColor]} {user.degree ? `(${user.degree}° grau)` : ''}
          </span>
        </div>
      )}

      <Badge 
        variant={
          user.financialStatus === "up_to_date" ? "default" : 
          user.financialStatus === "overdue" ? "destructive" : 
          "outline"
        }
        className="capitalize"
      >
        {user.financialStatus === "up_to_date" ? "Em dia" : 
         user.financialStatus === "overdue" ? "Atrasado" : 
         user.financialStatus === "pending" ? "Pendente" :
         "Isento"}
      </Badge>
    </motion.div>
  )
} 