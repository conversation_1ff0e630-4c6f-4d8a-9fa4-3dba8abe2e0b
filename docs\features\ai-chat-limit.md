# Sistema de Limite de Mensagens do Chat IA

## Visão Geral

O sistema implementa um limite de **5 mensagens por dia** por usuário para o chat IA, utilizando o banco de dados Supabase para controle e rastreamento.

## Funcionalidades

### ✅ Controle de Limite
- Limite diário de 5 mensagens por usuário
- Contador reset automaticamente a cada dia
- Verificação de limite antes de processar mensagens
- Bloqueio de interface quando limite é atingido

### ✅ Interface de Usuário
- Indicador de uso no header do chat (ex: "3/5 mensagens hoje")
- Aviso quando restam poucas mensagens
- Bloqueio visual do input quando limite é atingido
- Placeholder dinâmico mostrando mensagens restantes

### ✅ Segurança
- Validação no servidor antes de processar
- Incremento atômico usando função PostgreSQL
- Associação com tenant para isolamento de dados

## Implementação Técnica

### Banco de Dados

**Tabela: `ai_chat_usage`**
```sql
CREATE TABLE ai_chat_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  tenant_id UUID NOT NULL,
  message_count INTEGER NOT NULL DEFAULT 0,
  usage_date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, usage_date)
);
```

**Função: `increment_ai_message_count`**
```sql
CREATE OR REPLACE FUNCTION increment_ai_message_count(
  p_user_id UUID,
  p_tenant_id UUID,
  p_usage_date DATE
) RETURNS VOID AS $$
BEGIN
  INSERT INTO ai_chat_usage (user_id, tenant_id, usage_date, message_count)
  VALUES (p_user_id, p_tenant_id, p_usage_date, 1)
  ON CONFLICT (user_id, usage_date)
  DO UPDATE SET 
    message_count = ai_chat_usage.message_count + 1,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### APIs

#### 1. Chat API (`/api/ai/chat`)
- **Método**: POST
- **Funcionalidade**: Processa mensagens com verificação de limite
- **Validação**: Verifica limite antes de chamar Gemini
- **Incremento**: Aumenta contador apenas após resposta bem-sucedida

#### 2. Usage API (`/api/ai/usage`)
- **Método**: GET
- **Funcionalidade**: Retorna estatísticas de uso do usuário
- **Resposta**: 
  ```json
  {
    "success": true,
    "usageStats": {
      "messagesUsed": 3,
      "messagesLimit": 5,
      "remainingMessages": 2,
      "canSendMessage": true,
      "resetDate": "2024-01-02"
    }
  }
  ```

### Serviços

#### `AIChatUsageLimitService`
- `canUserSendMessage()`: Verifica se usuário pode enviar mensagem
- `incrementMessageCount()`: Incrementa contador de forma atômica
- `getUserUsageStats()`: Retorna estatísticas detalhadas

#### `ChatService`
- `getUsageStats()`: Cliente para API de estatísticas
- Tipos atualizados com `UsageStats`

### Componentes React

#### `AIChat`
- Carrega estatísticas ao abrir
- Mostra contador de uso no header
- Aviso quando próximo do limite
- Bloqueia interface quando limite atingido
- Atualiza estatísticas após cada mensagem

## Estados da Interface

### 1. Normal (dentro do limite)
```
[IA Icon] Assistente IA
          Como posso ajudar?
          ℹ️ 2/5 mensagens hoje
```

### 2. Próximo do limite (1 mensagem restante)
```
⚠️ Última mensagem disponível hoje
```

### 3. Limite atingido
```
⚠️ Limite diário atingido
   Volte amanhã para fazer mais perguntas

[Input desabilitado]
```

## Códigos de Erro

### API Responses

#### `DAILY_LIMIT_EXCEEDED` (429)
```json
{
  "error": "Limite diário de 5 mensagens atingido. Tente novamente amanhã.",
  "code": "DAILY_LIMIT_EXCEEDED",
  "usageStats": {
    "messagesUsed": 5,
    "messagesLimit": 5,
    "remainingMessages": 0,
    "canSendMessage": false,
    "resetDate": "2024-01-02"
  }
}
```

## Configuração

### Variáveis de Ambiente
```env
# Configuração do Supabase (já existente)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# API do Gemini (já existente)
GEMINI_API_KEY=your_gemini_api_key
```

### Constantes
O limite pode ser alterado em:
```typescript
// src/services/ai/usage-limit-service.ts
private static readonly DAILY_MESSAGE_LIMIT = 5;
```

## Testando o Sistema

### 1. Teste Manual
1. Faça login na aplicação
2. Abra o chat IA (ícone sparkles no menu)
3. Envie mensagens até atingir o limite
4. Observe as mudanças na interface

### 2. Teste via API
```bash
# Verificar estatísticas de uso
curl -X GET http://localhost:3000/api/ai/usage \
  -H "Cookie: your-session-cookie"

# Tentar enviar mensagem
curl -X POST http://localhost:3000/api/ai/chat \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"message": "Olá, como você pode me ajudar?"}'
```

### 3. Reset do Contador (Dev/Test)
Para resetar o contador durante desenvolvimento:
```sql
-- Via Supabase Dashboard ou psql
DELETE FROM ai_chat_usage WHERE user_id = 'seu-user-id';
```

## Monitoramento

### Métricas Importantes
- Número de usuários atingindo o limite diário
- Distribuição de uso por dia da semana
- Tempo médio entre mensagens

### Queries Úteis
```sql
-- Usuários que atingiram o limite hoje
SELECT user_id, message_count 
FROM ai_chat_usage 
WHERE usage_date = CURRENT_DATE AND message_count >= 5;

-- Estatísticas gerais de uso
SELECT 
  usage_date,
  COUNT(*) as users_active,
  AVG(message_count) as avg_messages,
  SUM(message_count) as total_messages
FROM ai_chat_usage 
GROUP BY usage_date 
ORDER BY usage_date DESC;
```

## Limitações e Considerações

### ✅ Pontos Fortes
- Controle eficiente de custos com API do Gemini
- Interface clara e informativa
- Validação dupla (cliente + servidor)
- Reset automático diário

### ⚠️ Limitações
- Limite fixo (não configurável por tenant)
- Não diferencia tipos de mensagem
- Reset às 00:00 UTC (não por timezone do usuário)

### 🔮 Possíveis Melhorias Futuras
- Limite configurável por tenant
- Diferentes limites por role de usuário
- Histórico de mensagens com paginação
- Análise de sentimento para priorizar mensagens 