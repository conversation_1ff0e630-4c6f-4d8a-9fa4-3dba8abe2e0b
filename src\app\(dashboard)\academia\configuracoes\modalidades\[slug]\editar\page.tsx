import { requireAuth } from '@/services/auth/actions/auth-actions';
import { listModalities } from '@/services/modalities';
import { getModalitySettings } from '@/services/modalities/settings';
import { listGraduationLevels } from '@/services/belts/levels';
import { getModalityRequirements } from '@/services/belts/requirements';
import { redirect, notFound } from 'next/navigation';
import ModalityEditView from './components/ModalityEditView';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function EditModalityPage({ params }: PageProps) {
  // Await params before using its properties
  const { slug } = await params;
  const { user } = await requireAuth();
  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    redirect('/home?erro=tenant-nao-encontrado');
  }
  if (role !== 'admin') {
    redirect('/home?erro=acesso-negado');
  }

  // Buscar todas as modalidades para encontrar a correta pelo slug
  const modalities = await listModalities(tenantId);
  const modality = modalities.find(m => m.slug === slug);

  if (!modality) {
    notFound();
  }

  // Buscar configurações da modalidade para este tenant
  const modalitySettings = await getModalitySettings(tenantId, modality.id);

  // Buscar níveis de graduação da modalidade
  const graduationLevels = await listGraduationLevels(tenantId, modality.slug);

  // Buscar requisitos da modalidade usando RPC (mais eficiente)
  const modalityRequirements = await getModalityRequirements(tenantId, modality.id);

  // Combinar dados
  const levelsWithRequirements = graduationLevels.map(level => ({
    ...level,
    requirements: modalityRequirements.find(req => req.belt_level_id === level.id),
  }));

  return (
    <>
      <div className="mb-4">
        <Link href="/academia/configuracoes/modalidades">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar para Modalidades
          </Button>
        </Link>
      </div>
      <ModalityEditView
        modality={modality}
        modalitySettings={modalitySettings}
        levelsWithRequirements={levelsWithRequirements}
      />
    </>
  );
}