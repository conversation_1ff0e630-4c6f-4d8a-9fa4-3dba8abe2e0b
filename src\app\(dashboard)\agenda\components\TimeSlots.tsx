'use client'

import React from 'react'

interface TimeSlotsProps {
  timeSlots: Array<{ time: string; hour: number }>
}

export function TimeSlots({ timeSlots }: TimeSlotsProps) {
  const SLOT_HEIGHT = 48

  return (
    <div className="border-r border-border bg-muted/20">
      {timeSlots.map((slot) => (
        <div 
          key={slot.time}
          className="px-3 py-2 border-b border-border flex items-start justify-end"
          style={{ height: `${SLOT_HEIGHT}px` }}
        >
          <span className="text-xs text-muted-foreground font-medium">
            {slot.time}
          </span>
        </div>
      ))}
    </div>
  )
} 