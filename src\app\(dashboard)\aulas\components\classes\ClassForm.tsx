'use client';

import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Loader2, Save, X } from 'lucide-react';
import { createClass } from '../../actions';
import { type CreateClass } from '../../actions/schemas';
import { toast } from 'sonner';

// Interface específica para o formulário
interface ClassFormData {
  title: string;
  description?: string;
  instructor_id: string;
  branch_id: string;
  location?: string;
  start_time: string;
  end_time: string;
  max_capacity?: number | string;
  class_type: 'regular' | 'free' | 'workshop' | 'exam';
  enrollment_type: string;
  waitlist_enabled: boolean;
  check_in_method: 'manual' | 'qr_code' | 'both';
}

interface ClassFormProps {
  mode: 'create' | 'edit';
  classType?: 'regular' | 'free' | 'workshop' | 'exam';
  instructors: Array<{ id: string; name: string; }>;
  branches: Array<{ id: string; name: string; }>;
  redirectPath?: string;
  initialData?: Partial<ClassFormData>;
}

export function ClassForm({ 
  mode, 
  classType = 'free',
  instructors, 
  branches,
  redirectPath = '/aulas',
  initialData 
}: ClassFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { isSubmitting }
  } = useForm<ClassFormData>({
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      instructor_id: initialData?.instructor_id || '',
      branch_id: initialData?.branch_id || '',
      location: initialData?.location || '',
      start_time: initialData?.start_time || '',
      end_time: initialData?.end_time || '',
      max_capacity: initialData?.max_capacity || '',
      class_type: classType,
      enrollment_type: 'open',
      waitlist_enabled: initialData?.waitlist_enabled || false,
      check_in_method: initialData?.check_in_method || 'manual',
      ...initialData
    }
  });

  const watchedValues = watch();

  const onSubmit = async (data: ClassFormData) => {
    startTransition(async () => {
      try {
        setErrors({});

        // Validações básicas no frontend
        if (!data.title.trim()) {
          setErrors(prev => ({ ...prev, title: 'Título é obrigatório' }));
          return;
        }
        
        if (!data.instructor_id) {
          setErrors(prev => ({ ...prev, instructor_id: 'Instrutor é obrigatório' }));
          return;
        }
        
        if (!data.branch_id) {
          setErrors(prev => ({ ...prev, branch_id: 'Filial é obrigatória' }));
          return;
        }
        
        if (!data.start_time) {
          setErrors(prev => ({ ...prev, start_time: 'Data e hora de início são obrigatórias' }));
          return;
        }
        
        if (!data.end_time) {
          setErrors(prev => ({ ...prev, end_time: 'Data e hora de término são obrigatórias' }));
          return;
        }

        // Verificar se a data de fim é posterior à de início
        const startDate = new Date(data.start_time);
        const endDate = new Date(data.end_time);
        
        if (endDate <= startDate) {
          setErrors(prev => ({ ...prev, end_time: 'Horário final deve ser posterior ao horário inicial' }));
          return;
        }

        // Transform form data to match schema
        const transformedData: CreateClass = {
          name: data.title.trim(),
          description: data.description?.trim() || undefined,
          instructor_id: data.instructor_id,
          branch_id: data.branch_id,
          start_time: data.start_time,
          end_time: data.end_time,
          max_capacity: data.max_capacity && data.max_capacity !== '' 
            ? Number(data.max_capacity) 
            : undefined,
          recurring: false,
          status: 'scheduled' as const,
          attendance_recorded: false,
        };

        // Verificar se max_capacity é um número válido se fornecido
        if (transformedData.max_capacity !== undefined && (isNaN(transformedData.max_capacity) || transformedData.max_capacity < 1)) {
          setErrors(prev => ({ ...prev, max_capacity: 'Capacidade deve ser um número maior que 0' }));
          return;
        }

        const result = await createClass(transformedData);

        if (!result.success) {
          if (result.errors) {
            const formattedErrors: Record<string, string> = {};
            Object.entries(result.errors).forEach(([key, value]) => {
              if (key === '_form') {
                formattedErrors._form = value as string;
              } else if (typeof value === 'object' && value !== null && '_errors' in value) {
                formattedErrors[key] = (value._errors as string[])[0];
              }
            });
            setErrors(formattedErrors);
          }
          
          toast.error('Erro ao salvar aula');
          return;
        }

        toast.success(mode === 'create' ? 'Aula criada com sucesso!' : 'Aula atualizada com sucesso!');
        router.push(redirectPath);
      } catch (error) {
        console.error('Erro no formulário:', error);
        toast.error('Erro inesperado ao salvar aula');
      }
    });
  };

  const getClassTypeLabel = (type: string) => {
    const labels = {
      'free': 'Aula Livre',
      'workshop': 'Workshop',
      'exam': 'Exame',
      'seminar': 'Seminário'
    };
    return labels[type as keyof typeof labels] || 'Aula Livre';
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Informações Básicas */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="title" className="text-foreground">Título da Aula *</Label>
          <Input
            id="title"
            {...register('title')}
            placeholder="Ex: Workshop de Defesa Pessoal"
            className={errors.title ? 'border-destructive' : ''}
          />
          {errors.title && (
            <p className="text-sm text-destructive">{errors.title}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="class_type" className="text-foreground">Tipo de Aula</Label>
          <Select 
            value={watchedValues.class_type} 
            onValueChange={(value) => setValue('class_type', value as any)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione o tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="free">Aula Livre</SelectItem>
              <SelectItem value="workshop">Workshop</SelectItem>
              <SelectItem value="exam">Exame</SelectItem>
              <SelectItem value="seminar">Seminário</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description" className="text-foreground">Descrição</Label>
        <Textarea
          id="description"
          {...register('description')}
          placeholder="Descreva o conteúdo e objetivos da aula..."
          rows={3}
          className={errors.description ? 'border-destructive' : ''}
        />
        {errors.description && (
          <p className="text-sm text-destructive">{errors.description}</p>
        )}
      </div>

      {/* Instrutor e Local */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="instructor_id" className="text-foreground">Instrutor *</Label>
          <Select 
            value={watchedValues.instructor_id} 
            onValueChange={(value) => setValue('instructor_id', value)}
          >
            <SelectTrigger className={errors.instructor_id ? 'border-destructive' : ''}>
              <SelectValue placeholder="Selecione o instrutor" />
            </SelectTrigger>
            <SelectContent>
              {instructors.map(instructor => (
                <SelectItem key={instructor.id} value={instructor.id}>
                  {instructor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.instructor_id && (
            <p className="text-sm text-destructive">{errors.instructor_id}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="branch_id" className="text-foreground">Filial *</Label>
          <Select 
            value={watchedValues.branch_id} 
            onValueChange={(value) => setValue('branch_id', value)}
          >
            <SelectTrigger className={errors.branch_id ? 'border-destructive' : ''}>
              <SelectValue placeholder="Selecione a filial" />
            </SelectTrigger>
            <SelectContent>
              {branches.map(branch => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.branch_id && (
            <p className="text-sm text-destructive">{errors.branch_id}</p>
          )}
        </div>
      </div>

      {/* <div className="space-y-2">
        <Label htmlFor="location" className="text-foreground">Local</Label>
        <Input
          id="location"
          {...register('location')}
          placeholder="Ex: Tatami 1, Sala A, etc."
          className={errors.location ? 'border-destructive' : ''}
        />
        {errors.location && (
          <p className="text-sm text-destructive">{errors.location}</p>
        )}
      </div> */}

      {/* Data e Horário */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="start_time" className="text-foreground">Data e Hora de Início *</Label>
          <Input
            id="start_time"
            type="datetime-local"
            {...register('start_time')}
            className={errors.start_time ? 'border-destructive' : ''}
          />
          {errors.start_time && (
            <p className="text-sm text-destructive">{errors.start_time}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_time" className="text-foreground">Data e Hora de Término *</Label>
          <Input
            id="end_time"
            type="datetime-local"
            {...register('end_time')}
            className={errors.end_time ? 'border-destructive' : ''}
          />
          {errors.end_time && (
            <p className="text-sm text-destructive">{errors.end_time}</p>
          )}
        </div>
      </div>

      {/* Configurações */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="max_capacity" className="text-foreground">Capacidade Máxima</Label>
          <Input
            id="max_capacity"
            type="number"
            min="1"
            max="100"
            {...register('max_capacity')}
            placeholder="Ex: 20 (deixe vazio para ilimitado)"
            className={errors.max_capacity ? 'border-destructive' : ''}
          />
          {errors.max_capacity && (
            <p className="text-sm text-destructive">{errors.max_capacity}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="check_in_method" className="text-foreground">Método de Check-in</Label>
          <Select 
            value={watchedValues.check_in_method} 
            onValueChange={(value) => setValue('check_in_method', value as any)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione o método" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="manual">Manual</SelectItem>
              <SelectItem value="qr_code">QR Code</SelectItem>
              <SelectItem value="both">Ambos</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Opções Avançadas */}
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Switch
            id="waitlist_enabled"
            checked={watchedValues.waitlist_enabled}
            onCheckedChange={(checked) => setValue('waitlist_enabled', checked)}
          />
          <div>
            <Label htmlFor="waitlist_enabled" className="cursor-pointer text-foreground">
              Ativar Lista de Espera
            </Label>
            <p className="text-sm text-muted-foreground">
              Permitir inscrições em lista de espera quando a aula estiver lotada
            </p>
          </div>
        </div>
      </div>

      {/* Erro geral */}
      {errors._form && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
          {errors._form}
        </div>
      )}

      {/* Botões */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-border">
        <Button 
          type="button" 
          variant="outline"
          onClick={() => router.push(redirectPath)}
          disabled={isPending || isSubmitting}
        >
          <X className="h-4 w-4 mr-2" />
          Cancelar
        </Button>
        <Button 
          type="submit" 
          disabled={isPending || isSubmitting}
        >
          {(isPending || isSubmitting) && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          <Save className="h-4 w-4 mr-2" />
          {mode === 'create' ? 'Criar Aula' : 'Salvar Alterações'}
        </Button>
      </div>
    </form>
  );
} 