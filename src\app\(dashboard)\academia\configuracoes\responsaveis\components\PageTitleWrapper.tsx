'use client';

import { useEffect } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';
import { Users } from 'lucide-react';

export function PageTitleWrapper() {
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle();

  useEffect(() => {
    setPageTitle('Gerenciar Responsáveis');
    setPageSubtitle('Configurar responsáveis legais para estudantes menores de idade');
    setPageIcon(<Users className="h-6 w-6 text-primary" />);

    return () => {
      setPageTitle(null);
      setPageSubtitle(null);
      setPageIcon(null);
    };
  }, [setPageTitle, setPageSubtitle, setPageIcon]);

  return null;
} 