{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=rfvzmrfzxlkyxgbmyptw"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "supabase-fake": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres.rfvzmrfzxlkyxgbmyptw:<EMAIL>:5432/postgres"]}, "@21st-dev/magic": {"command": "cmd", "args": ["/c", "npx", "-y", "@21st-dev/magic@latest", "API_KEY=\"426dddf0a978350e1ba04160fd1b1062391440f20d2637aa609bb2c7cf9e61e5\""]}, "browser-tools": {"command": "cmd", "args": ["bash", "-c", "cmd /c npx -y @agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true}, "exa": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "exa", "--key", "a40dca22-454d-434e-9947-594b0d5dc707"]}, "mcp-taskmanager": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@kazuph/mcp-taskmanager", "--key", "a40dca22-454d-434e-9947-594b0d5dc707"]}, "Sequential Thinking Tools": {"type": "stdio", "command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@xinzhongyouhai/mcp-sequentialthinking-tools", "--key", "64fe05f8-2b0f-49d1-9e17-1c36222fcf68"], "env": {}}, "context7": {"url": "https://mcp.context7.com/mcp"}}}