"use client";

/**
 * Componente de Gráfico de Projeções de Fluxo de Caixa - Fase 5
 * Exibe projeções futuras de fluxo de caixa em formato de linha
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, ReferenceLine } from 'recharts';
import { TrendingUp, Calendar, Target, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getCashFlowProjections } from '../../actions/metrics/cashflow-actions';
import { CashFlowProjectionData, DateRange } from '../../types/dashboard-types';
import { formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface CashFlowProjectionChartProps {
  dateRange: DateRange;
  monthsToProject?: number;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const netFlow = payload.find(p => p.dataKey === 'projectedNetFlow');
    const confidence = payload.find(p => p.dataKey === 'confidence');

    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          {label}
        </p>
        {netFlow && (
          <p className={cn(
            "text-sm font-medium",
            netFlow.value >= 0 
              ? "text-emerald-600 dark:text-emerald-400" 
              : "text-red-600 dark:text-red-400"
          )}>
            <span>Fluxo Projetado: </span>
            {formatCurrency(netFlow.value)}
          </p>
        )}
        {confidence && (
          <p className="text-sm text-blue-600 dark:text-blue-400">
            <span className="font-medium">Confiança: </span>
            {confidence.value.toFixed(0)}%
          </p>
        )}
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const CashFlowProjectionChart: React.FC<CashFlowProjectionChartProps> = ({
  dateRange,
  monthsToProject = 6,
  className
}) => {
  const [data, setData] = useState<CashFlowProjectionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar dados
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getCashFlowProjections(dateRange, monthsToProject);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar projeções');
        }

        // Formatar dados para o gráfico
        const chartData = (result.data || []).map(item => ({
          ...item,
          month: new Date(item.month + '-01').toLocaleDateString('pt-BR', { 
            month: 'short', 
            year: '2-digit' 
          })
        }));

        setData(chartData);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar projeções de fluxo de caixa:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateRange, monthsToProject]);

  // Calcular estatísticas
  const averageProjection = data.length > 0 
    ? data.reduce((sum, item) => sum + item.projectedNetFlow, 0) / data.length 
    : 0;
  const averageConfidence = data.length > 0 
    ? data.reduce((sum, item) => sum + item.confidence, 0) / data.length 
    : 0;
  const positiveMonths = data.filter(item => item.projectedNetFlow > 0).length;

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Projeções de Fluxo de Caixa
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Próximos {monthsToProject} meses
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Target className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Resumo das Projeções */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <TrendingUp className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Média Projetada
              </span>
            </div>
            <p className={cn(
              "text-lg font-semibold",
              averageProjection >= 0 
                ? "text-emerald-600 dark:text-emerald-400" 
                : "text-red-600 dark:text-red-400"
            )}>
              {formatCurrency(averageProjection)}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <Calendar className="h-4 w-4 text-emerald-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Meses Positivos
              </span>
            </div>
            <p className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">
              {positiveMonths}/{data.length}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <AlertCircle className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Confiança Média
              </span>
            </div>
            <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {averageConfidence.toFixed(0)}%
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400 mb-2">Erro ao carregar projeções</p>
              <p className="text-sm text-gray-500">{error}</p>
            </div>
          </div>
        ) : data.length === 0 ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Dados insuficientes para gerar projeções
              </p>
            </div>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="month" 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    // Para valores grandes, usar formatação compacta
                    if (Math.abs(value) >= 1000) {
                      return new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL',
                        notation: 'compact',
                        maximumFractionDigits: 1
                      }).format(value);
                    }
                    return formatCurrency(value);
                  }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <ReferenceLine y={0} stroke="#6b7280" strokeDasharray="2 2" />
                <Line 
                  type="monotone"
                  dataKey="projectedNetFlow" 
                  name="Fluxo Projetado"
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Aviso sobre projeções */}
        {data.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Sobre as Projeções
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  As projeções são baseadas em dados históricos e tendências. 
                  A confiança diminui para períodos mais distantes.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
