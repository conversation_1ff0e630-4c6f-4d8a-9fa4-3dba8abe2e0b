'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Clock, 
  User, 
  MapPin, 
  Calendar,
  MoreHorizontal,
  UserCheck,
  Settings,
  Eye,
  Edit,
  QrCode,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { ClassWithDetails } from '../../types';
import { format, isAfter, isBefore, addHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';

interface ClassCardProps {
  class: ClassWithDetails;
  showGroupInfo?: boolean;
  onCancel?: (classId: string) => void;
  onReschedule?: (classId: string) => void;
}

export function ClassCard({ 
  class: classItem, 
  showGroupInfo = true,
  onCancel,
  onReschedule 
}: ClassCardProps) {
  const attendanceCount = classItem._count.attendance || 0;
  const attendanceRate = classItem.attendance_rate || 0;
  const maxCapacity = classItem.max_capacity || 0;
  
  const startTime = new Date(classItem.start_time);
  const endTime = new Date(classItem.end_time);
  const now = new Date();
  
  const isUpcoming = isAfter(startTime, now);
  const isOngoing = isBefore(startTime, now) && isAfter(endTime, now);
  const isPast = isBefore(endTime, now);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-500 dark:bg-blue-600';
      case 'ongoing': return 'bg-green-500 dark:bg-green-600';
      case 'completed': return 'bg-muted dark:bg-muted';
      case 'cancelled': return 'bg-red-500 dark:bg-red-600';
      case 'rescheduled': return 'bg-yellow-500 dark:bg-yellow-600';
      default: return 'bg-muted dark:bg-muted';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Agendada';
      case 'ongoing': return 'Em Andamento';
      case 'completed': return 'Concluída';
      case 'cancelled': return 'Cancelada';
      case 'rescheduled': return 'Reagendada';
      default: return 'Desconhecido';
    }
  };

  const getClassTypeColor = (type: string) => {
    switch (type) {
      case 'regular': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'free': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'workshop': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200';
      case 'exam': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200';
      default: return 'bg-muted dark:bg-muted text-muted-foreground';
    }
  };

  const getClassTypeLabel = (type: string) => {
    switch (type) {
      case 'regular': return 'Regular';
      case 'free': return 'Aula Livre';
      case 'workshop': return 'Workshop';
      case 'exam': return 'Exame';
      default: return 'Outro';
    }
  };

  const canCheckIn = isOngoing || (isUpcoming && isBefore(now, addHours(startTime, -0.5)));
  const canEdit = isUpcoming && classItem.status === 'scheduled';
  const canCancel = isUpcoming && classItem.status === 'scheduled';

  return (
    <Card className={`hover:shadow-lg transition-shadow border-border ${
      isOngoing ? 'ring-2 ring-green-500 dark:ring-green-400' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <div className="flex items-center space-x-2">
              <div 
                className={`w-2 h-2 rounded-full ${getStatusColor(classItem.status)}`} 
              />
              <CardTitle className="text-lg font-semibold line-clamp-1 text-foreground">
                {classItem.name}
              </CardTitle>
              {isOngoing && (
                <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-600 dark:border-green-400">
                  AO VIVO
                </Badge>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary" 
                className={`${getStatusColor(classItem.status).replace('bg-', 'bg-opacity-20 text-')} dark:bg-opacity-30`}
              >
                {getStatusLabel(classItem.status)}
              </Badge>
              
              {classItem.class_type && classItem.class_type !== 'regular' && (
                <Badge 
                  variant="secondary" 
                  className={getClassTypeColor(classItem.class_type)}
                >
                  {getClassTypeLabel(classItem.class_type)}
                </Badge>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/presenca/${classItem.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  Ver Detalhes
                </Link>
              </DropdownMenuItem>
              
              {canCheckIn && (
                <DropdownMenuItem asChild>
                  <Link href={`/presenca/${classItem.id}`}>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Registrar Presença
                  </Link>
                </DropdownMenuItem>
              )}

              {canCheckIn && (
                <DropdownMenuItem asChild>
                  <Link href={`/checkin/qr/${classItem.id}`}>
                    <QrCode className="mr-2 h-4 w-4" />
                    QR Code Check-in
                  </Link>
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              {canEdit && (
                <DropdownMenuItem asChild>
                  <Link href={
                    classItem.class_group_id === null 
                      ? `/aulas/livres/editar/${classItem.id}`
                      : `/aulas/editar/${classItem.id}`
                  }>
                    <Edit className="mr-2 h-4 w-4" />
                    Editar Aula
                  </Link>
                </DropdownMenuItem>
              )}

              {canCancel && onCancel && (
                <DropdownMenuItem 
                  onClick={() => onCancel(classItem.id)}
                  className="text-red-600 dark:text-red-400"
                >
                  <X className="mr-2 h-4 w-4" />
                  Cancelar Aula
                </DropdownMenuItem>
              )}

              {canEdit && onReschedule && (
                <DropdownMenuItem onClick={() => onReschedule(classItem.id)}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Reagendar
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              {/* <DropdownMenuItem asChild>
                <Link href={`/aulas/configurar/${classItem.id}`}>
                  <Settings className="mr-2 h-4 w-4" />
                  Configurações
                </Link>
              </DropdownMenuItem> */}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {classItem.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {classItem.description}
          </p>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Horário */}
        <div className="space-y-1">
          <div className="flex items-center space-x-2 text-sm">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium text-foreground">
              {format(startTime, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
            </span>
          </div>
          <div className="text-xs text-muted-foreground ml-6">
            Duração: {format(startTime, 'HH:mm')} - {format(endTime, 'HH:mm')}
          </div>
        </div>

        {/* Instrutor */}
        <div className="flex items-center space-x-2 text-sm">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium text-foreground">
            {classItem.instructor.full_name || 
             `${classItem.instructor.first_name} ${classItem.instructor.last_name || ''}`.trim()}
          </span>
        </div>

        {/* Filial */}
        <div className="flex items-center space-x-2 text-sm">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span className="text-foreground">{classItem.branch.name}</span>
        </div>

        {/* Turma (se existir) */}
        {showGroupInfo && classItem.class_group && (
          <div className="flex items-center space-x-2 text-sm">
            <Users className="h-4 w-4 text-muted-foreground" />
            <Link 
              href={`/turmas/${classItem.class_group.id}`}
              className="text-primary hover:underline font-medium"
            >
              {classItem.class_group.name}
            </Link>
          </div>
        )}

        {/* Presença */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-4 w-4 text-muted-foreground" />
              <span className="text-foreground">Presença</span>
            </div>
            <span className="font-medium text-foreground">
              {attendanceCount}
              {!classItem.class_group_id || !maxCapacity ? (
                <span className="text-muted-foreground ml-1">alunos</span>
              ) : (
                <>
                  {` / ${maxCapacity}`}
                  {attendanceRate > 0 && (
                    <span className="text-muted-foreground ml-1">
                      ({Math.round(attendanceRate)}%)
                    </span>
                  )}
                </>
              )}
            </span>
          </div>
          
          {maxCapacity > 0 && attendanceCount > 0 && classItem.class_group_id && (
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all ${
                  attendanceRate >= 90 ? 'bg-green-500 dark:bg-green-400' :
                  attendanceRate >= 75 ? 'bg-yellow-500 dark:bg-yellow-400' :
                  'bg-blue-500 dark:bg-blue-400'
                }`}
                style={{ width: `${Math.min((attendanceCount / maxCapacity) * 100, 100)}%` }}
              />
            </div>
          )}
        </div>

        {/* Observações */}
        {classItem.notes && (
          <div className="text-sm">
            <span className="text-muted-foreground">Obs:</span>
            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
              {classItem.notes}
            </p>
          </div>
        )}

        {/* Status de Check-in */}
        {canCheckIn && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
              <UserCheck className="h-4 w-4" />
              <span className="text-sm font-medium">
                Check-in disponível
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 