import { <PERSON>, CardContent, <PERSON>Footer, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Student } from "../../types";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Belt, beltColorTranslation, BeltWithDetails } from "@/components/belt";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { motion, AnimatePresence } from "framer-motion";
import { RowActionAnimation } from "./row-action-animation";
import { useRowActionAnimation } from "../../hooks/use-row-action-animation";
import { formatPhoneNumberDisplay, formatPhoneNumberBRWithDDI } from "@/utils/phone-utils";
import { getFinancialStatusLabel, getFinancialStatusBadgeVariant } from "../../utils/financial-status-utils";

interface AlunosCardProps {
  student: Student;
  students?: Student[];
  isSelecting?: boolean;
  isSelected?: boolean;
  onSelect?: () => void;
}

export function AlunosCard({ 
  student, 
  students, 
  isSelecting = false,
  isSelected = false,
  onSelect
}: AlunosCardProps) {
  // Hook para animações de ação nas linhas
  const {
    isUserAnimating,
    getUserAction
  } = useRowActionAnimation();
  // Função para gerar as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Formatar data relativa
  const formatRelativeDate = (dateStr: string) => {
    try {
      return formatDistanceToNow(new Date(dateStr), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (e) {
      return "Data inválida";
    }
  };

  // Calcular idade a partir da data de nascimento
  const calcularIdade = (dataNascimento: string | undefined) => {
    if (!dataNascimento) return "N/A";
    
    try {
      const hoje = new Date();
      const nascimento = new Date(dataNascimento);
      let idade = hoje.getFullYear() - nascimento.getFullYear();
      const m = hoje.getMonth() - nascimento.getMonth();
      
      if (m < 0 || (m === 0 && hoje.getDate() < nascimento.getDate())) {
        idade--;
      }
      
      return idade;
    } catch (e) {
      return "N/A";
    }
  };

  // Formatar data de nascimento
  const formatarDataNascimento = (dataNascimento: string | undefined) => {
    if (!dataNascimento) return "";
    
    try {
      const data = new Date(dataNascimento);
      return `${data.getDate().toString().padStart(2, '0')}/${(data.getMonth() + 1).toString().padStart(2, '0')}/${data.getFullYear()}`;
    } catch (e) {
      return "";
    }
  };

  // Função para obter o label do status
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      case 'suspended':
        return 'Suspenso';
      default:
        return 'Desconhecido';
    }
  };

  // Função para obter a variante do badge baseada no status
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'inactive':
        return 'secondary';
      case 'suspended':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Usar o status centralizado se disponível, senão usar isActive como fallback
  const status = (student as any).status || ((student as any).isActive ? 'active' : 'inactive');

  const renderBelt = (student: Student) => {
    return (
      <BeltWithDetails
        color={student.belt}
        degree={student.degree}
        label={student.beltDetails?.label}
        stripeColor={student.beltDetails?.stripe_color}
        showCenterLine={student.beltDetails?.show_center_line}
        centerLineColor={student.beltDetails?.center_line_color}
        size="md"
      />
    );
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className="relative"
    >
      <Card className="overflow-hidden">
        {/* Animação de ação sobrepondo o card */}
        <AnimatePresence>
          {isUserAnimating(student.id) && (
            <RowActionAnimation
              action={getUserAction(student.id) || 'generic'}
              isVisible={isUserAnimating(student.id)}
            />
          )}
        </AnimatePresence>
        
        <CardContent className="p-0">
          <div className="p-4">
            <div className="flex items-start gap-3">
              {isSelecting && (
                <div className="flex items-center h-12 pr-2">
                  <Checkbox 
                    checked={isSelected}
                    disabled={isUserAnimating(student.id)}
                    onCheckedChange={() => {
                      if (onSelect && !isUserAnimating(student.id)) {
                        onSelect();
                      }
                    }}
                    className="border-gray-300 dark:border-gray-500"
                  />
                </div>
              )}
            
            <Avatar className="h-12 w-12">
              <AvatarImage src={student.avatar || ""} alt={student.name} />
              <AvatarFallback>{getInitials(student.name)}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">
                  <Link href={`/perfil/${student.id}?from=alunos`} className="hover:underline">
                    {student.name}
                  </Link>
                </h3>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      className="h-8 w-8 p-0"
                      disabled={isUserAnimating(student.id)}
                    >
                      <span className="sr-only">Abrir menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/perfil/${student.id}?from=alunos`}>
                        Ver perfil
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              
              <div className="text-sm text-muted-foreground flex flex-col">
                <span>{student.email}</span>
                {student.phone && (
                  <span className="text-xs mt-0.5">{formatPhoneNumberBRWithDDI(student.phone)}</span>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-3">
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Status:</span>
                  <Badge variant={getStatusBadgeVariant(status)} className="capitalize">
                    {getStatusLabel(status)}
                  </Badge>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Idade:</span>
                  <span className="text-xs">{calcularIdade(student.birthDate)} anos</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Faixa:</span>
                  <div className="flex items-center gap-1">
                    {renderBelt(student)}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Financeiro:</span>
                  <Badge 
                    variant={getFinancialStatusBadgeVariant(student.financialStatus)}
                    className="capitalize text-xs"
                  >
                    {getFinancialStatusLabel(student.financialStatus)}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Última visita:</span>
                  <span className="text-xs">{student.lastVisit ? formatRelativeDate(student.lastVisit) : "Nunca"}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Plano:</span>
                  {student.enrollmentPlan ? (
                    <Badge variant="outline" className="capitalize text-xs">
                      {student.enrollmentPlan.title}
                    </Badge>
                  ) : (
                    <span className="text-xs text-muted-foreground">Sem plano</span>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Filial:</span>
                  <Badge variant="secondary" className="capitalize text-xs">
                    {student.branch || "Não informado"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    </motion.div>
  );
}