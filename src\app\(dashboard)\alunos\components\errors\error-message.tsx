'use client';

import { useSearchParams } from "next/navigation";

export function StudentErrorMessage() {
  const searchParams = useSearchParams();
  const erro = searchParams.get('erro');
  
  if (!erro) {
    return null;
  }
  
  let message = "Ocorreu um erro ao processar sua solicitação.";
  
  // Mapeamento de códigos de erro para mensagens amigáveis
  if (erro === 'sem-permissao-criar') {
    message = "Você não tem permissão para criar novos alunos.";
  } else if (erro === 'sem-permissao-editar') {
    message = "Você não tem permissão para editar dados de alunos.";
  } else if (erro === 'sem-permissao-excluir') {
    message = "Você não tem permissão para excluir alunos.";
  }
  
  return (
    <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 mb-6">
      <div className="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-600" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span className="font-medium">{message}</span>
      </div>
    </div>
  );
} 