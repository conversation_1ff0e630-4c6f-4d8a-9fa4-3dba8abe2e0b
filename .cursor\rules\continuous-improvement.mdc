---
description: 
globs: 
alwaysApply: true
---
# Análise Crítica e Melhoria Contínua

## Princípios de Análise
Após implementar uma solução, sempre considere:

1. **Escalabilidade**: A solução funciona para volumes maiores de dados/usuários?
2. **Manutenibilidade**: O código é fácil de entender e modificar?
3. **Performance**: Existem otimizações que poderiam ser aplicadas?
4. **Segurança**: Há vulnerabilidades ou riscos a considerar?

## Formato de Análise
Ao finalizar uma implementação, forneça uma análise estruturada:

```
## Análise da Implementação

### Pontos Fortes
- [Liste os aspectos positivos da solução]

### Considerações Futuras
- [Liste possíveis melhorias ou otimizações]

### Limitações Atuais
- [Identifique quaisquer limitações da abordagem atual]
```

## Áreas de Melhoria Comuns

### Performance
- Redução de renderizações desnecessárias
- Otimização de consultas ao banco de dados
- Implementação de estratégias de cache

### Experiência do Usuário
- Feedback visual durante operações
- Estados de carregamento e tratamento de erros
- Acessibilidade e responsividade

### Arquitetura
- Separação de responsabilidades
- Reutilização de código
- Padrões consistentes

## Exemplo de Análise

```typescript
// Após implementar uma nova funcionalidade
/**
 * Análise:
 * - Forte: Implementação simples e direta que atende o requisito atual
 * - Futuro: Considerar cache para reduzir chamadas à API
 * - Limitação: Não lida bem com grandes volumes de dados
 */
```

Lembre-se: Identificar áreas de melhoria não significa que precisam ser implementadas imediatamente. O objetivo é documentar conscientemente as decisões e os caminhos futuros.
