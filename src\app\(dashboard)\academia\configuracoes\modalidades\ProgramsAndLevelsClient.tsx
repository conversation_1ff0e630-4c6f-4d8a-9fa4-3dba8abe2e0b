'use client'

import { useState, startTransition } from 'react'
import * as TooltipPrimitive from '@radix-ui/react-tooltip'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronUp, Edit2, Users, Settings, GraduationCap, PlusCircle, Loader2 } from 'lucide-react'
import { toggleModalityAction } from '../../actions/toggle-modality'
import { listGraduationLevelsAction } from '../../actions/list-graduation-levels'
import { toast } from '@/hooks/ui/use-toast'
import { BeltWithDetails } from '@/components/belt/BeltWithDetails'
import { GraduationLevel } from '@/services/belts/levels'
import Link from 'next/link'

type Modality = {
  id: string
  slug: string
  name: string
  enabled: boolean
}

type ModalityWithLevels = Modality & {
  levels?: GraduationLevel[]
  levelsLoaded?: boolean
}

interface Props {
  initialModalities: Modality[]
}

export default function ProgramsAndLevelsClient({ initialModalities }: Props) {
  const [modalities, setModalities] = useState<ModalityWithLevels[]>(initialModalities.map((m) => ({ ...m, levelsLoaded: false })))
  const [loadingLevels, setLoadingLevels] = useState<string | null>(null)
  const [isDisabledOpen, setIsDisabledOpen] = useState(false)

  const handleToggle = (modalityId: string) => (checked: boolean) => {
    setModalities((prev) => prev.map((m) => (m.id === modalityId ? { ...m, enabled: checked } : m)))

    startTransition(async () => {
      const result = await toggleModalityAction({ modalityId, enabled: checked })
      if (!result.success) {
        setModalities((prev) => prev.map((m) => (m.id === modalityId ? { ...m, enabled: !checked } : m)))
        toast({
          title: 'Erro',
          description: result.errors?._form || 'Falha ao atualizar modalidade',
          variant: 'destructive'
        })
      }
    })
  }

  const loadLevels = async (modalityId: string, modalitySlug: string) => {
    const modality = modalities.find((m) => m.id === modalityId)
    if (!modality || modality.levelsLoaded || loadingLevels === modalityId) {
      return
    }

    setLoadingLevels(modalityId)
    try {
      const result = await listGraduationLevelsAction(modalitySlug)
      if (result.success) {
        setModalities((prev) => prev.map((m) => (m.id === modalityId ? { ...m, levels: result.levels || [], levelsLoaded: true } : m)))
      } else {
        toast({
          title: 'Erro',
          description: 'Falha ao carregar níveis de graduação',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Falha ao carregar níveis de graduação',
        variant: 'destructive'
      })
    } finally {
      setLoadingLevels(null)
    }
  }

  const enabledModalities = modalities.filter((m) => m.enabled)
  const disabledModalities = modalities.filter((m) => !m.enabled)

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {enabledModalities.length > 0 && (
          <Accordion type="multiple" className="w-full space-y-4">
            {enabledModalities.map((modality) => (
              <AccordionItem value={modality.id} key={modality.id} className="border border-border rounded-lg bg-card">
                <div className="flex items-center justify-between pr-4">
                  <AccordionTrigger onClick={() => loadLevels(modality.id, modality.slug)} className="flex-1 p-4 text-lg font-semibold hover:no-underline">
                    <div className="flex items-center gap-3">
                      {loadingLevels === modality.id ? <Loader2 className="h-5 w-5 animate-spin" /> : <GraduationCap className="h-5 w-5 text-muted-foreground" />}
                      <span>{modality.name}</span>
                    </div>
                  </AccordionTrigger>

                  <div className="flex items-center gap-2">
                    <TooltipPrimitive.Root>
                      <TooltipTrigger asChild>
                        <Link href={`/academia/configuracoes/modalidades/${modality.slug}/editar`}>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Configurar modalidade</p>
                      </TooltipContent>
                    </TooltipPrimitive.Root>
                    <Switch checked={modality.enabled} onCheckedChange={handleToggle(modality.id)} onClick={(e) => e.stopPropagation()} />
                  </div>
                </div>
                <AccordionContent className="p-4 pt-0">
                  {modality.levelsLoaded && modality.levels && modality.levels.length > 0 ? (
                    <div className="space-y-3 pt-4 border-t">
                      {modality.levels.map((level) => (
                        <div key={level.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-md hover:bg-muted/50 transition-colors">
                          <div className="flex items-center gap-3">
                            <BeltWithDetails color={level.belt_color} degree={level.degree} label={level.label} stripeColor={level.stripe_color} showCenterLine={level.show_center_line} centerLineColor={level.center_line_color} size="md" />
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Users className="h-4 w-4" />
                              <span>{level.members_count}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground flex flex-col items-center gap-4">
                      <GraduationCap className="h-12 w-12" />
                      <p>Nenhum nível de graduação configurado.</p>
                      <Link href={`/academia/configuracoes/modalidades/${modality.slug}/editar`}>
                        <Button variant="outline">
                          <PlusCircle className="mr-2 h-4 w-4" />
                          Configurar Níveis
                        </Button>
                      </Link>
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}

        {disabledModalities.length > 0 && (
          <div className="border-t pt-6">
            <Collapsible open={isDisabledOpen} onOpenChange={setIsDisabledOpen}>
              <CollapsibleTrigger className="group flex w-full items-center gap-2 text-muted-foreground mb-4">
                <h3 className="text-lg font-semibold group-hover:text-foreground transition-colors">Modalidades Desabilitadas</h3>
                {isDisabledOpen ? <ChevronUp className="h-5 w-5 group-hover:text-foreground transition-colors" /> : <ChevronDown className="h-5 w-5 group-hover:text-foreground transition-colors" />}
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2">
                {disabledModalities.map((modality) => (
                  <div key={modality.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-md">
                    <span className="text-muted-foreground">{modality.name}</span>
                    <Switch checked={modality.enabled} onCheckedChange={handleToggle(modality.id)} />
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>
          </div>
        )}

        {modalities.length === 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <p>Nenhuma modalidade encontrada</p>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}
