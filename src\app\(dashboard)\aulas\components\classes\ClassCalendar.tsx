'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  Clock, 
  Users, 
  MapPin, 
  ChevronLeft, 
  ChevronRight,
  Filter,
  RefreshCw,
  LayoutGrid,
  List as ListIcon
} from 'lucide-react';
import Link from 'next/link';
import { ClassWithDetails } from '../../types';
import { format, isToday, isSameMonth, startOfMonth, endOfMonth, eachDayOfInterval, startOfWeek, endOfWeek, addMonths, subMonths, addWeeks, subWeeks, addDays, subDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export type CalendarView = 'month' | 'week' | 'day';

interface ClassCalendarProps {
  classes: ClassWithDetails[];
  currentDate?: Date;
  view?: CalendarView;
  onDateChange?: (date: Date) => void;
  onViewChange?: (view: CalendarView) => void;
  isLoading?: boolean;
  showFilters?: boolean;
  compact?: boolean;
  maxClassesPerDay?: number;
}

interface CalendarDay {
  date: Date;
  dateStr: string;
  isCurrentMonth: boolean;
  isToday: boolean;
  classes: ClassWithDetails[];
  day: number;
}

export function ClassCalendar({
  classes = [],
  currentDate = new Date(),
  view = 'month',
  onDateChange,
  onViewChange,
  isLoading = false,
  showFilters = false,
  compact = false,
  maxClassesPerDay = 3
}: ClassCalendarProps) {
  // Helper functions
  const formatDate = (dateString: string | Date) => {
    try {
      const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
      if (isNaN(date.getTime())) {
        return 'Data inválida';
      }
      return format(date, 'dd/MM/yyyy', { locale: ptBR });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return 'Data inválida';
    }
  };

  const formatTime = (dateString: string | Date) => {
    try {
      const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
      if (isNaN(date.getTime())) {
        return '--:--';
      }
      return format(date, 'HH:mm', { locale: ptBR });
    } catch (error) {
      console.error('Erro ao formatar horário:', error);
      return '--:--';
    }
  };

  // Group classes by date
  const classesByDate = useMemo(() => {
    return classes.reduce((acc: Record<string, ClassWithDetails[]>, classItem: ClassWithDetails) => {
      const date = formatDate(classItem.start_time);
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(classItem);
      return acc;
    }, {});
  }, [classes]);

  // Navigation functions
  const navigateDate = (direction: 'prev' | 'next') => {
    let newDate = new Date(currentDate);
    
    switch (view) {
      case 'day':
        newDate = direction === 'next' ? addDays(currentDate, 1) : subDays(currentDate, 1);
        break;
      case 'week':
        newDate = direction === 'next' ? addWeeks(currentDate, 1) : subWeeks(currentDate, 1);
        break;
      case 'month':
      default:
        newDate = direction === 'next' ? addMonths(currentDate, 1) : subMonths(currentDate, 1);
        break;
    }
    
    onDateChange?.(newDate);
  };

  const goToToday = () => {
    onDateChange?.(new Date());
  };

  // Generate month grid for calendar view
  const generateMonthGrid = (): CalendarDay[] => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 }); // Start on Sunday
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 });
    
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });
    
    return days.map(date => {
      const dateStr = formatDate(date);
      return {
        date,
        dateStr,
        isCurrentMonth: isSameMonth(date, currentDate),
        isToday: isToday(date),
        classes: classesByDate[dateStr] || [],
        day: date.getDate()
      };
    });
  };

  // Status and type styling functions
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'scheduled': { label: 'Agendada', variant: 'secondary' as const },
      'ongoing': { label: 'Em andamento', variant: 'default' as const },
      'completed': { label: 'Concluída', variant: 'outline' as const },
      'cancelled': { label: 'Cancelada', variant: 'destructive' as const },
      'rescheduled': { label: 'Reagendada', variant: 'outline' as const }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const };
    return <Badge variant={config.variant} className="text-xs">{config.label}</Badge>;
  };

  const getClassTypeColor = (classType?: string) => {
    const colors = {
      'regular': 'bg-blue-100 dark:bg-blue-900/50 border-blue-300 dark:border-blue-700 text-blue-800 dark:text-blue-200',
      'free': 'bg-green-100 dark:bg-green-900/50 border-green-300 dark:border-green-700 text-green-800 dark:text-green-200',
      'workshop': 'bg-purple-100 dark:bg-purple-900/50 border-purple-300 dark:border-purple-700 text-purple-800 dark:text-purple-200',
      'exam': 'bg-orange-100 dark:bg-orange-900/50 border-orange-300 dark:border-orange-700 text-orange-800 dark:text-orange-200'
    };
    return colors[(classType as keyof typeof colors)] || colors.regular;
  };

  // Get period title based on view
  const getPeriodTitle = () => {
    switch (view) {
      case 'month':
        return format(currentDate, 'MMMM yyyy', { locale: ptBR });
      case 'week':
        const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 });
        const weekEnd = endOfWeek(currentDate, { weekStartsOn: 0 });
        return `Semana de ${formatDate(weekStart)} a ${formatDate(weekEnd)}`;
      case 'day':
        return format(currentDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
      default:
        return '';
    }
  };

  const monthDays = view === 'month' ? generateMonthGrid() : [];

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="grid grid-cols-7 gap-2">
              {Array.from({ length: 35 }).map((_, i) => (
                <div key={i} className="h-20 bg-muted rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Navigation */}
      {!compact && (
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <h2 className="text-2xl font-bold text-foreground">
              Calendário de Aulas
            </h2>
            <p className="text-muted-foreground">
              Visualize as aulas em formato de calendário
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Date Navigation */}
            <div className="flex items-center space-x-1">
              <Button variant="outline" size="sm" onClick={() => navigateDate('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button variant="outline" size="sm" onClick={goToToday}>
                Hoje
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => navigateDate('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            
            {/* View Toggle */}
            {onViewChange && (
              <div className="flex items-center space-x-1">
                <Button 
                  variant={view === 'day' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => onViewChange('day')}
                >
                  Dia
                </Button>
                <Button 
                  variant={view === 'week' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => onViewChange('week')}
                >
                  Semana
                </Button>
                <Button 
                  variant={view === 'month' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => onViewChange('month')}
                >
                  Mês
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Period Title */}
      <div className="text-center">
        <h3 className={`font-semibold text-foreground ${compact ? 'text-lg' : 'text-xl'}`}>
          {getPeriodTitle()}
        </h3>
      </div>

      {/* Month Calendar View */}
      {view === 'month' && (
        <Card>
          <CardContent className={compact ? "p-4" : "p-6"}>
            {/* Weekday Headers */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map(day => (
                <div key={day} className={`text-center font-medium text-muted-foreground ${compact ? 'text-xs py-1' : 'text-sm py-2'}`}>
                  {day}
                </div>
              ))}
            </div>
            
            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-2">
              {monthDays.map((day, index) => (
                <div
                  key={index}
                  className={`
                    ${compact ? 'min-h-16 p-1' : 'min-h-24 p-2'} 
                    border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors
                    ${day.isCurrentMonth 
                      ? 'bg-background border-border' 
                      : 'bg-muted/20 border-muted'
                    }
                    ${day.isToday ? 'ring-2 ring-primary' : ''}
                  `}
                  onClick={() => onDateChange?.(day.date)}
                >
                  <div className={`
                    font-medium mb-1
                    ${compact ? 'text-xs' : 'text-sm'}
                    ${day.isCurrentMonth ? 'text-foreground' : 'text-muted-foreground'}
                    ${day.isToday ? 'text-primary' : ''}
                  `}>
                    {day.day}
                  </div>
                  
                  <div className="space-y-1">
                    {day.classes.slice(0, maxClassesPerDay).map((classItem: ClassWithDetails) => (
                      <Link
                        key={classItem.id}
                        href={`/presenca/${classItem.id}`}
                        className={`
                          block text-xs p-1 rounded border-l-2 hover:opacity-80 transition-opacity
                          ${getClassTypeColor(classItem.class_type)}
                        `}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="font-medium truncate">
                          {classItem.class_group?.name || 'Aula Livre'}
                        </div>
                        {!compact && (
                          <div className="text-xs opacity-75">
                            {formatTime(classItem.start_time)}
                          </div>
                        )}
                      </Link>
                    ))}
                    {day.classes.length > maxClassesPerDay && (
                      <div className="text-xs text-muted-foreground text-center">
                        +{day.classes.length - maxClassesPerDay} mais
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Week/Day List View */}
      {(view === 'week' || view === 'day') && (
        <div className="space-y-4">
          {Object.entries(classesByDate).length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Nenhuma aula encontrada
                </h3>
                <p className="text-muted-foreground">
                  Não há aulas agendadas para este período.
                </p>
              </CardContent>
            </Card>
          ) : (
            Object.entries(classesByDate)
              .sort(([a], [b]) => new Date(a.split('/').reverse().join('-')).getTime() - new Date(b.split('/').reverse().join('-')).getTime())
              .map(([date, dayClasses]) => (
                <Card key={date}>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {(() => {
                        try {
                          const parsedDate = new Date(date.split('/').reverse().join('-'));
                          if (isNaN(parsedDate.getTime())) {
                            return 'Data inválida';
                          }
                          return format(parsedDate, "EEEE, dd 'de' MMMM", { locale: ptBR });
                        } catch (error) {
                          console.error('Erro ao formatar data do calendário:', error);
                          return 'Data inválida';
                        }
                      })()}
                    </CardTitle>
                    <CardDescription>
                      {(dayClasses as ClassWithDetails[]).length} aula(s) agendada(s)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {(dayClasses as ClassWithDetails[])
                        .sort((a: ClassWithDetails, b: ClassWithDetails) => 
                          new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
                        )
                        .map((classItem: ClassWithDetails) => (
                          <Link
                            key={classItem.id}
                            href={`/presenca/${classItem.id}`}
                            className="block"
                          >
                            <div className={`
                              p-4 rounded-lg border-l-4 hover:shadow-md transition-shadow
                              ${getClassTypeColor(classItem.class_type)}
                            `}>
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <h4 className="font-medium">
                                      {classItem.class_group?.name || 'Aula Livre'}
                                    </h4>
                                    {getStatusBadge(classItem.status)}
                                  </div>
                                  
                                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                    <div className="flex items-center space-x-1">
                                      <Clock className="h-3 w-3" />
                                      <span>
                                        {formatTime(classItem.start_time)} - {formatTime(classItem.end_time)}
                                      </span>
                                    </div>
                                    
                                    {classItem.instructor?.full_name && (
                                      <div className="flex items-center space-x-1">
                                        <Users className="h-3 w-3" />
                                        <span>{classItem.instructor.full_name}</span>
                                      </div>
                                    )}
                                    
                                    {classItem.branch?.name && (
                                      <div className="flex items-center space-x-1">
                                        <MapPin className="h-3 w-3" />
                                        <span>{classItem.branch.name}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="text-sm text-muted-foreground">
                                  {classItem._count?.attendance || 0}
                                  {classItem.class_group_id && classItem.max_capacity ? `/${classItem.max_capacity}` : ''} alunos
                                </div>
                              </div>
                            </div>
                          </Link>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              ))
          )}
        </div>
      )}

      {/* Quick Stats */}
      {!compact && classes.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{classes.length}</div>
                <div className="text-sm text-muted-foreground">Total de aulas</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {classes.filter((c: ClassWithDetails) => c.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">Concluídas</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {classes.filter((c: ClassWithDetails) => c.status === 'scheduled').length}
                </div>
                <div className="text-sm text-muted-foreground">Agendadas</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {classes.filter((c: ClassWithDetails) => c.status === 'cancelled').length}
                </div>
                <div className="text-sm text-muted-foreground">Canceladas</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
} 