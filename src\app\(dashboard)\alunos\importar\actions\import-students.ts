'use server';

import { hasPermission } from '@/services/permissions/actions/check-permission';
import { createAdminClient } from '@/services/supabase/server';
import { revalidatePath } from 'next/cache';
import { nanoid } from 'nanoid';
import { extractNameParts } from '@/utils/name-utils';
import { randomUUID } from 'crypto';
import { formatPhoneNumber } from '@/utils/phone-utils';
import { normalizeEmail } from '@/utils/string';

type ImportStudentData = {
  full_name?: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone: string;
  birth_date?: string;
  gender?: string;
  street?: string;
  street_number?: string;
  complement?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  health_notes?: string;
  allergies?: string;
  medical_conditions?: string;
  medications?: string;
  belt_color?: string;
  belt_degree?: string;
  branch_id?: string;
}

type ImportResult = {
  success: boolean;
  message: string;
  total: number;
  imported: number;
  failed: number;
  errors: Array<{
    row: number;
    email: string;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    email: string;
    message: string;
  }>;
}

type ImportSettings = {
  defaultStatus: 'active' | 'inactive';
  duplicateDetection: 'name' | 'email';
  duplicateAction: 'do_nothing' | 'overwrite';
  branch_id: string;
  emptyFieldAction: 'allow' | 'skip_row' | 'error';
}

type PreparedStudent = {
  originalIndex: number;
  data: ImportStudentData;
  userId: string;
  studentId: string;
  beltId: string;
  registrationNumber: string;
  checkInCode: string;
  processedData: {
    first_name: string;
    last_name?: string;
    full_name: string;
    email: string;
    phone: string;
    birth_date?: string;
    gender?: string;
    belt_color: string;
    belt_degree: number;
  };
}

type BatchCreationResult = {
  success: PreparedStudent[];
  failed: Array<{
    student: PreparedStudent;
    error: string;
  }>;
}

function generateRegistrationNumber(): string {
  return `REG-${nanoid(8)}`;
}

async function generateUniqueCheckInCodes(
  supabase: any,
  tenantId: string,
  count: number
): Promise<string[]> {
  const codes: string[] = [];
  const maxAttempts = count * 3;
  
  for (let attempt = 0; attempt < maxAttempts && codes.length < count; attempt++) {
    const code = String(Math.floor(1000 + Math.random() * 9000));
    
    if (codes.includes(code)) continue;
    
    const { data, error } = await supabase
      .from('students')
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('check_in_code', code)
      .limit(1);

    if (error) continue;
    if (!data || data.length === 0) {
      codes.push(code);
    }
  }
  
  if (codes.length < count) {
    throw new Error(`Não foi possível gerar ${count} códigos únicos de check-in. Gerados apenas ${codes.length}.`);
  }
  
  return codes;
}

async function validateAndPrepareStudents(
  studentsData: ImportStudentData[],
  tenantId: string,
  branchId: string,
  settings: ImportSettings,
  supabase: any
): Promise<{
  valid: PreparedStudent[];
  invalid: Array<{ index: number; data: ImportStudentData; error: string }>;
}> {
  const valid: PreparedStudent[] = [];
  const invalid: Array<{ index: number; data: ImportStudentData; error: string }> = [];

  console.log(`[BULK] Preparando dados para ${studentsData.length} alunos...`);
  const startTime = Date.now();

  let checkInCodes: string[] = [];
  try {
    checkInCodes = await generateUniqueCheckInCodes(supabase, tenantId, studentsData.length);
    console.log(`[BULK] Códigos de check-in gerados em ${Date.now() - startTime}ms`);
  } catch (error) {
    console.error('[BULK] Erro ao gerar códigos de check-in:', error);
    checkInCodes = studentsData.map(() => `TEMP-${nanoid(6)}`);
  }

  for (let i = 0; i < studentsData.length; i++) {
    const studentData = studentsData[i];
    
    try {
      let first_name = studentData.first_name;
      let last_name = studentData.last_name;
      let full_name = studentData.full_name;

      if (!first_name && !last_name && full_name) {
        const nameParts = extractNameParts(full_name);
        first_name = nameParts.firstName;
        last_name = nameParts.lastName;
        full_name = nameParts.fullName;
      } else if (first_name || last_name) {
        full_name = `${first_name || ''} ${last_name || ''}`.trim();
      }

      if (!first_name) {
        invalid.push({ index: i, data: studentData, error: 'Nome é obrigatório' });
        continue;
      }

      const formattedPhone = formatPhoneNumber(studentData.phone);
      if (!formattedPhone) {
        invalid.push({ index: i, data: studentData, error: 'Telefone inválido' });
        continue;
      }

      const normalizedEmail = normalizeEmail(studentData.email);
      if (!normalizedEmail) {
        invalid.push({ index: i, data: studentData, error: 'E-mail inválido' });
        continue;
      }

      let birthDate = null;
      if (studentData.birth_date) {
        const dateStr = studentData.birth_date.trim();
        let parsedDate = null;
        
        if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
          const [day, month, year] = dateStr.split('/');
          parsedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        } else if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(dateStr)) {
          parsedDate = new Date(dateStr);
        }

        if (parsedDate && !isNaN(parsedDate.getTime())) {
          birthDate = parsedDate.toISOString().split('T')[0];
        }
      }

      const validGenders = ['masculino', 'feminino', 'outro', 'prefiro_nao_informar'];
      const gender = studentData.gender && validGenders.includes(studentData.gender.toLowerCase()) 
        ? studentData.gender.toLowerCase() 
        : null;

      const validBeltColors = ['white', 'blue', 'purple', 'brown', 'black'];
      const beltColor = studentData.belt_color && validBeltColors.includes(studentData.belt_color.toLowerCase())
        ? studentData.belt_color.toLowerCase()
        : 'white';
      
      const beltDegree = studentData.belt_degree && !isNaN(Number(studentData.belt_degree))
        ? Math.max(0, Math.min(4, Number(studentData.belt_degree)))
        : 0;

      const userId = randomUUID();
      const studentId = randomUUID();
      const beltId = randomUUID();
      const registrationNumber = generateRegistrationNumber();
      const checkInCode = checkInCodes[i];

      const prepared: PreparedStudent = {
        originalIndex: i,
        data: studentData,
        userId,
        studentId,
        beltId,
        registrationNumber,
        checkInCode,
        processedData: {
          first_name: first_name!,
          last_name: last_name ?? undefined,
          full_name: full_name!,
          email: normalizedEmail,
          phone: formattedPhone,
          birth_date: birthDate ?? undefined,
          gender: gender ?? undefined,
          belt_color: beltColor as any,
          belt_degree: beltDegree,
        }
      };

      valid.push(prepared);
    } catch (error) {
      invalid.push({ 
        index: i, 
        data: studentData, 
        error: error instanceof Error ? error.message : 'Erro na validação' 
      });
    }
  }

  console.log(`[BULK] Preparação concluída em ${Date.now() - startTime}ms. ${valid.length} válidos, ${invalid.length} inválidos`);
  return { valid, invalid };
}

async function checkDuplicatesInBatch(
  preparedStudents: PreparedStudent[],
  supabase: any,
  tenantId: string,
  settings: ImportSettings
): Promise<{
  nonDuplicates: PreparedStudent[];
  duplicates: Array<{ student: PreparedStudent; existing: any }>;
}> {
  console.log(`[BULK] Verificando duplicados para ${preparedStudents.length} alunos...`);
  const startTime = Date.now();

  const nonDuplicates: PreparedStudent[] = [];
  const duplicates: Array<{ student: PreparedStudent; existing: any }> = [];

  if (settings.duplicateDetection === 'email') {
    const emails = preparedStudents.map(s => s.processedData.email);
    
    const { data: existingUsers } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, full_name')
      .eq('tenant_id', tenantId)
      .eq('role', 'student')
      .in('email', emails);

    const existingEmailsSet = new Set((existingUsers || []).map((u: any) => u.email.toLowerCase()));
    
    for (const student of preparedStudents) {
      if (existingEmailsSet.has(student.processedData.email.toLowerCase())) {
        const existing = existingUsers?.find((u: any) => 
          u.email.toLowerCase() === student.processedData.email.toLowerCase()
        );
        duplicates.push({ student, existing });
      } else {
        nonDuplicates.push(student);
      }
    }
  } else {
    for (const student of preparedStudents) {
      const { data: existingUsers } = await supabase
        .from('users')
        .select('id, email, first_name, last_name, full_name')
        .eq('tenant_id', tenantId)
        .eq('role', 'student')
        .eq('full_name', student.processedData.full_name);

      if (existingUsers && existingUsers.length > 0) {
        duplicates.push({ student, existing: existingUsers[0] });
      } else {
        nonDuplicates.push(student);
      }
    }
  }

  console.log(`[BULK] Verificação de duplicados concluída em ${Date.now() - startTime}ms. ${nonDuplicates.length} únicos, ${duplicates.length} duplicados`);
  return { nonDuplicates, duplicates };
}

async function createAuthUsersInBatches(
  preparedStudents: PreparedStudent[],
  supabase: any,
  tenantId: string,
  branchId: string,
  status: 'active' | 'inactive',
  batchSize: number = 10
): Promise<BatchCreationResult> {
  console.log(`[BULK] Criando ${preparedStudents.length} usuários Auth em lotes de ${batchSize}...`);
  const startTime = Date.now();

  const success: PreparedStudent[] = [];
  const failed: Array<{ student: PreparedStudent; error: string }> = [];

  for (let i = 0; i < preparedStudents.length; i += batchSize) {
    const batch = preparedStudents.slice(i, i + batchSize);
    console.log(`[BULK] Processando lote ${Math.floor(i / batchSize) + 1}/${Math.ceil(preparedStudents.length / batchSize)} (${batch.length} usuários)...`);
    
    const batchPromises = batch.map(async (student) => {
      try {
        const { data: userData, error: userError } = await supabase.auth.admin.createUser({
          email: student.processedData.email,
          email_confirm: true,
          user_metadata: {
            first_name: student.processedData.first_name,
            last_name: student.processedData.last_name,
            full_name: student.processedData.full_name,
            phone: student.processedData.phone,
            email_verified: true
          },
          app_metadata: {
            tenant_id: tenantId,
            branch_id: branchId,
            role: 'student',
            provider: 'email',
          },
          password: '123456789'
        });

        if (userError || !userData.user) {
          throw new Error(`Erro ao criar usuário: ${userError?.message}`);
        }

        student.userId = userData.user.id;

        await supabase.auth.admin.updateUserById(userData.user.id, {
          app_metadata: {
            ...userData.user.app_metadata,
            id: userData.user.id,
            status: status,
          },
        });

        return { success: true, student };
      } catch (error) {
        return {
          success: false,
          student,
          error: error instanceof Error ? error.message : 'Erro desconhecido',
        };
      }
    });

    const results = await Promise.allSettled(batchPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        if (result.value.success) {
          success.push(result.value.student);
        } else {
          failed.push({ 
            student: result.value.student, 
            error: result.value.error || 'Erro desconhecido' 
          });
        }
      } else {
        failed.push({ 
          student: batch[index], 
          error: `Erro na promessa: ${result.reason}` 
        });
      }
    });

    if (i + batchSize < preparedStudents.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`[BULK] Criação de usuários Auth concluída em ${Date.now() - startTime}ms. ${success.length} criados, ${failed.length} falharam`);
  return { success, failed };
}

async function bulkInsertUsers(
  preparedStudents: PreparedStudent[],
  supabase: any,
  tenantId: string,
  branchId: string,
  status: 'active' | 'inactive'
): Promise<{ success: boolean; error?: string }> {
  console.log(`[BULK] Inserindo ${preparedStudents.length} registros na tabela users...`);
  const startTime = Date.now();

  const usersData = preparedStudents.map(student => ({
    id: student.userId,
    tenant_id: tenantId,
    branch_id: branchId,
    email: student.processedData.email,
    role: 'student' as const,
    status,
    first_name: student.processedData.first_name,
    last_name: student.processedData.last_name,
    full_name: student.processedData.full_name,
    phone: student.processedData.phone,
    avatar_url: null,
    avatar_storage_path: null,
    metadata: {}
  }));

  const { error } = await supabase
    .from('users')
    .insert(usersData);

  if (error) {
    console.warn('[BULK] Aviso no bulk insert users (pode ser ignorado se o trigger funcionar):', error.message);
  }

  console.log(`[BULK] Bulk insert users concluído em ${Date.now() - startTime}ms`);
  return { success: true };
}

async function bulkInsertStudents(
  preparedStudents: PreparedStudent[],
  supabase: any,
  tenantId: string,
  branchId: string
): Promise<{ success: boolean; error?: string }> {
  console.log(`[BULK] Inserindo ${preparedStudents.length} registros na tabela students...`);
  const startTime = Date.now();

  const studentsData = preparedStudents.map(student => {
    const sanitizedEmergencyPhone = student.data.emergency_contact_phone
      ? formatPhoneNumber(student.data.emergency_contact_phone)
      : null;

    return {
      id: student.studentId,
      tenant_id: tenantId,
      user_id: student.userId,
      branch_id: branchId,
      registration_number: student.registrationNumber,
      financial_status: 'up_to_date' as const,
      birth_date: student.processedData.birth_date,
      gender: student.processedData.gender,
      street: student.data.street || null,
      street_number: student.data.street_number || null,
      complement: student.data.complement || null,
      neighborhood: student.data.neighborhood || null,
      city: student.data.city || null,
      state: student.data.state || null,
      postal_code: student.data.postal_code || null,
      emergency_contact_name: student.data.emergency_contact_name || null,
      emergency_contact_phone: sanitizedEmergencyPhone,
      emergency_contact_relationship: student.data.emergency_contact_relationship || null,
      health_notes: student.data.health_notes || null,
      allergies: student.data.allergies || null,
      medical_conditions: student.data.medical_conditions || null,
      medications: student.data.medications || null,
      check_in_code: student.checkInCode,
      current_belt_id: null,
      metadata: {}
    };
  });

  const { error } = await supabase
    .from('students')
    .insert(studentsData);

  if (error) {
    console.error('[BULK] Erro no bulk insert students:', error);
    return { success: false, error: error.message };
  }

  console.log(`[BULK] Bulk insert students concluído em ${Date.now() - startTime}ms`);
  return { success: true };
}

async function bulkInsertStudentBelts(
  preparedStudents: PreparedStudent[],
  supabase: any,
  tenantId: string,
  authUserId: string
): Promise<{ success: boolean; error?: string }> {
  console.log(`[BULK] Inserindo ${preparedStudents.length} registros na tabela student_belts...`);
  const startTime = Date.now();

  const beltsData = preparedStudents.map(student => ({
    id: student.beltId,
    tenant_id: tenantId,
    student_id: student.studentId,
    belt_color: student.processedData.belt_color,
    degree: student.processedData.belt_degree,
    awarded_at: new Date().toISOString(),
    awarded_by: authUserId,
    notes: 'Faixa inicial - importação'
  }));

  const { error } = await supabase
    .from('student_belts')
    .insert(beltsData);

  if (error) {
    console.error('[BULK] Erro no bulk insert student_belts:', error);
    return { success: false, error: error.message };
  }

  console.log(`[BULK] Bulk insert student_belts concluído em ${Date.now() - startTime}ms`);
  return { success: true };
}

async function bulkUpdateStudentCurrentBelt(
  preparedStudents: PreparedStudent[],
  supabase: any
): Promise<{ success: boolean; error?: string }> {
  console.log(`[BULK] Atualizando current_belt_id para ${preparedStudents.length} alunos...`);
  const startTime = Date.now();

  const updates = preparedStudents.map(student => 
    supabase
      .from('students')
      .update({ current_belt_id: student.beltId })
      .eq('id', student.studentId)
  );

  const results = await Promise.all(updates);
  const firstError = results.find(res => res.error);

  if (firstError) {
    console.error('[BULK] Erro ao atualizar current_belt_id:', firstError.error);
    return { success: false, error: firstError.error.message };
  }
  
  console.log(`[BULK] Atualização de current_belt_id concluída em ${Date.now() - startTime}ms`);
  return { success: true };
}

export async function importStudents(
  studentsData: ImportStudentData[],
  settings: ImportSettings
): Promise<ImportResult> {
  const overallStartTime = Date.now();
  console.log(`[BULK] Iniciando importação otimizada de ${studentsData.length} alunos...`);

  const hasCreatePermission = await hasPermission('students:create');
  if (!hasCreatePermission) {
    return {
      success: false,
      message: 'Você não tem permissão para importar alunos',
      total: studentsData.length,
      imported: 0,
      failed: studentsData.length,
      errors: [],
      warnings: [],
    };
  }

  const supabase = await createAdminClient();
  
  try {
    const { data: { user: authUser } } = await supabase.auth.getUser();
    if (!authUser?.app_metadata.tenant_id) {
       return {
        success: false,
        message: 'Não foi possível identificar a academia',
        total: studentsData.length,
        imported: 0,
        failed: studentsData.length,
        errors: [],
        warnings: [],
      };
    }
    const tenantId = authUser.app_metadata.tenant_id;

    const { data: branch } = await supabase
      .from('branches')
      .select('id')
      .eq('id', settings.branch_id)
      .eq('tenant_id', tenantId)
      .single();

    if (!branch) {
      return {
        success: false,
        message: 'Filial selecionada não encontrada',
        total: studentsData.length,
        imported: 0,
        failed: studentsData.length,
        errors: [],
        warnings: [],
      };
    }

    const results: ImportResult = {
      success: true,
      message: '',
      total: studentsData.length,
      imported: 0,
      failed: 0,
      errors: [],
      warnings: [],
    };

    const { valid: preparedStudents, invalid: invalidStudents } = await validateAndPrepareStudents(
      studentsData,
      tenantId,
      settings.branch_id,
      settings,
      supabase
    );

    invalidStudents.forEach(({ index, data, error }) => {
      results.failed++;
      results.errors.push({
        row: index + 1,
        email: data.email || '',
        error
      });
    });

    if (preparedStudents.length === 0) {
      results.message = 'Nenhum aluno válido para importar';
      return results;
    }

    const { nonDuplicates, duplicates } = await checkDuplicatesInBatch(
      preparedStudents,
      supabase,
      tenantId,
      settings
    );

    duplicates.forEach(({ student }) => {
      if (settings.duplicateAction === 'do_nothing') {
        return;
      }
      
      results.failed++;
      results.errors.push({
        row: student.originalIndex + 1,
        email: student.processedData.email,
        error: 'Aluno já existe (sobrescrita não implementada)'
      });
    });

    if (nonDuplicates.length === 0) {
      results.message = 'Nenhum aluno novo para importar';
      return results;
    }

    console.log(`[BULK] Processando ${nonDuplicates.length} alunos únicos...`);

    const { success: authSuccessStudents, failed: authFailedStudents } = await createAuthUsersInBatches(
      nonDuplicates,
      supabase,
      tenantId,
      settings.branch_id,
      settings.defaultStatus
    );

    authFailedStudents.forEach(({ student, error }) => {
      results.failed++;
      results.errors.push({
        row: student.originalIndex + 1,
        email: student.processedData.email,
        error: `Erro na criação do usuário: ${error}`
      });
    });

    if (authSuccessStudents.length === 0) {
      results.message = 'Falha na criação de usuários Auth';
      return results;
    }

    console.log(`[BULK] Executando bulk inserts para ${authSuccessStudents.length} alunos...`);
    
    const usersResult = await bulkInsertUsers(authSuccessStudents, supabase, tenantId, settings.branch_id, settings.defaultStatus);
    const studentsResult = await bulkInsertStudents(authSuccessStudents, supabase, tenantId, settings.branch_id);
    
    if (!usersResult.success || !studentsResult.success) {
      console.error('[BULK] Falha nos bulk inserts (users ou students), limpando usuários Auth e Users...');
      
      const userIdsToDelete = authSuccessStudents.map(s => s.userId);

      // Limpar de todas as tabelas em caso de falha para evitar dados órfãos
      const dbCleanupPromise = supabase.from('users').delete().in('id', userIdsToDelete);
      
      const authCleanupPromises = userIdsToDelete.map(userId => 
        supabase.auth.admin.deleteUser(userId).catch(e => {
          console.error(`Falha ao limpar usuário Auth ${userId}`, e);
          return null; // Retornar nulo para satisfazer o tipo de Promise allSettled
        })
      );

      await Promise.allSettled([dbCleanupPromise, ...authCleanupPromises]);

      return {
        success: false,
        message: `Falha na inserção inicial: ${usersResult.error || studentsResult.error}`,
        total: studentsData.length,
        imported: 0,
        failed: studentsData.length,
        errors: results.errors,
        warnings: results.warnings,
      };
    }
    
    const beltsResult = await bulkInsertStudentBelts(authSuccessStudents, supabase, tenantId, authUser.id);
    
    if (!beltsResult.success) {
      console.error('[BULK] Falha na inserção de faixas, limpando usuários Auth e Users...');
      
      const userIdsToDelete = authSuccessStudents.map(s => s.userId);

      // Limpar de todas as tabelas em caso de falha para evitar dados órfãos
      const dbCleanupPromise = supabase.from('users').delete().in('id', userIdsToDelete);
      
      const authCleanupPromises = userIdsToDelete.map(userId => 
        supabase.auth.admin.deleteUser(userId).catch(e => {
          console.error(`Falha ao limpar usuário Auth ${userId}`, e);
          return null; // Retornar nulo para satisfazer o tipo de Promise allSettled
        })
      );

      await Promise.allSettled([dbCleanupPromise, ...authCleanupPromises]);

      return {
        success: false,
        message: `Falha na inserção de faixas: ${beltsResult.error}`,
        total: studentsData.length,
        imported: 0,
        failed: studentsData.length,
        errors: results.errors,
        warnings: results.warnings,
      };
    }
    
    const updateResult = await bulkUpdateStudentCurrentBelt(authSuccessStudents, supabase);

    if (!updateResult.success) {
      console.error('[BULK] Falha ao atualizar a faixa atual do aluno, limpando usuários Auth e Users...');
      
      const userIdsToDelete = authSuccessStudents.map(s => s.userId);

      // Limpar de todas as tabelas em caso de falha para evitar dados órfãos
      const dbCleanupPromise = supabase.from('users').delete().in('id', userIdsToDelete);
      
      const authCleanupPromises = userIdsToDelete.map(userId => 
        supabase.auth.admin.deleteUser(userId).catch(e => {
          console.error(`Falha ao limpar usuário Auth ${userId}`, e);
          return null; // Retornar nulo para satisfazer o tipo de Promise allSettled
        })
      );

      await Promise.allSettled([dbCleanupPromise, ...authCleanupPromises]);

      return {
        success: false,
        message: `Falha ao atualizar a faixa atual do aluno: ${updateResult.error}`,
        total: studentsData.length,
        imported: 0,
        failed: studentsData.length,
        errors: results.errors,
        warnings: results.warnings,
      };
    }
    
    results.imported = authSuccessStudents.length;
    
    const totalTime = Date.now() - overallStartTime;
    console.log(`[BULK] Importação concluída com sucesso em ${totalTime}ms! ${results.imported} alunos importados.`);

    if (results.imported > 0) {
      revalidatePath('/alunos');
    }

    results.message = `Importação concluída. ${results.imported} alunos importados${results.failed > 0 ? `, ${results.failed} falharam` : ''}.`;

    return results;

  } catch (error: any) {
    console.error('[BULK] Erro geral na importação:', error);
    return {
      success: false,
      message: error.message || 'Erro inesperado durante a importação',
      total: studentsData.length,
      imported: 0,
      failed: studentsData.length,
      errors: [],
      warnings: [],
    };
  }
} 