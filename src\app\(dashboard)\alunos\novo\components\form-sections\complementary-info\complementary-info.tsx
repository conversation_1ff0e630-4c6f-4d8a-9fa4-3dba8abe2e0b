'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { NovoAlunoFormValues } from "../../../actions/schemas/aluno-schema";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useEffect, useState } from "react";
import { Calendar, User, Building, AlertCircle, Bug } from "lucide-react";
import { getBranchesByTenant } from "../../../../actions/branch-actions";
import { getDebugInfo } from "../../../../actions/debug-actions";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>alog<PERSON><PERSON><PERSON>, 
  DialogTrigger 
} from "@/components/ui/dialog";

// Tipo para representar as filiais
type Branch = {
  id: string;
  name: string;
};

export default function ComplementaryInfoSection() {
  const { control, setValue } = useFormContext<NovoAlunoFormValues>();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoadingDebug, setIsLoadingDebug] = useState(false);

  // Carregar filiais disponíveis usando o server action
  useEffect(() => {
    const fetchBranches = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const result = await getBranchesByTenant();

        if (!result.success) {
          setError(result.error || 'Erro desconhecido ao carregar filiais');
          toast.error(`Erro ao carregar filiais: ${result.error}`);
          return;
        }
        
        if (!result.data || result.data.length === 0) {
          setError('Nenhuma filial encontrada para este tenant');
          return;
        }
        
        setBranches(result.data);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Erro desconhecido ao carregar filiais');
        toast.error('Erro ao carregar filiais');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchBranches();
  }, [setValue]);

  // Função para buscar informações de debug
  const handleLoadDebugInfo = async () => {
    setIsLoadingDebug(true);
    try {
      const debugData = await getDebugInfo();
      setDebugInfo(debugData);
    } catch (error) {
      console.error('[DEBUG] Erro ao carregar informações de debug:', error);
    } finally {
      setIsLoadingDebug(false);
    }
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Dados Complementares */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Dados Complementares
          </h2>
        </div>
        
        <CardContent className="p-6 space-y-4">
          <FormField
            control={control}
            name="birth_date"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                  <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Data de Nascimento
                  </FormLabel>
                </div>
                <FormControl>
                  <Input 
                    type="date" 
                    placeholder="AAAA-MM-DD" 
                    {...field} 
                    className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={control}
            name="gender"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <div className="flex items-center">
                  <User className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                  <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Gênero
                  </FormLabel>
                </div>
                <FormControl>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full">
                      <SelectValue placeholder="Selecione o gênero" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="masculino">Masculino</SelectItem>
                      <SelectItem value="feminino">Feminino</SelectItem>
                      <SelectItem value="outro">Outro</SelectItem>
                      <SelectItem value="prefiro_nao_informar">Prefiro não informar</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Filial */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <Building className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Filial
          </h2>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs"
                onClick={handleLoadDebugInfo}
              >
                <Bug className="h-3.5 w-3.5 mr-1" />
                Diagnóstico
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Informações de Diagnóstico</DialogTitle>
              </DialogHeader>
              
              {isLoadingDebug ? (
                <div className="py-4 text-center">Carregando informações...</div>
              ) : debugInfo ? (
                <div className="overflow-auto max-h-[400px]">
                  <div className="p-3 mb-3 bg-slate-100 dark:bg-slate-800 rounded">
                    <h3 className="font-medium mb-1">Tenant</h3>
                    <pre className="text-xs overflow-auto">{JSON.stringify(debugInfo.tenant, null, 2)}</pre>
                  </div>
                  
                  <div className="p-3 mb-3 bg-slate-100 dark:bg-slate-800 rounded">
                    <h3 className="font-medium mb-1">Usuário</h3>
                    <pre className="text-xs overflow-auto">{JSON.stringify(debugInfo.user, null, 2)}</pre>
                  </div>
                  
                  <div className="p-3 bg-slate-100 dark:bg-slate-800 rounded">
                    <h3 className="font-medium mb-1">Filiais</h3>
                    <pre className="text-xs overflow-auto">{JSON.stringify(debugInfo.metadata, null, 2)}</pre>
                  </div>
                </div>
              ) : (
                <div className="py-4 text-center">Clique em Diagnóstico para carregar informações</div>
              )}
            </DialogContent>
          </Dialog>
        </div>
        
        <CardContent className="p-6">
          {error ? (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          ) : null}
          
          <FormField
            control={control}
            name="branch_id"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <div className="flex items-center">
                  <Building className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                  <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                    Filial*
                  </FormLabel>
                </div>
                <FormControl>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full">
                      <SelectValue placeholder="Selecione uma filial" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.name || `Filial ${branch.id}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <p className="text-xs text-muted-foreground mt-1">
                  {isLoading ? "Carregando filiais..." : `${branches.length} filial(is) disponível(is)`}
                </p>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
} 