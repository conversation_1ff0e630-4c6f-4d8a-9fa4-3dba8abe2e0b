---
description: 
globs: 
alwaysApply: true
---
# Documentação de Código Efetiva

## Princípios de Documentação

A documentação de código deve seguir estes princípios fundamentais:

1. **Ser Clara e Concisa**
   - Use linguagem direta e objetiva
   - Evite jargões desnecessários ou explicações redundantes
   - Prefira frases curtas e diretas ao ponto

2. **Explicar o "Por quê" e não apenas o "O quê"**
   - Documente a intenção e raciocínio por trás do código
   - Explique decisões não óbvias ou contra-intuitivas
   - Forneça contexto sobre as restrições ou requisitos

3. **Manter-se Atualizada**
   - A documentação desatualizada é pior que nenhuma documentação
   - Atualize a documentação sempre que o código mudar
   - Revise periodicamente para garantir precisão

4. **Ser Acessível**
   - Use formatação consistente para facilitar a leitura
   - Organize em seções lógicas com títulos descritivos
   - Utilize listas e tabelas quando apropriado

## Tipos de Documentação

### Documentação de Componentes React

```tsx
/**
 * UserProfileCard - Exibe informações do perfil do usuário com controles de edição
 *
 * Este componente implementa:
 * 1. Layout responsivo com grid para diferentes tamanhos de tela
 * 2. Estados condicionais para modo de visualização e edição
 * 3. Gerenciamento otimizado de re-renderizações com memoização
 *
 * IMPORTANTE: Requer contexto de autenticação para verificação de permissões
 *
 * @example
 * ```tsx
 * <UserProfileCard 
 *   userId="123" 
 *   canEdit={hasEditPermission} 
 *   onProfileUpdate={handleProfileUpdate} 
 * />
 * ```
 */
```

### Documentação de Hooks

```tsx
/**
 * useFormValidation - Hook personalizado para validação de formulários
 * 
 * Este hook gerencia o estado de validação de formulários com:
 * - Validação em tempo real durante digitação
 * - Suporte para validação assíncrona (ex: verificação de email)
 * - Integração com bibliotecas externas como Zod ou Yup
 * - Cache de resultados para evitar validações redundantes
 * 
 * @param {Object} schema - Schema de validação (Zod, Yup, etc)
 * @param {Object} options - Opções de configuração
 * @param {boolean} options.validateOnChange - Ativa validação durante digitação
 * @param {number} options.debounceMs - Tempo de debounce para validação (ms)
 * 
 * @returns {Object} Objeto contendo funções e estado de validação
 * @returns {Function} .validate - Função para validar dados manualmente
 * @returns {Object} .errors - Objeto contendo erros de validação
 * @returns {boolean} .isValid - Estado geral de validação
 * @returns {boolean} .isValidating - Indica se validação está em andamento
 */
```

### Documentação de Funções Utilitárias

```tsx
/**
 * formatCurrency - Formata valor numérico para exibição monetária
 * 
 * Esta função:
 * 1. Aplica formatação baseada na localidade do usuário
 * 2. Gerencia diferentes moedas e suas configurações específicas
 * 3. Lida com casos especiais como valores negativos e zero
 * 
 * A implementação usa o Intl.NumberFormat internamente para garantir
 * compatibilidade internacional e respeito às convenções locais de
 * formatação, enquanto adiciona tratamentos específicos para o contexto
 * da aplicação.
 * 
 * @param {number} value - Valor a ser formatado
 * @param {string} currencyCode - Código ISO da moeda (ex: 'BRL', 'USD')
 * @param {Object} options - Opções adicionais de formatação
 * 
 * @returns {string} Valor formatado como string
 * 
 * @throws {Error} Se valor não for um número válido
 * @throws {Error} Se código de moeda for inválido
 * 
 * @example
 * // Retorna "R$ 1.234,56"
 * formatCurrency(1234.56, 'BRL');
 * 
 * // Retorna "$1,234.56"
 * formatCurrency(1234.56, 'USD');
 */
```

## Padrões de Documentação

### Para TypeScript

1. **Interfaces e Types**
   ```typescript
   /**
    * Representa os dados de um usuário no sistema
    * 
    * Esta interface é usada em:
    * - APIs de gerenciamento de usuários
    * - Componentes de exibição de perfil
    * - Processos de autenticação
    */
   interface User {
     /** Identificador único do usuário (UUID v4) */
     id: string;
     
     /** Nome completo do usuário */
     name: string;
     
     /** Email principal para contato e login */
     email: string;
     
     /** 
      * Data de criação do registro
      * Formato ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)
      */
     createdAt: string;
     
     /** 
      * Status atual do usuário no sistema
      * @see UserStatusEnum para valores possíveis
      */
     status: UserStatus;
   }
   ```

2. **Funções e Métodos**
   ```typescript
   /**
    * Autentica usuário usando credenciais e retorna token de acesso
    * 
    * Processo:
    * 1. Valida credenciais contra banco de dados
    * 2. Gera novo token JWT com payload apropriado
    * 3. Registra atividade de login para auditoria
    * 4. Atualiza estatísticas de uso no sistema
    * 
    * @param credentials - Objeto com credenciais de login
    * @param options - Opções de configuração da autenticação
    * 
    * @returns Objeto contendo token e dados do usuário
    * 
    * @throws AuthenticationError quando credenciais são inválidas
    * @throws RateLimitError quando excede tentativas permitidas
    */
   async function authenticateUser(
     credentials: LoginCredentials,
     options?: AuthOptions
   ): Promise<AuthResult> {
     // Implementação
   }
   ```

### Para Server Actions

```typescript
/**
 * createUser - Cria um novo usuário no sistema
 * 
 * Esta Server Action:
 * 1. Valida dados de entrada usando Zod
 * 2. Verifica duplicidade de email no banco de dados
 * 3. Aplica hash na senha usando algoritmo bcrypt
 * 4. Persiste o usuário no banco de dados
 * 5. Dispara eventos para ações subsequentes (email de boas-vindas, etc)
 * 
 * Proteções implementadas:
 * - Rate limiting por IP para prevenir abuso
 * - Validação complexa de senha para garantir segurança
 * - Sanitização de inputs para prevenir injeção
 * 
 * @security Esta ação exige autenticação como administrador
 * 
 * @param userData - Dados do novo usuário
 * @returns Objeto indicando sucesso/falha e dados do usuário criado
 */
```

## Checklist de Documentação

Antes de considerar a documentação completa, verifique:

- [ ] O propósito e funcionalidade estão claramente explicados?
- [ ] As pré-condições e requisitos estão documentados?
- [ ] Comportamentos especiais ou edge cases estão descritos?
- [ ] Os parâmetros e valores de retorno estão documentados?
- [ ] Exemplos de uso estão incluídos quando úteis?
- [ ] Informações de segurança relevantes estão mencionadas?
- [ ] A documentação está atualizada com a implementação atual?

