'use client'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useEffect, useState, useCallback } from 'react'
import { getBranches } from '../actions/get-branches'

interface ImportSettingsProps {
  settings: {
    defaultStatus: 'active' | 'inactive';
    duplicateDetection: 'name' | 'email';
    duplicateAction: 'do_nothing' | 'overwrite';
    branch_id: string;
    emptyFieldAction: 'allow' | 'skip_row' | 'error';
  };
  onSettingsChange: (
    settings:
      | Partial<ImportSettingsProps['settings']>
      | ((prev: ImportSettingsProps['settings']) => ImportSettingsProps['settings']),
  ) => void;
}

interface Branch {
  id: string;
  name: string;
}

export function ImportSettings({
  settings,
  onSettingsChange,
}: ImportSettingsProps) {
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const handleSettingChange = useCallback(
    (key: keyof ImportSettingsProps['settings'], value: string) => {
      onSettingsChange(prev => ({
        ...prev,
        [key]: value,
      }))
    },
    [onSettingsChange],
  )

  useEffect(() => {
    async function fetchBranches() {
      setLoading(true)
      setError(null)

      const result = await getBranches()

      if (result.error) {
        setError(result.error)
        setBranches([])
      } else if (result.data) {
        const fetchedBranches = result.data || []
        setBranches(fetchedBranches)

        if (fetchedBranches.length > 0 && !settings.branch_id) {
          onSettingsChange({ branch_id: fetchedBranches[0].id })
        }
      }

      setLoading(false)
    }

    fetchBranches()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [settings.branch_id])

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Configurações de Importação</CardTitle>
        <CardDescription>
          Configure as opções para processar e importar os dados.
        </CardDescription>
      </CardHeader>
      <CardContent className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="space-y-2">
          <Label>Filial</Label>
          <Select
            value={settings.branch_id}
            onValueChange={value => handleSettingChange('branch_id', value)}
            disabled={loading || branches.length === 0}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  loading
                    ? 'Carregando...'
                    : 'Selecione uma filial'
                }
              />
            </SelectTrigger>
            <SelectContent>
              {branches.map(branch => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {error && <p className="text-sm text-red-600">{error}</p>}
          {!loading && !error && branches.length === 0 && (
            <p className="text-sm text-red-600">
              Nenhuma filial encontrada. Verifique se há filiais cadastradas.
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Formato da Data</Label>
          <Select defaultValue="dmy">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="dmy">dia / mês / ano</SelectItem>
              {/* <SelectItem value="mdy">mês / dia / ano</SelectItem>
              <SelectItem value="ymd">ano / mês / dia</SelectItem> */}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Status Padrão</Label>
          <Select
            value={settings.defaultStatus}
            onValueChange={value => handleSettingChange('defaultStatus', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Ativo</SelectItem>
              <SelectItem value="inactive">Inativo</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 md:col-span-2 lg:col-span-1">
          <Label>Ação para Campos Vazios</Label>
          <Select
            value={settings.emptyFieldAction}
            onValueChange={value => handleSettingChange('emptyFieldAction', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="error">Marcar como erro</SelectItem>
              <SelectItem value="skip_row">Ignorar linha</SelectItem>
              <SelectItem value="allow">Permitir vazio</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4 rounded-lg border p-4 md:col-span-2 lg:col-span-2">
          <Label className="block font-medium">Duplicatas</Label>
          <div className="grid sm:grid-cols-2 gap-4 items-end">
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">
                Detectar duplicatas por
              </Label>
              <Select
                value={settings.duplicateDetection}
                onValueChange={value =>
                  handleSettingChange('duplicateDetection', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Nome</SelectItem>
                  <SelectItem value="email">E-mail</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">
                O que fazer se encontradas?
              </Label>
              <Select
                value={settings.duplicateAction}
                onValueChange={value =>
                  handleSettingChange('duplicateAction', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="do_nothing">Não fazer nada</SelectItem>
                  <SelectItem value="overwrite">
                    Sobrescrever (em breve)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 