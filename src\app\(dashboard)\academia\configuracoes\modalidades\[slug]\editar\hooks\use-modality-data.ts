'use client';

import { useState, useCallback } from 'react';
import { refetchModalityDataAction } from '../actions/refetch-modality-data';
import { type ModalitySettings } from '@/services/modalities/settings';
import { type GraduationLevel } from '@/services/belts/levels';
import { type BeltLevelRequirements } from '@/services/belts/requirements';

export interface GraduationLevelWithRequirements extends GraduationLevel {
  requirements?: BeltLevelRequirements;
  stripe_color?: string | null;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

interface UseModalityDataProps {
  modalityId: string;
  modalitySlug: string;
  initialModalitySettings: ModalitySettings | null;
  initialLevelsWithRequirements: GraduationLevelWithRequirements[];
}

interface UseModalityDataReturn {
  modalitySettings: ModalitySettings | null;
  levelsWithRequirements: GraduationLevelWithRequirements[];
  isRefetching: boolean;
  refetchData: () => Promise<void>;
  updateLocalData: (
    newSettings: ModalitySettings | null,
    newLevels: GraduationLevelWithRequirements[]
  ) => void;
}

export function useModalityData({
  modalityId,
  modalitySlug,
  initialModalitySettings,
  initialLevelsWithRequirements,
}: UseModalityDataProps): UseModalityDataReturn {
  const [modalitySettings, setModalitySettings] = useState<ModalitySettings | null>(
    initialModalitySettings
  );
  const [levelsWithRequirements, setLevelsWithRequirements] = useState<
    GraduationLevelWithRequirements[]
  >(initialLevelsWithRequirements);
  const [isRefetching, setIsRefetching] = useState(false);

  const refetchData = useCallback(async () => {
    if (!modalityId || !modalitySlug) return;

    setIsRefetching(true);
    try {
      console.log('🔄 Refetching modality data...');

      const result = await refetchModalityDataAction(modalityId, modalitySlug);

      if (result.success) {
        // Atualizar estado
        setModalitySettings(result.modalitySettings || null);
        setLevelsWithRequirements(result.levelsWithRequirements || []);
        console.log('✅ Modality data refetched successfully');
      } else {
        console.error('❌ Error refetching modality data:', result.error);
      }
    } catch (error) {
      console.error('❌ Error refetching modality data:', error);
    } finally {
      setIsRefetching(false);
    }
  }, [modalityId, modalitySlug]);

  const updateLocalData = useCallback(
    (
      newSettings: ModalitySettings | null,
      newLevels: GraduationLevelWithRequirements[]
    ) => {
      setModalitySettings(newSettings);
      setLevelsWithRequirements(newLevels);
    },
    []
  );

  return {
    modalitySettings,
    levelsWithRequirements,
    isRefetching,
    refetchData,
    updateLocalData,
  };
}
