# Documentação: Gráfico de Pagamentos Mensais

## ✅ PROBLEMA RESOLVIDO

**Status:** ~~Os pagamentos vencidos não estão sendo contabilizados no gráfico.~~ **CORRIGIDO**

**Causa:** ~~A função `getMonthlyPaymentStatus` busca apenas pagamentos com `status = 'overdue'`, mas o sistema não atualiza automaticamente o status de `pending` para `overdue` quando a data de vencimento passa.~~ **RESOLVIDO**

**Solução Implementada:**
- ✅ Modificada a lógica para considerar pagamentos `pending` com `due_date < hoje` como vencidos
- ✅ Adicionado campo `paid_at` na query para futuras melhorias
- ✅ Implementada **Opção 1**: Classificação por data de vencimento (`due_date`)

**Data da Correção:** 18/07/2025

---

## Visão Geral

O gráfico de pagamentos mensais (`RevenueChart.tsx`) exibe duas informações principais por mês:
- **Pagamentos Pagos** (barras verdes)
- **Pagamentos Vencidos** (barras vermelhas)

## Arquitetura do Sistema

### 1. Estrutura de Arquivos

```
src/app/(dashboard)/financeiro/mensalidades/
├── components/
│   └── RevenueChart.tsx          # Componente do gráfico
├── actions/
│   └── payment-actions.ts        # Lógica de busca de dados
└── types/
    └── index.ts                  # Definições de tipos
```

### 2. Fluxo de Dados

```
[Banco de Dados] → [Action] → [Componente] → [Gráfico]
     payments      getMonthlyPaymentStatus   RevenueChart   Recharts
```

## Implementação Detalhada

### 1. Busca de Dados (`getMonthlyPaymentStatus`)

**Localização:** `src/app/(dashboard)/financeiro/mensalidades/actions/payment-actions.ts`

#### Query SQL Executada:
```sql
SELECT amount, status, due_date, created_at 
FROM payments 
WHERE due_date >= '2025-01-01T00:00:00.000Z' 
  AND due_date <= '2025-12-31T23:59:59.000Z'
```

#### Parâmetros:
- `year`: Ano para filtrar os dados (padrão: ano atual)
- Busca todos os pagamentos do ano baseado na `due_date`

### 2. Processamento dos Dados

#### Inicialização dos Meses:
```javascript
// Cria objeto com todos os 12 meses zerados
const monthlyData = {};
for (let month = 0; month < 12; month++) {
  const monthKey = `${year}-${String(month + 1).padStart(2, '0')}`;
  monthlyData[monthKey] = { 
    paid: 0, 
    overdue: 0, 
    paidAmount: 0, 
    overdueAmount: 0 
  };
}
```

#### Lógica de Classificação:
```javascript
payments.forEach((payment) => {
  const dueDate = new Date(payment.due_date);
  const monthKey = `${dueDate.getFullYear()}-${String(dueDate.getMonth() + 1).padStart(2, '0')}`;
  const amount = parseFloat(payment.amount);
  
  if (payment.status === 'paid') {
    monthlyData[monthKey].paid += 1;
    monthlyData[monthKey].paidAmount += amount;
  } else if (payment.status === 'overdue') {
    monthlyData[monthKey].overdue += 1;
    monthlyData[monthKey].overdueAmount += amount;
  }
});
```

### 3. Estrutura de Dados Retornada

#### Tipo `MonthlyPaymentStatus`:
```typescript
interface MonthlyPaymentStatus {
  month: string;        // "2025-01" formato
  paid: number;         // Quantidade de pagamentos pagos
  overdue: number;      // Quantidade de pagamentos vencidos
  paidAmount: number;   // Valor total dos pagamentos pagos
  overdueAmount: number; // Valor total dos pagamentos vencidos
}
```

#### Exemplo de Dados:
```json
[
  {
    "month": "2025-01",
    "paid": 5,
    "overdue": 2,
    "paidAmount": 750.00,
    "overdueAmount": 200.00
  },
  {
    "month": "2025-02",
    "paid": 3,
    "overdue": 1,
    "paidAmount": 450.00,
    "overdueAmount": 100.00
  }
]
```

## Problema Identificado

### Status dos Pagamentos no Banco

**Consulta realizada:**
```sql
SELECT status, COUNT(*) as count FROM payments GROUP BY status;
```

**Resultado atual:**
```json
[
  {"status": "paid", "count": 3},
  {"status": "pending", "count": 10}
]
```

### Problema Principal

❌ **O que está acontecendo:**
- Existem pagamentos com `status = 'pending'` e `due_date < NOW()`
- A função busca apenas pagamentos com `status = 'overdue'`
- Não há processo automático para atualizar status de `pending` para `overdue`

❌ **Pagamentos vencidos não contabilizados:**
```sql
SELECT id, status, due_date, amount 
FROM payments 
WHERE status = 'pending' AND due_date < NOW();
```

**Resultado:**
```json
[
  {
    "id": "8a665197-f571-4abf-9e5d-c1ec25c3e78a",
    "status": "pending",
    "due_date": "2025-07-16",
    "amount": "150.00"
  },
  {
    "id": "be339d0c-db9b-4411-9ecd-032d6a5ecfed", 
    "status": "pending",
    "due_date": "2025-07-16",
    "amount": "50.00"
  }
]
```

## Solução Proposta

### Modificar a Lógica de Classificação

```javascript
payments.forEach((payment) => {
  const dueDate = new Date(payment.due_date);
  const today = new Date();
  const monthKey = `${dueDate.getFullYear()}-${String(dueDate.getMonth() + 1).padStart(2, '0')}`;
  const amount = parseFloat(payment.amount);
  
  if (payment.status === 'paid') {
    monthlyData[monthKey].paid += 1;
    monthlyData[monthKey].paidAmount += amount;
  } else if (payment.status === 'overdue' || 
            (payment.status === 'pending' && dueDate < today)) {
    monthlyData[monthKey].overdue += 1;
    monthlyData[monthKey].overdueAmount += amount;
  }
});
```

### Critérios de Classificação Atualizados

✅ **Pagamento Pago:**
- `status = 'paid'`

✅ **Pagamento Vencido:**
- `status = 'overdue'` OU
- `status = 'pending'` E `due_date < hoje`

## Componente Visual

### Configuração do Gráfico

```typescript
<BarChart data={data}>
  <Bar 
    dataKey="paid" 
    fill="#22c55e"     // Verde
    name="Pagos"
  />
  <Bar 
    dataKey="overdue" 
    fill="#ef4444"     // Vermelho
    name="Vencidos"
  />
</BarChart>
```

### Tooltip Personalizado

```typescript
const CustomTooltip = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="tooltip">
        <p>{data.displayMonth} {year}</p>
        <p>Pagos: {data.paid} ({formatCurrency(data.paidAmount)})</p>
        <p>Vencidos: {data.overdue} ({formatCurrency(data.overdueAmount)})</p>
      </div>
    );
  }
};
```

## Métricas Exibidas

### Cabeçalho do Gráfico
- **Título:** "Pagamentos Mensais - {ano}"
- **Resumo:** Total de pagos e vencidos com valores

### Dados por Mês
- **Eixo X:** Meses abreviados (jan, fev, mar...)
- **Eixo Y:** Quantidade de pagamentos
- **Barras:** Verde (pagos) e Vermelho (vencidos)
- **Tooltip:** Detalhes ao passar o mouse

## Considerações Técnicas

### Performance
- Busca dados uma vez por ano
- Processamento em JavaScript (client-side)
- Cache automático do React (useEffect com dependência do ano)

### Timezone
- Utiliza timezone do Brasil para cálculos de data
- Conversão realizada no código da aplicação

### Responsividade
- Gráfico responsivo usando ResponsiveContainer
- Adaptação automática para diferentes tamanhos de tela

## Estados do Componente

### Loading State
```typescript
if (loading) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Pagamentos Mensais</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64 bg-gray-100 dark:bg-gray-800 animate-pulse rounded"></div>
      </CardContent>
    </Card>
  );
}
```

### Error State
```typescript
if (error) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Pagamentos Mensais</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-red-500 dark:text-red-400 text-center py-8">{error}</p>
      </CardContent>
    </Card>
  );
}
```

## Dependências

### Bibliotecas Utilizadas
- **recharts**: Biblioteca de gráficos React
- **date-fns**: Manipulação de datas
- **@/components/ui**: Componentes UI customizados
- **@/utils/format-utils**: Utilitários de formatação

### Imports Necessários
```typescript
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { formatCurrency } from '@/utils/format-utils';
import { getMonthlyPaymentStatus } from '../actions/payment-actions';
import { MonthlyPaymentStatus } from '../types';
```

## Debugging e Troubleshooting

### Logs Úteis
```javascript
console.log('Dados recebidos:', result.data);
console.log('Dados processados:', allMonths);
console.log('Total pago:', totalPaidAmount);
console.log('Total vencido:', totalOverdueAmount);
```

### Verificações no Banco
```sql
-- Verificar status dos pagamentos
SELECT status, COUNT(*) FROM payments GROUP BY status;

-- Verificar pagamentos vencidos
SELECT * FROM payments WHERE status = 'pending' AND due_date < NOW();

-- Verificar distribuição por mês
SELECT
  DATE_TRUNC('month', due_date) as month,
  status,
  COUNT(*) as count,
  SUM(amount) as total_amount
FROM payments
WHERE EXTRACT(YEAR FROM due_date) = 2025
GROUP BY DATE_TRUNC('month', due_date), status
ORDER BY month, status;
```

## Melhorias Futuras

### Funcionalidades Sugeridas
1. **Filtro por período personalizado**
2. **Exportação de dados**
3. **Drill-down por mês**
4. **Comparação entre anos**
5. **Alertas para pagamentos vencidos**

### Otimizações
1. **Cache de dados no servidor**
2. **Paginação para grandes volumes**
3. **Lazy loading do gráfico**
4. **Compressão de dados**
