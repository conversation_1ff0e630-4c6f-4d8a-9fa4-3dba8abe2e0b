'use server';

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';
import { requireAuth } from '@/services/auth/server';
import { getTenantSlug } from '@/services/tenant';
import { revalidatePath } from 'next/cache';

/**
 * Schema de validação das configurações de aparência
 */
const appearanceSchema = z.object({
  primaryColor: z
    .string()
    .regex(/^#([0-9a-f]{3}|[0-9a-f]{6})$/i, 'Cor primária inválida')
    .nullable()
    .optional(),
  // secondaryColor removido nas configurações
  description: z.string().nullable().optional(), // JSON string (slides) ou null
});

export type AppearanceFormData = z.infer<typeof appearanceSchema>;

/**
 * <PERSON><PERSON> as configurações de aparência do tenant atual
 */
export async function getAppearance() {
  try {
    await requireAuth();
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return { success: false, errors: { _form: 'Tenant não encontrado' } };
    }

    const supabase = await createClient();

    const { data, error } = await supabase
      .from('tenants')
      .select('id, primary_color, description')
      .eq('slug', tenantSlug)
      .single();

    if (error) {
      console.error('Erro ao buscar aparência:', error);
      return { success: false, errors: { _form: 'Erro ao buscar dados' } };
    }

    if (!data) {
      return { success: false, errors: { _form: 'Configurações não encontradas' } };
    }

    return {
      success: true,
      data: {
        primaryColor: data.primary_color || null,
        description: data.description || null,
      },
    };
  } catch (err) {
    console.error('Erro interno getAppearance:', err);
    return { success: false, errors: { _form: 'Erro interno do servidor' } };
  }
}

/**
 * Atualiza as configurações de aparência
 */
export async function updateAppearance(payload: unknown) {
  try {
    await requireAuth();

    const result = appearanceSchema.safeParse(payload);
    if (!result.success) {
      return { success: false, errors: result.error.format() };
    }

    const data = result.data;
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return { success: false, errors: { _form: 'Tenant não encontrado' } };
    }

    const supabase = await createClient();

    const { error } = await supabase
      .from('tenants')
      .update({
        primary_color: data.primaryColor ?? null,
        description: data.description ?? null,
      })
      .eq('slug', tenantSlug);

    if (error) {
      console.error('Erro ao atualizar aparência:', error);
      return { success: false, errors: { _form: 'Falha ao atualizar dados' } };
    }

    // Revalidar rota de check-in e auth para refletir alterações imediatamente
    revalidatePath('/');

    return { success: true };
  } catch (err) {
    console.error('Erro interno updateAppearance:', err);
    return { success: false, errors: { _form: 'Erro interno do servidor' } };
  }
} 