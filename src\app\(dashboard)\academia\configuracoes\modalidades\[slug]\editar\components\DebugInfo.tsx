'use client';

import React from 'react';
import { type ModalitySettings } from '@/services/modalities/settings';
import { type GraduationLevelWithRequirements } from '../hooks/use-modality-data';

interface DebugInfoProps {
  modalitySettings: ModalitySettings | null;
  levelsWithRequirements: GraduationLevelWithRequirements[];
}

export function DebugInfo({ modalitySettings, levelsWithRequirements }: DebugInfoProps) {
  // Verificar se há dados de requirements
  const hasExistingSessions = levelsWithRequirements.some(level => 
    level.requirements?.sessions && level.requirements.sessions > 0
  );
  const hasExistingMinAge = levelsWithRequirements.some(level => 
    level.requirements?.minimum_age && level.requirements.minimum_age > 0
  );

  // Contar quantos levels têm requirements
  const levelsWithSessions = levelsWithRequirements.filter(level => 
    level.requirements?.sessions && level.requirements.sessions > 0
  ).length;
  const levelsWithMinAge = levelsWithRequirements.filter(level => 
    level.requirements?.minimum_age && level.requirements.minimum_age > 0
  ).length;

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-sm space-y-2 mb-4">
      <h4 className="font-semibold text-gray-800 dark:text-gray-100">🔍 Debug - Dados de Requirements</h4>
      
      <div className="space-y-1">
        <p className="dark:text-gray-200"><strong>Modality Settings:</strong></p>
        <ul className="ml-4 space-y-1">
          <li>require_sessions: <span className="font-mono dark:text-gray-300">{String(modalitySettings?.require_sessions ?? 'undefined')}</span></li>
          <li>require_minimum_age: <span className="font-mono dark:text-gray-300">{String(modalitySettings?.require_minimum_age ?? 'undefined')}</span></li>
          <li>promotion_setting: <span className="font-mono dark:text-gray-300">{modalitySettings?.promotion_setting ?? 'undefined'}</span></li>
        </ul>
      </div>

      <div className="space-y-1">
        <p className="dark:text-gray-200"><strong>Levels with Requirements:</strong></p>
        <ul className="ml-4 space-y-1">
          <li>Total levels: <span className="font-mono dark:text-gray-300">{levelsWithRequirements.length}</span></li>
          <li>Levels with sessions: <span className="font-mono dark:text-gray-300">{levelsWithSessions}</span></li>
          <li>Levels with min age: <span className="font-mono dark:text-gray-300">{levelsWithMinAge}</span></li>
          <li>Auto-detect sessions: <span className="font-mono dark:text-gray-300">{String(hasExistingSessions)}</span></li>
          <li>Auto-detect min age: <span className="font-mono dark:text-gray-300">{String(hasExistingMinAge)}</span></li>
        </ul>
      </div>

      <div className="space-y-1">
        <p className="dark:text-gray-200"><strong>Requirements Detail:</strong></p>
        <div className="ml-4 max-h-40 overflow-y-auto">
          {levelsWithRequirements.map((level, idx) => (
            <div key={level.id || idx} className="text-xs border-l-2 border-gray-300 dark:border-gray-600 pl-2 mb-1">
              <p className="dark:text-gray-100"><strong>{level.label || level.belt_color}:</strong></p>
              <p className="dark:text-gray-200">ID: <span className="font-mono text-xs dark:text-gray-300">{level.id || 'undefined'}</span></p>
              <p className="dark:text-gray-200">Sessions: {level.requirements?.sessions || 0}</p>
              <p className="dark:text-gray-200">Min Age: {level.requirements?.minimum_age || 'N/A'}</p>
              <p className="dark:text-gray-200">Has Requirements: {level.requirements ? 'Yes' : 'No'}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-1">
        <p className="dark:text-gray-200"><strong>Form Input Values (Real-time):</strong></p>
        <div className="ml-4 text-xs">
          {levelsWithRequirements.map((level, idx) => {
            const currentSessions = level.requirements?.sessions || 0;
            
            return (
              <div key={level.id || idx} className="p-1 rounded bg-blue-50 dark:bg-blue-900 mb-1">
                <p className="dark:text-gray-100"><strong>{level.label}:</strong></p>
                <p className="dark:text-gray-200">DB Sessions: <span className="font-mono dark:text-gray-300">{currentSessions}</span></p>
                <p className="dark:text-gray-200">Requirements obj: <span className="font-mono dark:text-gray-300">{JSON.stringify(level.requirements)}</span></p>
              </div>
            );
          })}
        </div>
      </div>

      <div className="space-y-1">
        <p className="dark:text-gray-200"><strong>ID Validation:</strong></p>
        <div className="ml-4 text-xs">
          {levelsWithRequirements.map((level, idx) => {
            const hasValidId = level.id && level.id.trim() !== '';
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
            const isValidUuid = hasValidId && uuidRegex.test(level.id!);
            
            return (
              <div key={idx} className={`p-1 rounded ${!isValidUuid ? 'bg-red-100 dark:bg-red-900' : 'bg-green-100 dark:bg-green-900'}`}>
                <span className="font-mono dark:text-gray-100">{level.label}</span>: 
                {!hasValidId && <span className="text-red-600 dark:text-red-300 ml-1">NO ID</span>}
                {hasValidId && !isValidUuid && <span className="text-red-600 dark:text-red-300 ml-1">INVALID UUID</span>}
                {isValidUuid && <span className="text-green-600 dark:text-green-300 ml-1">VALID</span>}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 