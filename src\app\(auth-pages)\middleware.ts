import { redirect } from 'next/navigation';
import { getRedirectPathAfterLogin } from '@/services/user/role-service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

/**
 * Verifica se o usuário já está autenticado e redireciona para a página
 * apropriada com base na role (dashboard para admin, home para outros).
 * 
 * Esta função deve ser chamada no início de cada página de autenticação.
 */
export async function redirectAuthenticatedUser() {
  // Usar server action para verificar autenticação sem expor API key
  const user = await getCurrentUser();
  
  // Se estiver autenticado, redirecionar para a página apropriada baseada na role
  if (user) {
    const redirectPath = await getRedirectPathAfterLogin(user.id);
    redirect(redirectPath);
  }
} 