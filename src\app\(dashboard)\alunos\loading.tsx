import { ListSkeleton } from "./components/list/list-skeleton";

export default function AlunosLoading() {
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">Alunos</h1>
        </div>
        <div className="h-9 w-32 rounded-md bg-muted animate-pulse"></div>
      </div>
      
      <div className="h-20 rounded-md bg-muted animate-pulse"></div>
      
      <ListSkeleton />
    </div>
  );
}