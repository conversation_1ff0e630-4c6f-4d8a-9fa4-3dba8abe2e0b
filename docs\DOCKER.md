# Configuração do Docker para ApexSaaS

Este documento descreve como executar a aplicação ApexSaaS usando Docker, permitindo o funcionamento do sistema multi-tenant para academias de jiu-jitsu.

## Pré-requisitos

- Docker instalado (versão 20.10.0 ou superior)
- Docker Compose instalado (versão 2.0.0 ou superior)
- Acesso ao Supabase (URL e chave anônima)

## Estrutura de Arquivos

- `Dockerfile`: Configuração para construir a imagem Docker
- `docker-compose.yml`: Orquestração dos serviços
- `nginx/default.conf`: Configuração do Nginx para subdomínios
- `docker-setup.sh`: Script para facilitar a configuração inicial

## Configuração Inicial

1. Execute o script de configuração:

```bash
chmod +x docker-setup.sh
./docker-setup.sh
```

2. O script verificará e criará:
   - Arquivo `.env` (se não existir)
   - Diretório `nginx` (se não existir)
   - Arquivo de configuração `nginx/default.conf` (se não existir)

3. Edite o arquivo `.env` com suas informações do Supabase:

```
NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima-do-supabase
NEXT_PUBLIC_BASE_DOMAIN=localhost:3000
```

## Executando a Aplicação

### Ambiente de Desenvolvimento

Para executar a aplicação em modo de desenvolvimento com hot-reload:

```bash
docker-compose up apexsaas-dev nginx
```

### Ambiente de Produção

Para executar a aplicação em modo de produção:

```bash
docker-compose up apexsaas-prod nginx
```

## Acessando a Aplicação


- **Acesso direto**: http://localhost:3000 (via porta exposta do container Next.js)
- **Acesso via Nginx**: http://localhost (via proxy Nginx)
- **Subdomínios de tenant**: http://[nome-da-academia].localhost (via proxy Nginx)

> **Nota**: Para desenvolvimento, recomendamos usar http://localhost:3000 diretamente, pois isso corresponde à configuração da aplicação Next.js e facilita o acesso a recursos como hot-reload.

## Configuração de Subdomínios Locais

Para que os subdomínios funcionem corretamente no ambiente de desenvolvimento local:

### No Windows

1. Edite o arquivo `C:\Windows\System32\drivers\etc\hosts` como administrador
2. Adicione as seguintes linhas:
```
127.0.0.1 localhost
127.0.0.1 academia1.localhost
127.0.0.1 academia2.localhost
# Adicione mais subdomínios conforme necessário
```

### No macOS/Linux

1. Edite o arquivo `/etc/hosts` como superusuário
2. Adicione as seguintes linhas:
```
127.0.0.1 localhost
127.0.0.1 academia1.localhost
127.0.0.1 academia2.localhost
# Adicione mais subdomínios conforme necessário
```

## Usando a Aplicação em Desenvolvimento

A aplicação está configurada para usar a variável de ambiente `NEXT_PUBLIC_BASE_DOMAIN=localhost:3000`, que é correta para o ambiente de desenvolvimento. Isso significa que:

1. Acesse a aplicação diretamente em http://localhost:3000
2. Os subdomínios são configurados como http://[academia].localhost pelo Nginx, que redireciona para a aplicação
3. A aplicação Next.js entende corretamente o formato tanto de localhost:3000 quanto de [academia].localhost

## Solução de Problemas

### Não é possível acessar os subdomínios

Certifique-se de:
1. Ter configurado corretamente o arquivo hosts
2. O Nginx está rodando (`docker-compose ps`)
3. As variáveis de ambiente estão configuradas corretamente

### Erro de conexão com o Supabase

Verifique:
1. Se as variáveis `NEXT_PUBLIC_SUPABASE_URL` e `NEXT_PUBLIC_SUPABASE_ANON_KEY` estão corretas
2. Se o projeto do Supabase está ativo e acessível

## Considerações de Segurança para Produção

Ao implantar em produção, considere:
1. Utilizar HTTPS com certificados SSL
2. Ajustar as configurações do Nginx para seu domínio de produção
3. Implementar rate limiting e outras proteções de segurança 