'use client'

import React from 'react'
import { format, startOfWeek, endOfWeek, isSameWeek } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useCalendar } from '../contexts/CalendarContext'
import { AgendaFilters } from './AgendaFilters'
import { cn } from '@/lib/utils'

export function CalendarHeader() {
  const { 
    currentDate, 
    events,
    nextWeek,
    prevWeek,
    goToToday
  } = useCalendar()

  const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 })
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 })
  const isCurrentWeek = isSameWeek(new Date(), currentDate, { weekStartsOn: 1 })

  // Contar eventos da semana atual
  const weekEvents = events.filter(event => 
    event.startTime >= weekStart && event.startTime <= weekEnd
  )

  const eventsByStatus = {
    pending: weekEvents.filter(e => e.status === 'pending').length,
    ongoing: weekEvents.filter(e => e.status === 'ongoing').length,
    completed: weekEvents.filter(e => e.status === 'completed').length,
    cancelled: weekEvents.filter(e => e.status === 'cancelled').length,
  }

  const eventsByType = {
    class: weekEvents.filter(e => e.type === 'class').length,
    appointment: weekEvents.filter(e => e.type === 'appointment').length,
    event: weekEvents.filter(e => e.type === 'event').length,
  }

  return (
    <div className="flex items-center justify-between">
      {/* Título e informações da semana */}
      <div className="flex items-center space-x-6">
        <div>
          <div className="flex items-center gap-3 mb-1">
            <h2 className="text-xl font-semibold text-foreground">
              {format(currentDate, "MMMM 'de' yyyy", { locale: ptBR })}
            </h2>
            {isCurrentWeek && (
              <Badge variant="default" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                Semana Atual
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-4">
            <p className="text-sm text-muted-foreground">
              Semana de {format(weekStart, "dd/MM", { locale: ptBR })} até {format(weekEnd, "dd/MM", { locale: ptBR })}
            </p>
            
            {/* Resumo de eventos */}
            {weekEvents.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">•</span>
                <span className="text-xs font-medium text-foreground">
                  {weekEvents.length} evento{weekEvents.length !== 1 ? 's' : ''}
                </span>
                
                {/* Badges de status (apenas os que existem) */}
                <div className="flex gap-1 flex-wrap">
                  {eventsByStatus.pending > 0 && (
                    <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                      {eventsByStatus.pending} agendado{eventsByStatus.pending !== 1 ? 's' : ''}
                    </Badge>
                  )}
                  {eventsByStatus.ongoing > 0 && (
                    <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                      {eventsByStatus.ongoing} em andamento{eventsByStatus.ongoing !== 1 ? 's' : ''}
                    </Badge>
                  )}
                  {eventsByStatus.completed > 0 && (
                    <Badge variant="outline" className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      {eventsByStatus.completed} concluído{eventsByStatus.completed !== 1 ? 's' : ''}
                    </Badge>
                  )}
                  {eventsByStatus.cancelled > 0 && (
                    <Badge variant="destructive" className="text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                      {eventsByStatus.cancelled} cancelado{eventsByStatus.cancelled !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
                
                {/* Separador e badges de tipo */}
                {(eventsByType.class > 0 || eventsByType.appointment > 0 || eventsByType.event > 0) && (
                  <>
                    <span className="text-xs text-muted-foreground">•</span>
                    <div className="flex gap-1">
                      {eventsByType.class > 0 && (
                        <Badge variant="outline" className="text-xs">
                          {eventsByType.class} aula{eventsByType.class !== 1 ? 's' : ''}
                        </Badge>
                      )}
                      {eventsByType.appointment > 0 && (
                        <Badge variant="outline" className="text-xs">
                          {eventsByType.appointment} atendimento{eventsByType.appointment !== 1 ? 's' : ''}
                        </Badge>
                      )}
                      {eventsByType.event > 0 && (
                        <Badge variant="outline" className="text-xs">
                          {eventsByType.event} evento{eventsByType.event !== 1 ? 's' : ''}
                        </Badge>
                      )}
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Controles de navegação e filtros */}
      <div className="flex items-center space-x-3">
        {/* Filtros */}
        <AgendaFilters />
        
        <Button
          variant="outline"
          size="sm"
          onClick={goToToday}
          className={cn(
            "text-sm transition-all",
            isCurrentWeek && "bg-primary/10 border-primary/30 text-primary"
          )}
        >
          <CalendarIcon className="h-4 w-4 mr-2" />
          Hoje
        </Button>
        
        <div className="flex items-center border rounded-lg overflow-hidden">
          <Button
            variant="ghost"
            size="sm"
            onClick={prevWeek}
            className="h-9 w-9 p-0 rounded-none border-r hover:bg-muted/50"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Semana anterior</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={nextWeek}
            className="h-9 w-9 p-0 rounded-none hover:bg-muted/50"
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Próxima semana</span>
          </Button>
        </div>
      </div>
    </div>
  )
} 