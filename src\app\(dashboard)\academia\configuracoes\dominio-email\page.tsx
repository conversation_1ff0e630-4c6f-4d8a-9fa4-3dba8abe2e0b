'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Loader2, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  getSenderEmail,
  setSenderEmail,
  type EmailFormData,
} from '../actions/email-domain-actions';

const emailSchema = z.object({
  email: z.string().email('Email inválido'),
});

export default function DominioEmailPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
  } = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: { email: '' },
  });

  useEffect(() => {
    const load = async () => {
      const result = await getSenderEmail();
      if (result.success && result.data) {
        setValue('email', result.data.email);
      } else {
        toast.error(result.errors?._form || 'Erro ao carregar e-mail');
      }
      setIsLoading(false);
    };
    load();
  }, [setValue]);

  const onSubmit = async (data: EmailFormData) => {
    setIsSubmitting(true);
    const result = await setSenderEmail(data);
    if (result.success) {
      toast.success(result.message || 'Endereço de envio definido');
      router.back();
    } else if (result.errors) {
      toast.error(result.errors._form || 'Erro ao salvar');
    }
    setIsSubmitting(false);
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center p-6">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-xl space-y-6 p-6">
      <button
        type="button"
        onClick={() => router.back()}
        className="flex items-center gap-1 text-sm text-muted-foreground hover:underline"
      >
        <ArrowLeft className="h-4 w-4" /> Voltar
      </button>

      <h1 className="text-xl font-semibold">Configurações de domínio de e-mail</h1>

      <div className="rounded-2xl border border-border bg-card p-6 shadow-sm space-y-6">
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Configurar domínio de e-mail</h2>
          <p className="text-sm text-muted-foreground">
            Para configurar o envio e recebimento de e-mails com seu próprio domínio, você
            precisará adicionar algumas configurações ao seu domínio. Primeiro, escolha o
            endereço de e-mail para enviar e-mails:
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="uppercase text-xs font-medium">
              Endereço de envio de e-mail
            </Label>
            <Input id="email" {...register('email')} />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          <Button type="submit" disabled={isSubmitting || !isDirty} className="w-full">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Definindo...
              </>
            ) : (
              'Definir endereço de envio'
            )}
          </Button>
        </form>
      </div>
    </div>
  );
} 