'use client'

import { Suspense } from 'react'
import { AuthCarousel } from '@/components/auth'
import { LoginForm } from '@/components/auth/login'
import { ResetPassword } from '@/components/auth/reset-password'
// import { ThemeToggle } from 'components/theme/theme-toggle'
import { SplashScreen } from '@/components/splash-screen'
import { useSearchParams } from 'next/navigation'
import { AuthLoading, AuthResult } from '@/components/auth'

function LoginContent() {
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const error = searchParams.get('error')
  
  if (error) {
    return (
      <AuthResult 
        type="error" 
        title="Erro de Autenticação" 
        message={decodeURIComponent(error)} 
      />
    )
  }
  
  return (
    <div className="relative flex w-full items-center justify-center bg-white/[0.96] px-4 dark:bg-gray-950/[0.96] sm:px-6 lg:w-1/2">
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-white/80 to-white/40 dark:from-gray-950 dark:via-gray-950/80 dark:to-gray-950/40" />
      {mode === 'reset-password' ? <ResetPassword /> : <LoginForm />}
    </div>
  )
}

/**
 * Adiciona uma mensagem informativa sobre o redirecionamento automático
 * após o login bem-sucedido, conforme a função do usuário.
 */
function LoginRedirectionInfo() {
  return (
    <div className="text-xs text-muted-foreground mt-2">
      <p>Após o login, você será redirecionado para a área apropriada:</p>
      <ul className="list-disc list-inside mt-1 ml-2">
        <li>Administradores: Dashboard administrativo</li>
        <li>Professores: Área do professor</li>
        <li>Alunos: Área do aluno</li>
      </ul>
    </div>
  );
}

export default function LoginPage() {
  return (
    <SplashScreen>
      <div className="relative flex min-h-screen overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="relative h-full w-full bg-white dark:bg-gray-950">
            <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
          </div>
        </div>

        <Suspense fallback={<AuthLoading />}>
          <LoginContent />
        </Suspense>

        {/* Right side - Carousel */}
        <div className="hidden lg:block lg:w-1/2">
          <AuthCarousel />
        </div>

        {/* Theme Toggle */}
        {/* <div className="absolute right-4 top-4 z-10">
          <ThemeToggle />
        </div> */}
      </div>
    </SplashScreen>
  )
} 