'use client';

import { useStudentsQuery } from '../../hooks/use-students-query';
import { Badge } from '@/components/ui/badge';

/**
 * Componente indicador do status do cache para debug
 * Mostra quando dados estão sendo carregados do cache vs. fetch
 */
export function CacheIndicator() {
  const { isLoading, isFetching, isStale, data } = useStudentsQuery();

  // Não mostrar o indicador em produção
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const getCacheStatus = () => {
    if (isLoading) return { status: 'Carregando inicial...', variant: 'secondary' as const };
    if (isFetching && data) return { status: 'Atualizando em background...', variant: 'outline' as const };
    if (isStale && data) return { status: 'Dados do cache (atualizando...)', variant: 'outline' as const };
    if (data && !isStale) return { status: 'Dados atualizados', variant: 'default' as const };
    return { status: 'Sem dados', variant: 'destructive' as const };
  };

  const { status, variant } = getCacheStatus();

  return (
    <div className="fixed top-4 right-4 z-50">
      <Badge variant={variant} className="text-xs">
        🔄 {status}
      </Badge>
    </div>
  );
} 