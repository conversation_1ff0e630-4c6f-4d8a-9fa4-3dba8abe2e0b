export interface SystemField {
  value: string;
  label: string;
  required: boolean;
  description: string;
}

// Lista de campos reconhecidos pelo sistema para importação de alunos
export const systemFields: SystemField[] = [
  {
    value: 'full_name',
    label: 'Nome Completo',
    required: true,
    description:
      'Nome completo do aluno (será dividido automaticamente em primeiro nome e sobrenome)',
  },
  {
    value: 'first_name',
    label: 'Primeiro Nome',
    required: false,
    description: 'Primeiro nome do aluno',
  },
  {
    value: 'last_name',
    label: 'Sobrenome',
    required: false,
    description: 'Sobrenome do aluno',
  },
  {
    value: 'email',
    label: 'E-mail',
    required: true,
    description: 'Endereço de e-mail válido',
  },
  {
    value: 'phone',
    label: 'Telefone',
    required: true,
    description: 'Número de telefone com DDD',
  },
  {
    value: 'birth_date',
    label: 'Data de Nascimento',
    required: false,
    description: 'Formato: DD/MM/AAAA ou AAAA-MM-DD',
  },
  {
    value: 'gender',
    label: '<PERSON><PERSON><PERSON><PERSON>',
    required: false,
    description: 'masculino, feminino, outro ou prefiro_nao_informar',
  },
  {
    value: 'street',
    label: 'Rua/Logradouro',
    required: false,
    description: 'Nome da rua, avenida, etc.',
  },
  {
    value: 'street_number',
    label: 'Número',
    required: false,
    description: 'Número do endereço',
  },
  {
    value: 'complement',
    label: 'Complemento',
    required: false,
    description: 'Apartamento, bloco, etc.',
  },
  {
    value: 'neighborhood',
    label: 'Bairro',
    required: false,
    description: 'Nome do bairro',
  },
  {
    value: 'city',
    label: 'Cidade',
    required: false,
    description: 'Nome da cidade',
  },
  {
    value: 'state',
    label: 'Estado',
    required: false,
    description: 'UF do estado (ex: SP, RJ)',
  },
  {
    value: 'postal_code',
    label: 'CEP',
    required: false,
    description: 'Código postal (ex: 12345-678)',
  },
  {
    value: 'emergency_contact_name',
    label: 'Nome do Contato de Emergência',
    required: false,
    description: 'Nome completo do contato de emergência',
  },
  {
    value: 'emergency_contact_phone',
    label: 'Telefone do Contato de Emergência',
    required: false,
    description: 'Telefone do contato de emergência',
  },
  {
    value: 'emergency_contact_relationship',
    label: 'Parentesco do Contato de Emergência',
    required: false,
    description: 'Relacionamento com o aluno (pai, mãe, etc.)',
  },
  {
    value: 'health_notes',
    label: 'Observações de Saúde',
    required: false,
    description: 'Observações gerais sobre a saúde',
  },
  {
    value: 'allergies',
    label: 'Alergias',
    required: false,
    description: 'Lista de alergias conhecidas',
  },
  {
    value: 'medical_conditions',
    label: 'Condições Médicas',
    required: false,
    description: 'Condições médicas relevantes',
  },
  {
    value: 'medications',
    label: 'Medicamentos',
    required: false,
    description: 'Medicamentos em uso regular',
  },
  {
    value: 'belt_color',
    label: 'Cor da Faixa',
    required: false,
    description: 'white, blue, purple, brown ou black (padrão: white)',
  },
  {
    value: 'belt_degree',
    label: 'Grau da Faixa',
    required: false,
    description: 'Número do grau (0-4, padrão: 0)',
  },
];

export interface ImportData {
  rowIndex: number;
  data: Record<string, string>;
  isValid: boolean;
  errors: Record<string, string>;
}

const emailRegex = /^[A-Za-z0-9.!#$%&'*+/=?^_`{|}~-]+@[A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?(?:\.[A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/;

import { validatePhoneNumber } from '@/utils/phone-utils';
import { validatePostalCode } from '@/components/perfil/EditableAddress/utils';

// Validação de data (DD/MM/AAAA ou AAAA-MM-DD) e verificação se não está no futuro
function isValidBirthDate(dateStr: string): boolean {
  const regexDMY = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/; // DD/MM/YYYY ou D/M/YYYY
  const regexYMD = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;     // YYYY-MM-DD

  let day: number, month: number, year: number;

  if (regexDMY.test(dateStr)) {
    const match = dateStr.match(regexDMY);
    if (!match) return false;
    day = parseInt(match[1], 10);
    month = parseInt(match[2], 10) - 1; // Mês é baseado em zero no JS Date
    year = parseInt(match[3], 10);
  } else if (regexYMD.test(dateStr)) {
    const match = dateStr.match(regexYMD);
    if (!match) return false;
    year = parseInt(match[1], 10);
    month = parseInt(match[2], 10) - 1;
    day = parseInt(match[3], 10);
  } else {
    return false; // Formato não reconhecido
  }

  // Verifica se data é válida (evita auto-correção do JS Date)
  const date = new Date(year, month, day);
  if (
    isNaN(date.getTime()) ||
    date.getDate() !== day ||
    date.getMonth() !== month ||
    date.getFullYear() !== year
  ) {
    return false;
  }

  // Data não pode estar no futuro
  const today = new Date();
  if (date > today) return false;

  // Data muito antiga (antes de 1900) provavelmente é inválida
  if (year < 1900) return false;

  return true;
}

/**
 * Realiza a validação das linhas selecionadas a partir dos mapeamentos informados.
 */
export function validateImportData(
  headers: string[],
  data: string[][],
  selectedRows: Set<number>,
  mappings: Record<string, string>,
  emptyFieldAction: 'allow' | 'skip_row' | 'error' = 'error',
): ImportData[] {
  const results: ImportData[] = [];

  const requiredFields = systemFields.filter(f => f.required).map(f => f.value);
  const mappedFields = Object.values(mappings);

  // Verificar se algum campo obrigatório não foi mapeado
  const missingRequiredFields = requiredFields.filter(field => !mappedFields.includes(field));

  data.forEach((row, rowIndex) => {
    if (!selectedRows.has(rowIndex)) return; // pular linhas não selecionadas

    const rowData: Record<string, string> = {};
    const errors: Record<string, string> = {};

    // Construir objeto de dados a partir dos mapeamentos
    headers.forEach((header, headerIndex) => {
      const mappedField = mappings[header];
      if (mappedField) {
        // Trim whitespace to ensure blank values are correctly identified as empty
        const cellValue = (row[headerIndex] || '').trim();
        rowData[mappedField] = cellValue;
      }
    });

    // Erros gerais: campos obrigatórios não mapeados
    if (missingRequiredFields.length > 0) {
      errors._form = missingRequiredFields
        .map(field => {
          const fieldInfo = systemFields.find(f => f.value === field);
          return `Campo obrigatório "${fieldInfo?.label}" não foi mapeado`;
        })
        .join('. ');
    }

    // Validação de preenchimento de campos obrigatórios mapeados
    requiredFields.forEach(field => {
      if (mappedFields.includes(field) && (!rowData[field] || rowData[field].trim() === '')) {
        const fieldInfo = systemFields.find(f => f.value === field);
        errors[field] = `Campo obrigatório "${fieldInfo?.label}" não preenchido`;
      }
    });

    // Validação de campos vazios (não obrigatórios) de acordo com configuração
    if (emptyFieldAction !== 'allow') {
      let hasEmpty = false;
      Object.entries(rowData).forEach(([field, value]) => {
        if (!value || value.trim() === '') {
          hasEmpty = true;
          if (emptyFieldAction === 'error') {
            // Se já existe erro para campo obrigatório, não sobrescrever
            if (!errors[field]) {
              const fieldInfo = systemFields.find(f => f.value === field);
              errors[field] = `Campo "${fieldInfo?.label}" está vazio`;
            }
          }
        }
      });

      if (emptyFieldAction === 'skip_row' && hasEmpty) {
        errors._row = 'Linha ignorada por conter campos vazios';
      }
    }

    // Validação de formato de e-mail
    if (rowData.email && !emailRegex.test(rowData.email)) {
      errors.email = 'Formato de e-mail inválido';
    }

    // Validação de telefone
    if (rowData.phone && !validatePhoneNumber(rowData.phone)) {
      errors.phone = 'Número de telefone inválido';
    }

    // Validação de telefone do contato de emergência
    if (rowData.emergency_contact_phone && !validatePhoneNumber(rowData.emergency_contact_phone)) {
      errors.emergency_contact_phone = 'Número de telefone de contato de emergência inválido';
    }

    // Validação de data de nascimento
    if (rowData.birth_date && !isValidBirthDate(rowData.birth_date)) {
      errors.birth_date =
        'Data de nascimento inválida. Use os formatos DD/MM/AAAA ou AAAA-MM-DD';
    }

    // Validação de gênero
    if (
      rowData.gender &&
      !['masculino', 'feminino', 'outro', 'prefiro_nao_informar'].includes(rowData.gender.toLowerCase())
    ) {
      errors.gender = 'Gênero deve ser: masculino, feminino, outro ou prefiro_nao_informar';
    }

    // Validação de cor de faixa
    if (
      rowData.belt_color &&
      !['white', 'blue', 'purple', 'brown', 'black'].includes(rowData.belt_color.toLowerCase())
    ) {
      errors.belt_color = 'Cor da faixa deve ser: white, blue, purple, brown ou black';
    }

    // Validação de grau da faixa
    if (
      rowData.belt_degree &&
      (isNaN(Number(rowData.belt_degree)) || Number(rowData.belt_degree) < 0 || Number(rowData.belt_degree) > 6)
    ) {
      errors.belt_degree = 'Grau da faixa deve ser um número entre 0 e 6';
    }

    // Validação de número da casa (street_number)
    if (rowData.street_number) {
      const streetNumberRegex = /^\d[0-9A-Za-z-]*$/; // começa com dígito e pode conter letras ou hífen
      if (!streetNumberRegex.test(rowData.street_number)) {
        errors.street_number = 'Número do endereço deve começar com dígito e conter apenas letras, números ou hífen';
      }
    }

    // Validação de CEP (postal_code)
    if (rowData.postal_code && !validatePostalCode(rowData.postal_code)) {
      errors.postal_code = 'CEP inválido. O formato deve ser XXXXX-XXX ou XXXXXXXX';
    }

    const isValid = Object.keys(errors).length === 0;

    // Se a ação for skip_row e há campos vazios, consideramos inválido para fins de importação
    results.push({
      rowIndex,
      data: rowData,
      isValid,
      errors,
    });
  });

  return results;
} 