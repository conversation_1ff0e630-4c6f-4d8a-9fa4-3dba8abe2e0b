# Implementação de Multi-Tenancy no Supabase

Este documento descreve a implementação de multi-tenancy no projeto ApexSaaS utilizando Supabase.

## Visão Geral

A implementação de multi-tenancy permite que:

1. Cada tenant (academia) tenha seus próprios dados isolados
2. Usuários só possam acessar dados do seu próprio tenant
3. O isolamento seja garantido por políticas de segurança no nível do banco de dados

## Arquivos de Implementação

- `tenant_function.sql`: Define funções auxiliares como `tenant_auth.tenant_id()` e `auth.role()`
- `tenant_policies.sql`: Aplica políticas RLS básicas em todas as tabelas que contêm `tenant_id`
- `tenant_policies_enhanced.sql`: Implementa políticas RLS mais robustas com controle por operação e papel de usuário
- `tenant_indexes.sql`: Otimiza os índices para consultas que envolvem tenant_id
- `src/app/actions.ts`: Implementa o uso de `app_metadata` para armazenar o tenant_id durante o cadastro e login

## Como Funciona

### 1. Armazenamento do Tenant ID

Durante o cadastro, o tenant_id é armazenado em dois lugares:

- Em `app_metadata` do usuário autenticado (via Supabase Auth)
- Na tabela `users` como uma coluna

### 2. Funções Auxiliares

**Importante**: As funções customizadas foram movidas do schema `auth` para o schema `tenant_auth` devido às novas restrições do Supabase (julho 2025).

O sistema utiliza várias funções auxiliares:

```sql
-- Obtém o tenant_id do usuário autenticado
create or replace function tenant_auth.tenant_id() 
returns text
language sql stable
as $$
  select 
   nullif(
       ((current_setting('request.jwt.claims')::jsonb -> 'app_metadata')::jsonb ->> 'tenant_id'),
     ''
     )::text
$$;

-- Verifica se o usuário tem acesso ao tenant especificado
create or replace function tenant_auth.has_tenant_access(tenant_id text)
returns boolean
language sql stable
as $$
  select 
    tenant_auth.tenant_id() = tenant_id
$$;

-- Verifica se o usuário é proprietário do registro
create or replace function tenant_auth.is_owner(record_user_id uuid)
returns boolean
language sql stable
as $$
  select 
    auth.uid() = record_user_id
$$;

-- Obtém o papel (role) do usuário autenticado (mantida no schema auth)
create or replace function auth.role() 
returns text
language sql stable
as $$
  select 
    coalesce(
      (select role from public.users where id = auth.uid()),
      'anonymous'
    )::text
$$;
```

### 3. Políticas RLS Robustas

As políticas RLS são separadas por operação (SELECT, INSERT, UPDATE, DELETE) e consideram:

- Isolamento por tenant: `tenant_auth.tenant_id()::text = tenant_id::text`
- Papel do usuário: `auth.role() = 'admin'::text`
- Soft delete: `deleted_at IS NULL`
- Propriedade de registros: `user_id = auth.uid()`

Exemplo:

```sql
CREATE POLICY users_tenant_select
ON public.users
FOR SELECT
TO authenticated
USING (
  tenant_auth.tenant_id()::text = tenant_id::text
  AND deleted_at IS NULL
);
```

### 4. Otimização de Índices

Para melhorar a performance das consultas que usam as políticas RLS, adicionamos índices que incluem `tenant_id`:

```sql
-- Índice para busca de usuários por email dentro de um tenant
CREATE INDEX IF NOT EXISTS idx_users_email_tenant_id ON public.users (email, tenant_id);

-- Índice para busca de filiais por nome dentro de um tenant
CREATE INDEX IF NOT EXISTS idx_branches_name_tenant_id ON public.branches (name, tenant_id);
```

### 5. Verificações Durante a Autenticação

No processo de login, verificamos se o usuário pertence ao tenant que está tentando acessar:

1. Verificação antes da autenticação (via tabela `users`)
2. Verificação após a autenticação (via `app_metadata` e tabela `users`)

## Migração das Funções Auth (Julho 2025)

Em julho de 2025, o Supabase implementou restrições que impedem a criação de funções customizadas nos schemas internos (`auth`, `storage`, `realtime`). Como resultado, todas as funções customizadas foram migradas para o schema `tenant_auth`:

### Funções Migradas:
- `auth.tenant_id()` → `tenant_auth.tenant_id()`
- `auth.has_tenant_access()` → `tenant_auth.has_tenant_access()`
- `auth.is_owner()` → `tenant_auth.is_owner()`

### Políticas Atualizadas:
Todas as políticas RLS foram atualizadas automaticamente para usar o novo namespace `tenant_auth.*`.

### Impacto:
- ✅ **Zero downtime**: A migração foi aplicada sem interrupção do serviço
- ✅ **Funcionalidade mantida**: Todas as funcionalidades de multi-tenancy continuam funcionando
- ✅ **Compatibilidade**: O código da aplicação não precisou ser alterado

## Benefícios

- **Segurança**: O isolamento acontece no nível do banco de dados, não apenas na aplicação
- **Performance**: Usando `app_metadata` e índices otimizados para consultas com tenant_id
- **Consistência**: As políticas RLS garantem que todas as consultas respeitem o isolamento entre tenants
- **Granularidade**: Controle de acesso por papel de usuário e tipo de operação
- **Conformidade**: Alinhado com as novas restrições de segurança do Supabase

## Considerações Adicionais

1. As políticas RLS têm impacto na performance, por isso a otimização de índices é crucial
2. A utilização de `app_metadata` mantém o tenant_id seguro, pois só pode ser modificado por administradores
3. As funções auxiliares como `tenant_auth.tenant_id()` e `auth.role()` facilitam a criação de políticas consistentes
4. As funções no schema `tenant_auth` estão sob nosso controle e não serão afetadas por futuras mudanças do Supabase 