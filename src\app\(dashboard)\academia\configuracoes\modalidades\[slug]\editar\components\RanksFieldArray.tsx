'use client'

import { useFieldArray, useFormContext } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { JiuJitsuBelt } from '@/components/belt/JiuJitsuBelt'
import { ColorPicker } from '@/components/ui/color-picker'
import { PlusCircle, Trash2, GripVertical, Info, AlertTriangle } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  KeyboardSensor,
} from '@dnd-kit/core'
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

export default function RanksFieldArray() {
  const { control, register, setValue, watch } = useFormContext()

  const {
    fields,
    append,
    remove,
    insert,
    move,
  } = useFieldArray({
    control,
    name: 'levels',
  })

  const requireSessions = watch('require_sessions')
  const requireMinAge = watch('require_minimum_age')
  const watchedLevels = watch('levels')

  // Função para detectar graduações duplicadas
  const detectDuplicates = (): Array<{ index: number; key: string; name: string }> => {
    const combinations = new Map<string, number>()
    const duplicates: Array<{ index: number; key: string; name: string }> = []
    
    watchedLevels?.forEach((level: any, index: number) => {
      const key = `${level.belt_color}-${level.degree}`
      if (combinations.has(key)) {
        duplicates.push({ index, key, name: level.name || 'Sem nome' })
      } else {
        combinations.set(key, index)
      }
    })
    
    return duplicates
  }

  const duplicates = detectDuplicates()

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // Handler fired after a drag operation finishes
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    if (!over || active.id === over.id) return

    const oldIndex = fields.findIndex(f => f.id === active.id)
    const newIndex = fields.findIndex(f => f.id === over.id)
    move(oldIndex, newIndex)
  }

  // Função para adicionar nova graduação com valores padrão mais intuitivos
  const addNewRank = () => {
    const lastRank = fields[fields.length - 1]
    const newDegree = lastRank ? (lastRank as any).degree + 1 : 0
    
    append({
      name: `Nova Graduação ${fields.length + 1}`,
      degree: newDegree > 6 ? 0 : newDegree, // Reset para 0 se passar de 6 graus
      belt_color: '#ffffff',
      stripe_color: '#ffffff',
      sessions: 0,
      minimum_age: 0,
      show_center_line: false,
      center_line_color: '',
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Níveis / Graduações</h3>
          <p className="text-sm text-muted-foreground">
            Gerencie as graduações desta modalidade. Você pode adicionar, remover e reordenar livremente.
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addNewRank}
          className="flex items-center gap-2"
        >
          <PlusCircle className="h-4 w-4" />
          Adicionar Graduação
        </Button>
      </div>

      {/* Alerta para graduações duplicadas */}
      {/* {duplicates.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Graduações duplicadas detectadas:</strong>
            <ul className="mt-1 list-disc list-inside">
              {duplicates.map((dup, idx) => (
                <li key={idx}>
                  Linha {dup.index + 1}: {dup.name || 'Sem nome'} ({dup.key})
                </li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )} */}

      {fields.length === 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Nenhuma graduação configurada. Clique em "Adicionar Graduação" para criar a primeira graduação 
            ou use um preset na seção acima para aplicar graduações predefinidas.
          </AlertDescription>
        </Alert>
      )}

      {fields.length > 0 && (
        <>
          {/* Header row */}
          <div className="hidden md:flex items-center gap-3 px-2 text-xs font-semibold text-muted-foreground uppercase select-none">
            <span className="w-4 shrink-0" />
            <span className="flex-1 min-w-[180px]">Graduação</span>
            <span className="w-24 shrink-0">Graus</span>
            <span className="w-[232px] shrink-0 text-center">Cor da Faixa</span>
            {requireSessions && (
              <span className="w-40 shrink-0">Aulas</span>
            )}
            {requireMinAge && (
              <span className="w-40 shrink-0">Idade Mínima</span>
            )}
            <span className="w-20 shrink-0">Ações</span>
          </div>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={fields.map(f => f.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-3">
                {fields.map((field, index) => (
                  <RankRow
                    key={field.id}
                    fieldId={field.id}
                    index={index}
                    requireSessions={requireSessions}
                    requireMinAge={requireMinAge}
                    register={register}
                    setValue={setValue}
                    watch={watch}
                    insert={insert}
                    remove={remove}
                    totalFields={fields.length}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>

          <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
            <p className="font-medium mb-1">💡 Dicas:</p>
            <ul className="space-y-1">
              <li>• Arraste e solte as graduações para reordená-las</li>
              <li>• Use o botão "+" em cada linha para inserir uma nova graduação na posição</li>
              <li>• As graduações são salvas automaticamente ao submeter o formulário</li>
              <li>• Graduações com alunos atribuídos não podem ser removidas</li>
            </ul>
          </div>
        </>
      )}
    </div>
  )
}

// ------------------------
// Sortable row component
// ------------------------
interface RankRowProps {
  fieldId: string
  index: number
  requireSessions: boolean
  requireMinAge: boolean
  register: ReturnType<typeof useFormContext>['register']
  setValue: ReturnType<typeof useFormContext>['setValue']
  watch: ReturnType<typeof useFormContext>['watch']
  insert: ReturnType<typeof useFieldArray>['insert']
  remove: ReturnType<typeof useFieldArray>['remove']
  totalFields: number
}

function RankRow({
  fieldId,
  index,
  requireSessions,
  requireMinAge,
  register,
  setValue,
  watch,
  insert,
  remove,
  totalFields,
}: RankRowProps) {
  const level = watch(`levels.${index}`)

  const {
    setNodeRef,
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: fieldId })

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
  }

  const stripeColorDefault = '#ffffff'

  const insertNewRank = () => {
    insert(index + 1, {
      name: `Nova Graduação ${index + 2}`,
      degree: 0,
      belt_color: '#ffffff',
      stripe_color: '#ffffff',
      sessions: 0,
      minimum_age: 0,
      show_center_line: false,
      center_line_color: '',
    })
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-3 p-2 border rounded-md w-full bg-background hover:bg-muted/50 transition-colors"
    >
      {/* Drag handle */}
      <div 
        className="shrink-0 cursor-grab hover:text-primary transition-colors" 
        {...attributes} 
        {...listeners}
        title="Arrastar para reordenar"
      >
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </div>

      {/* Rank name */}
      <div className="flex-1 min-w-[180px]">
        <Input
          {...register(`levels.${index}.name` as const)}
          placeholder="Nome da graduação"
          className="w-full"
        />
      </div>

      {/* Degree & stripe color */}
      <div className="shrink-0 w-40 flex items-center gap-2">
        <Select
          onValueChange={value =>
            setValue(`levels.${index}.degree`, parseInt(value, 10))
          }
          value={String(level?.degree || 0)}
        >
          <SelectTrigger className="w-24">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Array.from(Array(7).keys()).map(i => (
              <SelectItem key={i} value={String(i)}>
                {i === 0 ? 'Faixa Pura' : `${i}º Grau`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <ColorPicker
          value={level?.stripe_color || stripeColorDefault}
          onChange={value => setValue(`levels.${index}.stripe_color`, value)}
          compact
          allowClear
        />
      </div>

      {/* Belt visuals */}
      <div className="shrink-0 w-[232px] flex flex-col items-center gap-2">
        <JiuJitsuBelt
          beltColor={level?.belt_color || '#ffffff'}
          stripeColor={level?.stripe_color}
          degrees={level?.degree || 0}
          stripePosition="middle"
          className="h-6 w-24"
          showCenterLine={!!level?.center_line_color}
          centerLineColor={level?.center_line_color}
        />
        <div className="flex items-center gap-3">
          <div className="text-center">
            <ColorPicker
              value={level?.belt_color || '#ffffff'}
              onChange={value => {
                setValue(`levels.${index}.belt_color`, value)
                const isNewColorBlack =
                  value.toLowerCase() === '#000000' ||
                  value.toLowerCase() === 'black'
                const currentStripeColor = watch(
                  `levels.${index}.stripe_color`,
                )

                if (isNewColorBlack && currentStripeColor === '#dc2626') {
                  setValue(`levels.${index}.stripe_color`, '#ffffff')
                }
              }}
              compact
              allowClear
            />
            <p className="text-xs text-muted-foreground mt-1">Cor</p>
          </div>

          <div className="text-center">
            <ColorPicker
              value={level?.center_line_color || ''}
              onChange={value => {
                setValue(`levels.${index}.center_line_color`, value)
                setValue(`levels.${index}.show_center_line`, !!value)
              }}
              compact
              allowClear
            />
            <p className="text-xs text-muted-foreground mt-1">Linha</p>
          </div>
        </div>
      </div>

      {/* Sessions requirement */}
      {requireSessions && (
        <div className="shrink-0 w-40">
          <div className="flex items-center w-full">
            <Input
              type="number"
              {...register(`levels.${index}.sessions` as const, {
                valueAsNumber: true,
                setValueAs: (value) => {
                  const num = parseFloat(value);
                  return isNaN(num) ? 0 : num;
                },
              })}
              placeholder="0"
              className="w-full rounded-r-none"
              min="0"
              value={level?.sessions || 0}
            />
            <span className="flex-shrink-0 border border-l-0 border-input bg-background rounded-r-md px-3 py-2 text-sm text-muted-foreground select-none h-10">
              Aulas
            </span>
          </div>
        </div>
      )}

      {/* Minimum age */}
      {requireMinAge && (
        <div className="shrink-0 w-40">
          <div className="flex items-center w-full">
            <Input
              type="number"
              {...register(`levels.${index}.minimum_age` as const, {
                valueAsNumber: true,
                setValueAs: (value) => {
                  const num = parseFloat(value);
                  return isNaN(num) ? 0 : num;
                },
              })}
              placeholder="0"
              className="w-full rounded-r-none"
              min="0"
              value={level?.minimum_age || 0}
            />
            <span className="flex-shrink-0 border border-l-0 border-input bg-background rounded-r-md px-3 py-2 text-sm text-muted-foreground select-none h-10">
              Anos
            </span>
          </div>
        </div>
      )}

      {/* Row actions */}
      <div className="shrink-0 flex items-center">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={insertNewRank}
          title="Inserir nova graduação após esta"
          className="hover:bg-primary/10 hover:text-primary"
        >
          <PlusCircle className="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={() => remove(index)}
          disabled={totalFields <= 1}
          title={totalFields <= 1 ? "Não é possível remover a última graduação" : "Remover esta graduação"}
          className="hover:bg-destructive/10 hover:text-destructive disabled:opacity-50"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
} 