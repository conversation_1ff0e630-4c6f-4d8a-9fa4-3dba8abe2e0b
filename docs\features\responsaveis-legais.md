# Funcionalidade de Responsáveis Legais

## Visão Geral

A funcionalidade de responsáveis legais foi implementada para permitir que usuários menores de 18 anos tenham suas contas administradas por responsáveis legais (pais, tutores, etc.).

## Características Principais

### 1. Detecção Automática de Menores
- O sistema calcula automaticamente se um usuário é menor de idade baseado na data de nascimento
- Trigger no banco de dados atualiza automaticamente o status quando a data de nascimento é alterada
- Status é recalculado em tempo real na interface

### 2. Campos de Responsável
- **Nome completo**: Nome do responsável legal
- **E-mail**: E-mail de contato do responsável
- **Telefone**: Telefone formatado (11) 99999-9999
- **Relação**: Tipo de relação (pai, mãe, avô, etc.)
- **Documento**: CPF do responsável com validação

### 3. Validações Implementadas
- Validação de CPF com dígitos verificadores
- Validação de e-mail
- Validação de telefone com formatação brasileira
- Campos obrigatórios para menores de idade

## Estrutura do Banco de Dados

### Novos Campos na Tabela `users`
```sql
-- Campos para identificação de menor
is_minor BOOLEAN DEFAULT FALSE
is_guardian_account BOOLEAN DEFAULT FALSE
managed_student_id UUID REFERENCES students(id)

-- Dados do responsável
guardian_name TEXT
guardian_email TEXT
guardian_phone TEXT
guardian_relationship TEXT
guardian_document TEXT
```

### Novos Campos na Tabela `students`
```sql
is_minor BOOLEAN DEFAULT FALSE
requires_guardian_consent BOOLEAN DEFAULT FALSE
```

### Funções e Triggers
- `calculate_is_minor(birth_date)`: Calcula se é menor de idade
- `update_minor_status()`: Trigger que atualiza status automaticamente

## Componentes Frontend

### 1. ProfileHeader com Informações de Responsável
```tsx
<ProfileHeaderGuardianInfo userId={userId} />
```

### 2. Cartão de Informações do Responsável
```tsx
<GuardianInfoCard 
  displayInfo={displayInfo}
  canEdit={canEdit}
  onEditGuardian={handleEdit}
/>
```

### 3. Formulário de Responsável
```tsx
<GuardianForm
  userId={userId}
  initialData={guardianData}
  onCancel={handleCancel}
  onSuccess={handleSuccess}
/>
```

### 4. Badge de Status
```tsx
<MinorStatusBadge displayInfo={displayInfo} />
<InlineGuardianInfo displayInfo={displayInfo} />
```

## Server Actions

### Principais Actions
- `updateGuardianInfoAction`: Atualizar dados do responsável
- `setupManagedAccountAction`: Configurar conta administrada
- `checkGuardianConfiguredAction`: Verificar se tem responsável
- `getUserWithGuardianInfoAction`: Buscar dados completos
- `canActAsGuardianAction`: Verificar permissões

## Hooks Customizados

### useGuardianInfo
```tsx
const { user, displayInfo, loading, error, refetch } = useGuardianInfo(userId);
```

### useGuardianStatus
```tsx
const { hasGuardian, loading, error } = useGuardianStatus(userId);
```

### useMinorStatus
```tsx
const { isMinor, loading, error } = useMinorStatus(userId);
```

**Mudança importante**: Este hook agora busca o status de menor de idade diretamente do banco de dados através da coluna `is_minor`, em vez de calcular baseado na data de nascimento. Isso garante consistência com o status armazenado no banco e melhora a performance.

## Tipos TypeScript

### Interfaces Principais
```tsx
interface GuardianInfo {
  guardian_name: string | null;
  guardian_email: string | null;
  guardian_phone: string | null;
  guardian_relationship: string | null;
  guardian_document: string | null;
}

interface MinorAccountInfo {
  is_minor: boolean;
  is_guardian_account: boolean;
  managed_student_id: string | null;
}

interface MinorAccountDisplayInfo {
  isMinor: boolean;
  hasGuardian: boolean;
  guardianName?: string;
  guardianRelationship?: string;
  requiresGuardianConsent: boolean;
}
```

## Páginas de Administração

### Página de Gerenciamento
- **Rota**: `/academia/configuracoes/responsaveis`
- **Funcionalidade**: Listar menores sem responsável configurado
- **Ações**: Configurar responsável para cada menor

## Fluxo de Uso

### 1. Para Administradores
1. Acessar página de gerenciamento de responsáveis
2. Visualizar lista de menores sem responsável
3. Configurar dados do responsável para cada menor
4. Validar informações e salvar

### 2. Para Usuários com Menores
1. Visualizar perfil do menor
2. Ver badge indicando necessidade de responsável
3. Editar dados do responsável
4. Preencher formulário completo

### 3. Para Responsáveis (Futuro)
- Login com e-mail de responsável
- Visualizar contas administradas
- Gerenciar atividades dos menores

## Validações e Regras de Negócio

### Regras de Idade
- Menor de idade: < 18 anos
- Status atualizado automaticamente via trigger
- Remoção automática ao completar 18 anos

### Regras de Validação
- CPF válido com dígitos verificadores
- E-mail válido para responsável
- Telefone no formato brasileiro
- Relacionamento obrigatório

### Segurança
- Dados do responsável criptografados
- Validação no servidor e cliente
- Auditoria de mudanças

## Migrations

### Como Aplicar
```bash
# Aplicar a migração manualmente (banco em read-only)
# Arquivo: migrations/20241222_add_guardian_fields_for_minors.sql
```

## Extensões Futuras

### Funcionalidades Planejadas
1. **Login de Responsáveis**: Permitir que responsáveis façam login
2. **Dashboard de Responsáveis**: Interface específica para administrar contas
3. **Notificações**: Alertas por e-mail para responsáveis
4. **Relatórios**: Atividades dos menores para responsáveis
5. **Aprovações**: Sistema de aprovação para atividades específicas

### Integrações Possíveis
- Sistema de notificações por e-mail
- WhatsApp API para comunicação
- Assinatura digital para termos
- Integração com documentos oficiais

## Monitoramento e Logs

### Eventos Auditados
- Criação/atualização de dados de responsável
- Tentativas de acesso por menores
- Mudanças de status de maioridade
- Ações realizadas em nome de menores

### Métricas
- Número de menores com/sem responsável
- Taxa de configuração de responsáveis
- Atividade de responsáveis

## Considerações de Performance

### Otimizações Implementadas
- Cache em hooks customizados
- Queries otimizadas com Supabase
- Triggers eficientes no banco
- Componentes com memoização

### Boas Práticas
- Lazy loading de formulários
- Validação progressiva
- Estados de loading adequados
- Tratamento de erros robusto 