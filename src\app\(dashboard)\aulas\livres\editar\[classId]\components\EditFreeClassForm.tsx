'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { updateClass } from '../../../../actions';
import { toast } from 'sonner';
import type { ClassWithDetails } from '../../../../types';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Loader2, Save, X } from 'lucide-react';
import { formatToDatetimeLocal, datetimeLocalToBrasiliaISO } from '@/utils/timezone-utils';
import { editFreeClassSchema, type EditFreeClassFormData } from './schema';
import { DebugPanel } from './DebugPanel';

interface EditFreeClassFormProps {
  classId: string;
  classData: ClassWithDetails;
  instructors: Array<{ id: string; name: string; }>;
  branches: Array<{ id: string; name: string; }>;
  redirectPath?: string;
}

export function EditFreeClassForm({
  classId,
  classData,
  instructors,
  branches,
  redirectPath = '/aulas/livres'
}: EditFreeClassFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Verificar se a aula pode ser editada
  const canEdit = classData.status !== 'completed' && !classData.attendance_recorded;

  if (!canEdit) {
    return (
      <div className="p-6 bg-muted/50 rounded-lg border border-muted">
        <div className="text-center space-y-2">
          <h3 className="text-lg font-medium text-foreground">
            Edição não permitida
          </h3>
          <p className="text-muted-foreground">
            {classData.status === 'completed' 
              ? 'Esta aula já foi concluída e não pode ser editada.'
              : 'Esta aula já possui presença registrada e não pode ser editada.'}
          </p>
        </div>
      </div>
    );
  }

  // Converter os dados da aula para o formato esperado pelo formulário
  const defaultValues: EditFreeClassFormData = {
    name: classData.name,
    description: classData.description || '',
    instructor_id: classData.instructor_id,
    branch_id: classData.branch_id,
    start_time: formatToDatetimeLocal(classData.start_time),
    end_time: formatToDatetimeLocal(classData.end_time),
    max_capacity: classData.max_capacity || undefined,
    notes: ''
  };

  const form = useForm<EditFreeClassFormData>({
    resolver: zodResolver(editFreeClassSchema),
    defaultValues,
    mode: 'onChange'
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = form;

  const watchedValues = watch();

  const handleFormSubmit = async (formData: EditFreeClassFormData) => {
    startTransition(async () => {
      try {
        // Converter dados do formulário para o formato de atualização
        const updateData = {
          name: formData.name.trim(),
          description: formData.description?.trim() || undefined,
          instructor_id: formData.instructor_id,
          branch_id: formData.branch_id,
          start_time: datetimeLocalToBrasiliaISO(formData.start_time),
          end_time: datetimeLocalToBrasiliaISO(formData.end_time),
          max_capacity: formData.max_capacity && formData.max_capacity !== '' 
            ? Number(formData.max_capacity) 
            : undefined,
        };

        const result = await updateClass(classId, updateData);

        if (!result.success) {
          toast.error(result.error || 'Erro ao atualizar aula');
          return;
        }

        toast.success('Aula atualizada com sucesso!');
        router.push(redirectPath);
      } catch (error) {
        console.error('Erro no formulário:', error);
        toast.error('Erro inesperado ao atualizar aula');
      }
    });
  };

  // Calcular duração da aula
  const getDuration = () => {
    if (!watchedValues.start_time || !watchedValues.end_time) return null;
    
    try {
      const start = new Date(watchedValues.start_time);
      const end = new Date(watchedValues.end_time);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) return null;
      
      const diffMs = end.getTime() - start.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      
      if (diffMinutes <= 0) return null;
      
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      
      let text = '';
      if (hours > 0) {
        text += `${hours}h`;
        if (minutes > 0) text += ` ${minutes}min`;
      } else {
        text = `${minutes}min`;
      }
      
      return { text, minutes: diffMinutes };
    } catch (error) {
      return null;
    }
  };

  const duration = getDuration();

  return (
    <div className="space-y-4">
      {/* Informações da aula atual */}
      <div className="p-4 bg-muted/30 rounded-lg border border-muted">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-foreground">Aula: {classData.name}</h4>
            <p className="text-sm text-muted-foreground">
              Status: <span className="capitalize">{classData.status}</span>
              {classData.instructor && (
                <> • Instrutor: {classData.instructor.full_name || 
                  `${classData.instructor.first_name} ${classData.instructor.last_name || ''}`.trim()}
                </>
              )}
            </p>
          </div>
          <div className="text-right text-sm text-muted-foreground">
            <p>ID: {classData.id}</p>
            <p>Criada em: {new Date(classData.created_at).toLocaleDateString('pt-BR')}</p>
          </div>
        </div>
      </div>

      {/* Formulário */}
      <div className="relative">
        {isPending && (
          <div className="absolute inset-0 bg-background/50 backdrop-blur-sm z-10 flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm text-muted-foreground">Salvando alterações...</span>
            </div>
          </div>
        )}
        
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Informações Básicas */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-foreground">Nome da Aula *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Ex: Workshop de Defesa Pessoal"
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label className="text-foreground">Tipo de Aula</Label>
              <div className="px-3 py-2 bg-muted/50 border border-muted rounded-md text-sm text-muted-foreground">
                Aula Livre
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-foreground">Descrição</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Descreva o conteúdo e objetivos da aula..."
              rows={3}
              className={errors.description ? 'border-destructive' : ''}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>

          {/* Instrutor e Filial */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="instructor_id" className="text-foreground">Instrutor *</Label>
              <Select 
                value={watchedValues.instructor_id} 
                onValueChange={(value) => setValue('instructor_id', value, { shouldValidate: true })}
              >
                <SelectTrigger className={errors.instructor_id ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Selecione o instrutor" />
                </SelectTrigger>
                <SelectContent>
                  {instructors.map(instructor => (
                    <SelectItem key={instructor.id} value={instructor.id}>
                      {instructor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.instructor_id && (
                <p className="text-sm text-destructive">{errors.instructor_id.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="branch_id" className="text-foreground">Filial *</Label>
              <Select 
                value={watchedValues.branch_id} 
                onValueChange={(value) => setValue('branch_id', value, { shouldValidate: true })}
              >
                <SelectTrigger className={errors.branch_id ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Selecione a filial" />
                </SelectTrigger>
                <SelectContent>
                  {branches.map(branch => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.branch_id && (
                <p className="text-sm text-destructive">{errors.branch_id.message}</p>
              )}
            </div>
          </div>

          {/* Data e Horário */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="start_time" className="text-foreground">Data e Hora de Início *</Label>
              <Input
                id="start_time"
                type="datetime-local"
                {...register('start_time')}
                className={errors.start_time ? 'border-destructive' : ''}
              />
              {errors.start_time && (
                <p className="text-sm text-destructive">{errors.start_time.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_time" className="text-foreground">Data e Hora de Término *</Label>
              <Input
                id="end_time"
                type="datetime-local"
                {...register('end_time')}
                className={errors.end_time ? 'border-destructive' : ''}
              />
              {errors.end_time && (
                <p className="text-sm text-destructive">{errors.end_time.message}</p>
              )}
              {duration && (
                <p className="text-xs text-muted-foreground">
                  Duração: {duration.text}
                </p>
              )}
            </div>
          </div>

          {/* Configurações */}
          <div className="space-y-2">
            <Label htmlFor="max_capacity" className="text-foreground">Capacidade Máxima</Label>
            <Input
              id="max_capacity"
              type="number"
              min="1"
              max="100"
              {...register('max_capacity')}
              placeholder="Ex: 20 (deixe vazio para ilimitado)"
              className={errors.max_capacity ? 'border-destructive' : ''}
            />
            {errors.max_capacity && (
              <p className="text-sm text-destructive">{errors.max_capacity.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Deixe vazio para capacidade ilimitada
            </p>
          </div>

          {/* Botões */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-border">
            <Button 
              type="button" 
              variant="outline"
              onClick={() => router.push(redirectPath)}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || !isDirty}
            >
              {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              <Save className="h-4 w-4 mr-2" />
              Salvar Alterações
            </Button>
          </div>
        </form>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        form={form}
        errors={errors}
        duration={duration}
        isPending={isPending}
      />
    </div>
  );
} 