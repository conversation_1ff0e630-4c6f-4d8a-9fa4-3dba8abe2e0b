'use client'

import React from 'react'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { X, Calendar, Clock, MapPin, User, Tag } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useCalendar } from '../contexts/CalendarContext'
import { cn } from '@/lib/utils'

export function EventModal() {
  const { selectedEvent, isModalOpen, setIsModalOpen, setSelectedEvent } = useCalendar()

  const handleClose = () => {
    setIsModalOpen(false)
    setSelectedEvent(null)
  }

  if (!selectedEvent) return null

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { 
        label: 'Agendado', 
        variant: 'secondary' as const,
        className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
      },
      ongoing: { 
        label: 'Em Andamento', 
        variant: 'default' as const,
        className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
      },
      completed: { 
        label: 'Concluído', 
        variant: 'outline' as const,
        className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
      },
      cancelled: { 
        label: 'Cancelado', 
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
      }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    )
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'class':
        return 'Aula'
      case 'appointment':
        return 'Atendimento'
      case 'event':
        return 'Evento'
      default:
        return 'Indefinido'
    }
  }

  return (
    <Dialog open={isModalOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Detalhes do Evento</span>
            {/* <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button> */}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações do cliente/participante */}
          {selectedEvent.customer && (
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={selectedEvent.customer.avatar} />
                <AvatarFallback>
                  {selectedEvent.customer.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium text-foreground">
                  {selectedEvent.customer.name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {selectedEvent.code}
                </div>
              </div>
            </div>
          )}

          {/* Título do evento */}
          <div>
            <h3 className="font-semibold text-lg text-foreground mb-2">
              {selectedEvent.title}
            </h3>
            <div className="flex items-center gap-2">
              {getStatusBadge(selectedEvent.status)}
              <Badge variant="outline" className="text-xs">
                <Tag className="h-3 w-3 mr-1" />
                {getTypeLabel(selectedEvent.type)}
              </Badge>
            </div>
          </div>

          {/* Detalhes do serviço */}
          <div className="space-y-3">
            <div className="flex items-center text-sm">
              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="font-medium">Turma:</span>
              <span className="ml-2 text-muted-foreground">
                {selectedEvent.service}
              </span>
            </div>

            <div className="flex items-center text-sm">
              <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="font-medium">Data e Horário:</span>
              <span className="ml-2 text-muted-foreground">
                {format(selectedEvent.startTime, "dd 'de' MMMM, yyyy", { locale: ptBR })}
                {' às '}
                {format(selectedEvent.startTime, 'HH:mm')}
                {' - '}
                {format(selectedEvent.endTime, 'HH:mm')}
              </span>
            </div>

            <div className="flex items-center text-sm">
              <User className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="font-medium">Instrutor:</span>
              <span className="ml-2 text-muted-foreground">
                {selectedEvent.stylist}
              </span>
            </div>

            {selectedEvent.location && (
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="font-medium">Local:</span>
                <span className="ml-2 text-muted-foreground">
                  {selectedEvent.location}
                </span>
              </div>
            )}
          </div>

          {/* Ações */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 