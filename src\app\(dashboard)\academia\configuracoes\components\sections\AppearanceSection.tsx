'use client';

import LogoSection from './LogoSection';
import { useAppearanceManagement } from '../../hooks/use-appearance-management';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Plus, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface SlideForm {
  title: string;
  subtitle: string;
  description: string;
  gradient: string;
}

export default function AppearanceSection() {
  const {
    data,
    isLoading,
    isSavingColors,
    isSavingCarousel,
    saveColors,
    saveCarousel,
  } = useAppearanceManagement();

  const [primaryColor, setPrimaryColor] = useState<string | ''>('');
  const [slides, setSlides] = useState<SlideForm[]>([]);
  
  const [colorSaved, setColorSaved] = useState(false);
  const [carouselSaved, setCarouselSaved] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      setPrimaryColor(data.primaryColor || '');
      try {
        if (data.description) {
          const parsed = JSON.parse(data.description);
          if (Array.isArray(parsed)) {
            setSlides(parsed.map((s: any) => ({
              title: s.title || '',
              subtitle: s.subtitle || '',
              description: s.description || '',
              gradient: s.gradient || '',
            })) as SlideForm[]);
          }
        }
      } catch (err) {
        console.error('Erro ao parsear description:', err);
      }
    }
  }, [isLoading, data]);

  const handleAddSlide = () => {
    setSlides([...slides, { title: '', subtitle: '', description: '', gradient: '' }]);
  };

  const handleRemoveSlide = (index: number) => {
    setSlides(slides.filter((_, i) => i !== index));
  };

  const handleSlideChange = (index: number, field: keyof SlideForm, value: string) => {
    setSlides(
      slides.map((s, i) => (i === index ? { ...s, [field]: value } : s))
    );
  };

  const handleSaveColors = async () => {
    const success = await saveColors({
      primaryColor: primaryColor || null,
    });
    if (success) {
      setColorSaved(true);
      setTimeout(() => setColorSaved(false), 2000);
    }
  };

  const handleSaveCarousel = async () => {
    const success = await saveCarousel({
      description: JSON.stringify(slides),
    });
    if (success) {
      setCarouselSaved(true);
      setTimeout(() => setCarouselSaved(false), 2000);
    }
  };

  return (
    <div className="space-y-10">
      {/* Logo Management */}
      <LogoSection />

      {/* Colors */}
      <section className="space-y-8 rounded-2xl border border-border bg-card p-6 shadow-sm">
        <div>
          <h2 className="text-lg font-semibold">Cores da Academia</h2>
          <p className="text-sm text-muted-foreground">Defina a cor primária usada na interface.</p>
        </div>
        {isLoading ? (
          <div className="flex items-center justify-center py-10">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label
                    htmlFor="primary-color"
                    className="text-sm font-medium"
                  >
                    Cor Primária
                  </label>
                  <div className="relative flex max-w-xs items-center">
                    <label
                      htmlFor="color-picker-input"
                      className="absolute left-3 top-1/2 z-10 -translate-y-1/2 cursor-pointer"
                    >
                      <div
                        className="h-5 w-5 rounded-full border border-gray-300"
                        style={{
                          backgroundColor: primaryColor || '#ffffff',
                        }}
                      />
                    </label>
                    <input
                      id="color-picker-input"
                      type="color"
                      value={primaryColor || '#000000'}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 cursor-pointer opacity-0"
                    />
                    <Input
                      id="primary-color"
                      value={primaryColor || ''}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      placeholder="#000000"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Sugestões</p>
                  <div className="flex flex-wrap gap-2 pt-1">
                    {[
                      '#dc2626',
                      '#16a34a',
                      '#2563eb',
                      '#ca8a04',
                      '#9333ea',
                      '#db2777',
                    ].map((color) => (
                      <button
                        key={color}
                        type="button"
                        aria-label={`Selecionar cor ${color}`}
                        className={`h-7 w-7 rounded-full border-2 transition-transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${
                          primaryColor === color
                            ? 'border-foreground'
                            : 'border-muted'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setPrimaryColor(color)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end border-t border-border pt-6">
              <Button onClick={handleSaveColors} disabled={isSavingColors} variant={colorSaved ? 'default' : 'default'}>
                {isSavingColors ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Salvando...
                  </>
                ) : colorSaved ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="mr-2 h-4 w-4 text-green-500">
                      <path fillRule="evenodd" d="M2.25 12a9.75 9.75 0 1119.5 0 9.75 9.75 0 01-19.5 0zm14.072-2.823a.75.75 0 10-1.144-.954l-4.513 5.414-2.46-2.46a.75.75 0 10-1.06 1.061l3.06 3.06a.75.75 0 001.112-.05l5.005-6.07z" clipRule="evenodd" />
                    </svg>
                    Salvo!
                  </>
                ) : (
                  'Salvar Cores'
                )}
              </Button>
            </div>
          </>
        )}
      </section>

      {/* Carousel */}
      <section className="space-y-8 rounded-2xl border border-border bg-card p-6 shadow-sm">
        <div>
          <h2 className="text-lg font-semibold">Carrossel de Mensagens</h2>
          <p className="text-sm text-muted-foreground">Configure as mensagens e gradientes exibidos na tela de login.</p>
        </div>
        {slides.map((slide, index) => (
          <div key={index} className="space-y-4 rounded-lg border p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <label className="text-xs font-medium">Título</label>
                <Input
                  value={slide.title}
                  onChange={(e) => handleSlideChange(index, 'title', e.target.value)}
                />
              </div>
              <div className="space-y-1">
                <label className="text-xs font-medium">Subtítulo</label>
                <Input
                  value={slide.subtitle}
                  onChange={(e) => handleSlideChange(index, 'subtitle', e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-1">
              <label className="text-xs font-medium">Descrição</label>
              <Textarea
                value={slide.description}
                onChange={(e) => handleSlideChange(index, 'description', e.target.value)}
                rows={2}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
              <div className="space-y-1">
                <label className="text-xs font-medium">Gradiente (CSS)</label>
                <Input
                  value={slide.gradient}
                  onChange={(e) => handleSlideChange(index, 'gradient', e.target.value)}
                  placeholder="from-red-900 to-black"
                />
              </div>
              <div className="flex justify-end pt-1 md:pt-0">
                <Button
                  variant="destructive"
                  size="icon"
                  onClick={() => handleRemoveSlide(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
        <div className="flex items-center justify-between border-t border-border pt-6">
          <Button variant="outline" size="sm" onClick={handleAddSlide}>
            <Plus className="mr-2 h-4 w-4" /> Adicionar Mensagem
          </Button>

          <Button onClick={handleSaveCarousel} disabled={isSavingCarousel} variant={carouselSaved ? 'default' : 'default'}>
            {isSavingCarousel ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Salvando...
              </>
            ) : carouselSaved ? (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="mr-2 h-4 w-4 text-green-500">
                  <path fillRule="evenodd" d="M2.25 12a9.75 9.75 0 1119.5 0 9.75 9.75 0 01-19.5 0zm14.072-2.823a.75.75 0 10-1.144-.954l-4.513 5.414-2.46-2.46a.75.75 0 10-1.06 1.061l3.06 3.06a.75.75 0 001.112-.05l5.005-6.07z" clipRule="evenodd" />
                </svg>
                Salvo!
              </>
            ) : (
              'Salvar Carrossel'
            )}
          </Button>
        </div>
      </section>
    </div>
  );
} 