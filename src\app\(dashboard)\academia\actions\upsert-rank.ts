'use server';

import { z } from 'zod';
import { upsertRankRequirements, type BeltLevelRequirementsInput } from '@/services/belts/requirements';
import { requireAuth } from '@/services/auth/actions/auth-actions';

const UpsertRankSchema = z.object({
  beltLevelId: z.string().uuid({ message: 'ID do nível de faixa inválido' }),
  sessions: z.number().min(0, { message: 'Sessões deve ser maior ou igual a zero' }).optional(),
  hours: z.number().min(0, { message: 'Horas deve ser maior ou igual a zero' }).optional(),
  days_in_rank: z.number().min(0, { message: 'Dias na faixa deve ser maior ou igual a zero' }).optional(),
  days_attended: z.number().min(0, { message: 'Dias frequentados deve ser maior ou igual a zero' }).optional(),
  skill_requirements: z.boolean().default(false),
  minimum_age: z.number().min(0, { message: '<PERSON><PERSON> mínima deve ser maior ou igual a zero' }).optional(),
  promotion_fee: z.number().min(0, { message: 'Taxa deve ser maior ou igual a zero' }).optional(),
});

interface UpsertRankInput {
  beltLevelId: string;
  sessions?: number;
  hours?: number;
  days_in_rank?: number;
  days_attended?: number;
  skill_requirements?: boolean;
  minimum_age?: number;
  promotion_fee?: number;
}

interface ActionResult {
  success: boolean;
  errors?: any;
}

/**
 * Server Action: cria ou atualiza os requisitos de um rank para o tenant corrente
 * 
 * Requisitos:
 *  • Usuário autenticado com role `admin`
 *  • Tenant identificado no app_metadata do usuário
 */
export async function upsertRankAction(data: unknown): Promise<ActionResult> {
  const { user } = await requireAuth();

  // Validação dos dados
  const parsed = UpsertRankSchema.safeParse(data);
  if (!parsed.success) {
    return {
      success: false,
      errors: parsed.error.format(),
    };
  }

  const { beltLevelId, ...requirements } = parsed.data as UpsertRankInput;

  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    return {
      success: false,
      errors: { _form: 'Tenant não identificado' },
    };
  }

  if (role !== 'admin') {
    return {
      success: false,
      errors: { _form: 'Acesso negado' },
    };
  }

  try {
    const result = await upsertRankRequirements(tenantId, beltLevelId, requirements);

    if (!result.success) {
      return {
        success: false,
        errors: { _form: 'Falha ao atualizar requisitos do rank' },
      };
    }

    return { success: true };
  } catch (error: any) {
    console.error('[upsertRankAction] erro:', error);
    return {
      success: false,
      errors: { _form: 'Falha ao atualizar requisitos do rank' },
    };
  }
} 