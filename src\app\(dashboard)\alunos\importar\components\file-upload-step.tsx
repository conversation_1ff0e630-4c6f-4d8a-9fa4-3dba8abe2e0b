'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import Papa from 'papaparse'

interface FileUploadStepProps {
  onFileUploaded: (data: string[][], headers: string[]) => void
}

export function FileUploadStep({ onFileUploaded }: FileUploadStepProps) {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      Papa.parse(file, {
        complete: (result) => {
          const data = result.data as string[][]
          if (data.length > 0) {
            const headers = data[0]
            const rows = data.slice(1)
            const nonEmptyRows = rows.filter((row) =>
              row.some((cell) => cell.trim() !== ''),
            )
            onFileUploaded(nonEmptyRows, headers)
          }
        },
        header: false,
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Importar Membros</CardTitle>
      </CardHeader>
      <CardContent className="grid md:grid-cols-3 gap-8">
        <div className="md:col-span-1">
          <Card className="bg-teal-50 dark:bg-teal-900/30">
            <CardHeader>
              <CardTitle className="text-base text-teal-800 dark:text-teal-200">
                SELECIONE O ARQUIVO PARA IMPORTAR
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-4">
              <Input type="file" accept=".csv" onChange={handleFileChange} />
              <Select defaultValue="other">
                <SelectTrigger>
                  <SelectValue placeholder="Provedor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="other">Outro</SelectItem>
                </SelectContent>
              </Select>
              <Button
                className="bg-teal-600 hover:bg-teal-700 text-white dark:text-white"
                onClick={() =>
                  (
                    document.querySelector('input[type="file"]') as HTMLInputElement
                  )?.click()
                }
              >
                ENVIAR CSV
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="md:col-span-2 prose prose-sm max-w-none text-muted-foreground dark:prose-invert">
          <p>
            Importe seus dados de membros existentes através de um arquivo CSV.
            Qualquer planilha ou aplicativo de banco de dados deve ser capaz de
            exportar seus dados no formato CSV.
          </p>
          <h3 className="text-foreground font-semibold mt-6">
            COMO PREPARAR UM ARQUIVO CSV PARA IMPORTAÇÃO:
          </h3>
          <ol className="list-decimal list-inside space-y-2">
            <li>
              Salve ou exporte seus dados de membros como um arquivo CSV
              (valores separados por vírgula).
            </li>
            <li>
              Os dados podem precisar de um pouco de organização. Você pode
              abrir um arquivo CSV em qualquer aplicativo de planilha e editar
              as informações.
            </li>
            <li>
              Faça o upload do arquivo CSV ao lado. Você verá uma prévia dos dados
              importados.
            </li>
          </ol>
        </div>
      </CardContent>
    </Card>
  )
} 