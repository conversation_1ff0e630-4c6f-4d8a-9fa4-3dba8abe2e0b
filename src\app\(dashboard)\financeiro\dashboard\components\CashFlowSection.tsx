"use client";

/**
 * Componente da Seção de Fluxo de Caixa - Fase 5
 * Organiza todos os gráficos e análises de fluxo de caixa
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrendingUp, TrendingDown, DollarSign, AlertCircle, Calendar, Target } from 'lucide-react';
import { cn } from '@/lib/utils';

import {
  MonthlyCashFlowChart,
  CashFlowProjectionChart,
  CumulativeBalanceChart,
  SeasonalityChart
} from './charts';

import { getCashFlowAnalysisData } from '../actions/metrics/cashflow-actions';
import {
  FinancialKPIs,
  DashboardData,
  CashFlowAnalysisData,
  DateRange
} from '../types/dashboard-types';
import { formatCurrency, createMetricWithGrowth } from '../utils/dashboard-utils';
import { EnhancedKPICard } from './EnhancedKPICard';
import { LoadingStates } from './LoadingStates';

// ============================================================================
// TIPOS
// ============================================================================

interface CashFlowSectionProps {
  kpis: FinancialKPIs;
  data: DashboardData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const CashFlowSection: React.FC<CashFlowSectionProps> = ({
  kpis,
  data,
  loading = false,
  error = null,
  className
}) => {
  const [cashFlowData, setCashFlowData] = useState<CashFlowAnalysisData | null>(null);
  const [analysisLoading, setAnalysisLoading] = useState(true);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // Extrair dateRange dos dados (assumindo que está disponível)
  const dateRange: DateRange = {
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 5, 1),
    endDate: new Date(),
    period: 'month',
    label: 'Últimos 6 meses'
  };

  // Carregar dados específicos de fluxo de caixa
  useEffect(() => {
    const loadCashFlowData = async () => {
      try {
        setAnalysisLoading(true);
        setAnalysisError(null);

        const result = await getCashFlowAnalysisData(dateRange);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar análise de fluxo de caixa');
        }

        setCashFlowData(result.data || null);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setAnalysisError(errorMessage);
        console.error('Erro ao carregar análise de fluxo de caixa:', err);
      } finally {
        setAnalysisLoading(false);
      }
    };

    loadCashFlowData();
  }, []);

  const isLoading = loading || analysisLoading;
  const hasError = error || analysisError;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Erro */}
      {hasError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {hasError}
          </AlertDescription>
        </Alert>
      )}

      {/* KPIs de Fluxo de Caixa */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <EnhancedKPICard
          title="Saldo Atual"
          metric={createMetricWithGrowth(
            data.cashFlowMetrics.currentBalance,
            0, // TODO: Implementar valor anterior
            false,
            true
          )}
          icon={<DollarSign />}
          description="Saldo atual do fluxo de caixa"
        />

        <EnhancedKPICard
          title="Entradas Totais"
          metric={createMetricWithGrowth(
            data.cashFlowMetrics.totalInflows,
            0, // TODO: Implementar valor anterior
            false,
            true
          )}
          icon={<TrendingUp />}
          description="Total de entradas no período"
        />

        <EnhancedKPICard
          title="Saídas Totais"
          metric={createMetricWithGrowth(
            data.cashFlowMetrics.totalOutflows,
            0, // TODO: Implementar valor anterior
            false,
            true
          )}
          icon={<TrendingDown />}
          description="Total de saídas no período"
        />

        <EnhancedKPICard
          title="Fluxo Líquido"
          metric={createMetricWithGrowth(
            data.cashFlowMetrics.netCashFlow,
            0, // TODO: Implementar valor anterior
            false,
            true
          )}
          icon={<Target />}
          description="Diferença entre entradas e saídas"
        />
      </div>

      {/* Gráficos de Fluxo de Caixa */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fluxo de Caixa Mensal */}
        <MonthlyCashFlowChart
          dateRange={dateRange}
          className="lg:col-span-2"
        />

        {/* Saldo Acumulado */}
        <CumulativeBalanceChart
          dateRange={dateRange}
        />

        {/* Projeções */}
        <CashFlowProjectionChart
          dateRange={dateRange}
          monthsToProject={6}
        />
      </div>

      {/* Análise de Sazonalidade */}
      <div className="grid grid-cols-1 gap-6">
        <SeasonalityChart />
      </div>

      {/* Resumo e Insights */}
      {cashFlowData && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Resumo do Fluxo de Caixa
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Estatísticas Gerais */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">
                  Estatísticas Gerais
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Meses analisados:
                    </span>
                    <span className="text-sm font-medium">
                      {cashFlowData.monthlyCashFlow.length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Meses positivos:
                    </span>
                    <span className="text-sm font-medium text-emerald-600">
                      {cashFlowData.monthlyCashFlow.filter(m => m.netFlow > 0).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Meses negativos:
                    </span>
                    <span className="text-sm font-medium text-red-600">
                      {cashFlowData.monthlyCashFlow.filter(m => m.netFlow < 0).length}
                    </span>
                  </div>
                </div>
              </div>

              {/* Projeções */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">
                  Projeções
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Próximos meses:
                    </span>
                    <span className="text-sm font-medium">
                      {cashFlowData.projections.length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Saldo projetado:
                    </span>
                    <span className={cn(
                      "text-sm font-medium",
                      cashFlowData.projectedBalance >= 0 
                        ? "text-emerald-600" 
                        : "text-red-600"
                    )}>
                      {formatCurrency(cashFlowData.projectedBalance)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Tendência:
                    </span>
                    <span className={cn(
                      "text-sm font-medium",
                      cashFlowData.cashFlowTrend === 'up' ? "text-emerald-600" :
                      cashFlowData.cashFlowTrend === 'down' ? "text-red-600" :
                      "text-gray-600"
                    )}>
                      {cashFlowData.cashFlowTrend === 'up' ? 'Crescimento' :
                       cashFlowData.cashFlowTrend === 'down' ? 'Declínio' :
                       'Estável'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Sazonalidade */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">
                  Sazonalidade
                </h4>
                <div className="space-y-2">
                  {cashFlowData.seasonality.length > 0 && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Melhor mês:
                        </span>
                        <span className="text-sm font-medium text-emerald-600">
                          {cashFlowData.seasonality.reduce((best, current) => 
                            current.averageNetFlow > best.averageNetFlow ? current : best
                          ).monthName}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Pior mês:
                        </span>
                        <span className="text-sm font-medium text-red-600">
                          {cashFlowData.seasonality.reduce((worst, current) => 
                            current.averageNetFlow < worst.averageNetFlow ? current : worst
                          ).monthName}
                        </span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
