'use client';

import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { useEffect } from 'react';
import { FilterParams, Student } from '../types';
import { useAlunosFilter } from './use-alunos-filter';
import { useStudentsCache } from './use-students-cache';

// Definição local da interface
interface FetchStudentsResult {
  students: Student[];
  totalCount: number;
}

// Interface local para parâmetros de filtro
interface LocalFilterParams {
  skip?: number;
  take?: number;
  search?: string;
  status?: string[];
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
}

/**
 * Hook para buscar dados dos alunos com React Query
 * Implementa cache persistente que carrega primeiro do cache e depois faz refetch em background
 */
export function useStudentsQuery() {
  const { filters } = useAlunosFilter();
  const { refreshStudentsData, hasFreshData } = useStudentsCache();
  
  // Converter os filtros do estado para o formato esperado pela API
  const queryParams: LocalFilterParams = {
    skip: ((filters.page || 1) - 1) * (filters.limit || 10),
    take: filters.limit || 10,
    search: filters.search || '',
    status: filters.status || ['active'], // Enviar array completo de status
    belt: filters.belt || [],
    financialStatus: filters.financialStatus || [],
    branch: filters.branch || [],
    enrollmentStatus: filters.enrollmentStatus || []
  };
  
  // Criar uma chave de consulta baseada nos filtros atuais
  const queryKey = [
    'students',
    queryParams.skip,
    queryParams.take,
    queryParams.search,
    JSON.stringify(queryParams.status), // Usar JSON.stringify para arrays
    queryParams.belt,
    queryParams.financialStatus,
    queryParams.branch,
    queryParams.enrollmentStatus
  ];
  
  // Função que fará a requisição dos dados
  const fetchStudents = async (): Promise<FetchStudentsResult> => {
    try {
      // Fazer a requisição para a API
      const response = await fetch('/api/students/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryParams),
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar alunos: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Erro ao buscar alunos:', error);
      throw error;
    }
  };
  
  // Usar React Query para buscar e cachear os dados
  const query = useQuery({
    queryKey,
    queryFn: fetchStudents,
    staleTime: 5 * 60 * 1000, // 5 minutos - dados considerados "frescos"
    gcTime: 30 * 60 * 1000, // 30 minutos - mantém em cache por mais tempo
    refetchOnWindowFocus: false, // Não refetch quando voltar à janela
    refetchOnMount: false, // Não refetch automático no mount
    refetchOnReconnect: true, // Apenas refetch quando reconectar
    placeholderData: keepPreviousData, // Manter dados anteriores durante transições
    // Refetch em background após usar dados do cache
    refetchInterval: false, // Desabilitar refetch automático por intervalo
    // Configuração para refetch em background quando dados ficam stale
    refetchIntervalInBackground: false,
  });

  // Refetch inteligente em background quando dados ficam stale
  useEffect(() => {
    // Se temos dados em cache mas eles estão stale, fazer refetch em background
    if (query.data && query.isStale && !query.isFetching) {
      const timeoutId = setTimeout(() => {
        if (!hasFreshData(queryKey)) {
          refreshStudentsData();
        }
      }, 1000); // Aguardar 1 segundo para não interferir com a renderização

      return () => clearTimeout(timeoutId);
    }
  }, [query.data, query.isStale, query.isFetching, queryKey, refreshStudentsData, hasFreshData]);

  // Retornar explicitamente todos os estados e métodos necessários
  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isFetching: query.isFetching,
    isStale: query.isStale, // Adicionar para mostrar quando dados estão desatualizados
    refetch: query.refetch
  };
} 