# Pausa de Matrícula em Turma

> Objetivo: permitir que matrículas sejam temporariamente suspensas, impedindo que o aluno seja contado nas aulas ocorridas durante o período de pausa e restabelecendo a contagem após a reativação.

---

## 1. Modelagem de Dados

1. **<PERSON><PERSON>r tabela `class_group_enrollment_pauses`**
   - `id` UUID **PK**
   - `tenant_id` UUID **FK** → `tenants(id)`
   - `enrollment_id` UUID **FK** → `class_group_enrollments(id)` *ON DELETE CASCADE*
   - `start_date` timestamptz **NOT NULL**
   - `end_date` timestamptz **NULL** (pausa aberta quando `NULL`)
   - `created_at` timestamptz **DEFAULT** `now()`
   - **Índice** composto (`enrollment_id`, `start_date`, `end_date`)
   - Replicar políticas RLS da tabela de matrículas.

2. **<PERSON>gração SQL** (Supabase CLI)
   ```sql
   create table public.class_group_enrollment_pauses (
     id uuid primary key default gen_random_uuid(),
     tenant_id uuid not null references public.tenants(id) on delete restrict,
     enrollment_id uuid not null references public.class_group_enrollments(id) on delete cascade,
     start_date timestamptz not null,
     end_date timestamptz null,
     created_at timestamptz not null default now()
   );

   create index on public.class_group_enrollment_pauses (enrollment_id, start_date, end_date);
   ```

---

## 2. Ações de Domínio (Server Actions / Services)

| Ação | Descrição |
|------|-----------|
| **`pauseEnrollment(enrollmentId, startDate?)`** | • Insere linha na tabela de pausas<br>• Atualiza `status = 'suspended'` em `class_group_enrollments`<br>• Remove **presenças futuras** (`attendance`) do aluno para aulas da turma a partir de `startDate` |
| **`resumeEnrollment(enrollmentId, resumeDate?)`** | • Atualiza linha de pausa aberta (`end_date = resumeDate`)<br>• Atualiza `status = 'active'`<br>• Recria registros de presença somente para **aulas futuras** a partir de `resumeDate` |

---

## 3. Ajustes em Queries & Relatórios

1. **Ignorar pausados** em contagens sem necessidade de alterar `attendance`  – a deleção dos registros futuros já garante "0".
2. Relatórios baseados apenas em matrícula:<br>
   ```sql
   where not exists (
     select 1 from class_group_enrollment_pauses p
     where p.enrollment_id = e.id
       and p.start_date <= c.start_time
       and (p.end_date is null or p.end_date >= c.start_time)
   )
   ```

---

## 4. Interface do Usuário

1. **Tela "Alunos da Turma"**
   - Botões "Pausar / Reativar" já existentes → apontar para novas actions.
   - Badge "Pausado" quando houver pausa aberta.
2. **Feedback**: após pausar, remover aluno instantaneamente das listas de aulas futuras (optimistic update).

---

## 5. Testes

- **Unitários**
  1. Pausa cria linha e remove presenças futuras.
  2. Reativação fecha intervalo e recria presenças futuras.

---

## 6. Deployment

1. Executar migração **staging** → validar estatísticas e presenças.
2. Ajustar variáveis de ambiente/roles se necessário para nova tabela.
3. Deploy em **produção** fora do horário de pico.

---

## 7. Próximos Passos (opcionais)

- Dashboard de histórico de pausas por aluno.
- Automatizar e-mails de aviso quando matrícula for pausada / reativada.
- Endpoint de API pública para integrações externas consultarem status de matrícula. 