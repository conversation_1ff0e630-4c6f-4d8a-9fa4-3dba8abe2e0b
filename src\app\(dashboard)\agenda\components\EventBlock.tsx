'use client'

import React from 'react'
import { format } from 'date-fns'
import { CalendarEvent, useCalendar } from '../contexts/CalendarContext'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'

interface StackInfo {
  stackIndex: number
  totalInStack: number
  isStacked: boolean
}

interface EventBlockProps {
  event: CalendarEvent
  style?: React.CSSProperties
  stackInfo?: StackInfo
}

export function EventBlock({ event, style, stackInfo }: EventBlockProps) {
  const { setSelectedEvent, setIsModalOpen } = useCalendar()

  const handleClick = () => {
    setSelectedEvent(event)
    setIsModalOpen(true)
  }

  const getEventColors = (color: CalendarEvent['color'], status: CalendarEvent['status']) => {
    const baseColors = {
      green: {
        bg: 'bg-green-100 dark:bg-green-900/30',
        border: 'border-green-300 dark:border-green-700',
        text: 'text-green-800 dark:text-green-200',
        accent: 'bg-green-500'
      },
      yellow: {
        bg: 'bg-yellow-100 dark:bg-yellow-900/30',
        border: 'border-yellow-300 dark:border-yellow-700',
        text: 'text-yellow-800 dark:text-yellow-200',
        accent: 'bg-yellow-500'
      },
      red: {
        bg: 'bg-red-100 dark:bg-red-900/30',
        border: 'border-red-300 dark:border-red-700',
        text: 'text-red-800 dark:text-red-200',
        accent: 'bg-red-500'
      },
      blue: {
        bg: 'bg-blue-100 dark:bg-blue-900/30',
        border: 'border-blue-300 dark:border-blue-700',
        text: 'text-blue-800 dark:text-blue-200',
        accent: 'bg-blue-500'
      }
    }

    let colors = baseColors[color]

    // Ajustar cores baseado no status
    if (status === 'cancelled') {
      colors = {
        bg: 'bg-muted dark:bg-muted/50',
        border: 'border-muted-foreground/30',
        text: 'text-muted-foreground line-through',
        accent: 'bg-muted-foreground/50'
      }
    } else if (status === 'completed') {
      colors = {
        ...colors,
        bg: colors.bg + ' opacity-75'
      }
    }

    return colors
  }

  const colors = getEventColors(event.color, event.status)

  const getStatusIcon = () => {
    switch (event.status) {
      case 'ongoing':
        return '✓'
      case 'pending':
        return '⏳'
      case 'completed':
        return '✅'
      case 'cancelled':
        return '❌'
      default:
        return ''
    }
  }

  // Determinar se é um evento pequeno (menos de 45 minutos)
  const eventDuration = (event.endTime.getTime() - event.startTime.getTime()) / (1000 * 60) // em minutos
  const isCompactEvent = eventDuration < 45
  
  // Determinar se está empilhado
  const isStacked = stackInfo?.isStacked || false
  const isTopOfStack = stackInfo ? stackInfo.stackIndex === stackInfo.totalInStack - 1 : false

  return (
    <div
      style={style}
      onClick={handleClick}
      className={cn(
        "rounded-lg border cursor-pointer group calendar-event-block",
        "p-2 overflow-hidden relative",
        colors.bg,
        colors.border,
        "backdrop-blur-sm transition-all duration-200",
        "hover:shadow-md hover:scale-[1.02]",
        isStacked && "shadow-lg",
        isStacked && !isTopOfStack && "opacity-90"
      )}
    >
      {/* Barra lateral colorida para identificação rápida */}
      <div className={cn("absolute left-0 top-0 bottom-0 w-1 rounded-l-lg", colors.accent)} />

      {/* Indicador de empilhamento no canto superior direito */}
      {isStacked && isTopOfStack && stackInfo && stackInfo.totalInStack > 1 && (
        <div className="absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold shadow-sm z-10">
          {stackInfo.totalInStack}
        </div>
      )}

      <div className="pl-2">
        {/* Header com código e status */}
        {/* <div className="flex items-center justify-between mb-1">
          <span className={cn("text-xs font-bold tracking-wide", colors.text)}>
            {event.code}
          </span>
          <span className="text-xs opacity-75">
            {getStatusIcon()}
          </span>
        </div> */}

        {/* Título do evento */}
        <div className={cn(
          "font-semibold mb-1",
          isCompactEvent || isStacked ? "text-xs line-clamp-1" : "text-sm line-clamp-2",
          colors.text
        )}>
          {event.title}
        </div>

        {/* Horário - sempre visível */}
        <div className={cn("text-xs font-medium mb-1", colors.text)}>
          {format(event.startTime, 'HH:mm')} - {format(event.endTime, 'HH:mm')}
        </div>

        {/* Informações adicionais - condicionais baseadas no espaço */}
        {!isCompactEvent && !isStacked && (
          <>
            {/* Cliente com avatar (se houver) */}
            {event.customer && (
              <div className="flex items-center gap-1.5 mb-1">
                <Avatar className="h-4 w-4">
                  <AvatarImage src={event.customer.avatar} />
                  <AvatarFallback className="text-xs bg-white/80">
                    {event.customer.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <span className={cn("text-xs truncate flex-1", colors.text)}>
                  {event.customer.name}
                </span>
              </div>
            )}

            {/* Instrutor */}
            <div className={cn("text-xs truncate", colors.text)}>
              <span className="opacity-75">Prof. </span>
              <span className="font-medium">{event.stylist}</span>
            </div>
          </>
        )}

        {/* Para eventos compactos ou empilhados, mostrar apenas o instrutor */}
        {(isCompactEvent || isStacked) && (
          <div className={cn("text-xs truncate opacity-75", colors.text)}>
            {event.stylist}
          </div>
        )}

        {/* Indicador de local (se houver espaço) */}
        {!isCompactEvent && !isStacked && event.location && (
          <div className={cn("text-xs opacity-60 truncate mt-1", colors.text)}>
            📍 {event.location}
          </div>
        )}
      </div>

      {/* Overlay para hover effect */}
      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg" />
    </div>
  )
} 