"use server";

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';
import { requireAuth } from '@/services/auth/server';
import { getTenantSlug } from '@/services/tenant';

const emailSchema = z.object({
  email: z.string().email('Email inválido'),
});

export type EmailFormData = z.infer<typeof emailSchema>;

export async function getSenderEmail() {
  try {
    await requireAuth();
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return { success: false, errors: { _form: 'Tenant não encontrado' } };
    }

    const supabase = await createClient();

    // Buscar tenant id
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, owner_id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error('Erro tenant:', tenantError);
      return { success: false, errors: { _form: 'Erro ao buscar dados da academia' } };
    }

    // Buscar email da filial principal
    const { data: branchData, error: branchError } = await supabase
      .from('branches')
      .select('email')
      .eq('tenant_id', tenantData.id)
      .eq('is_main', true)
      .single();

    if (branchError) {
      console.error('Erro branch:', branchError);
    }

    // Se não houver email na filial, buscar email do dono
    let email = branchData?.email || '';
    if (!email) {
      const { data: ownerData, error: ownerError } = await supabase
        .from('users')
        .select('email')
        .eq('id', tenantData.owner_id)
        .single();
      if (!ownerError) {
        email = ownerData?.email || '';
      }
    }

    return { success: true, data: { email } };
  } catch (error) {
    console.error('Erro getSenderEmail:', error);
    return { success: false, errors: { _form: 'Erro interno do servidor' } };
  }
}

export async function setSenderEmail(data: unknown) {
  try {
    await requireAuth();
    const result = emailSchema.safeParse(data);
    if (!result.success) {
      return { success: false, errors: result.error.format() };
    }
    const { email } = result.data;

    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return { success: false, errors: { _form: 'Tenant não encontrado' } };
    }

    const supabase = await createClient();

    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();
    if (tenantError || !tenantData) {
      console.error('Erro tenant:', tenantError);
      return { success: false, errors: { _form: 'Erro ao buscar tenant' } };
    }

    // Atualizar email na filial principal
    const { error: updateError } = await supabase
      .from('branches')
      .update({ email, updated_at: new Date().toISOString() })
      .eq('tenant_id', tenantData.id)
      .eq('is_main', true);

    if (updateError) {
      console.error('Erro update branch:', updateError);
      return { success: false, errors: { _form: 'Erro ao atualizar email' } };
    }
    return { success: true, message: 'Endereço de envio definido com sucesso' };
  } catch (error) {
    console.error('Erro setSenderEmail:', error);
    return { success: false, errors: { _form: 'Erro interno do servidor' } };
  }
} 