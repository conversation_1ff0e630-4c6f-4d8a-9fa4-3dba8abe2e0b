'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { updateModalitySettings } from '@/services/modalities/settings';
import { updateModalityName } from '@/services/modalities';
import { requireAuth } from '@/services/auth/actions/auth-actions';
import { upsertRankRequirements, type BeltLevelRequirementsInput } from '@/services/belts/requirements';
import { upsertBeltLevels } from '@/services/belts/levels';
import { deleteBeltLevel } from '@/services/belts/levels';
import { listModalities } from '@/services/modalities';

// Schema to validate each rank coming from the form
const LevelSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().optional(),
  degree: z.number().optional(),
  belt_color: z.string().optional(),
  stripe_color: z.string().optional(),
  show_center_line: z.boolean().optional(),
  center_line_color: z.string().optional(),
  sessions: z.number().min(0).optional(),
  minimum_age: z.number().min(0).optional(),
});

const UpdateModalitySettingsSchema = z.object({
  modalityId: z.string().uuid({ message: 'ID da modalidade inválido' }),
  name: z.string().min(1, { message: 'Nome da modalidade é obrigatório' }),
  type: z.string().optional(),
  level_rank_style: z.string().optional(),
  secondary_color: z.string().optional(),
  auto_assign_initial_rank: z.boolean().default(false),
  promotion_setting: z.enum(['manual', 'automatic']).default('manual'),
  promotion_fee: z
    .number()
    .min(0, { message: 'Taxa deve ser maior ou igual a zero' })
    .optional(),
  // New rank-related fields
  require_sessions: z.boolean().default(false),
  require_minimum_age: z.boolean().default(false),
  levels: z.array(LevelSchema).default([]),
});

interface LevelInput {
  id?: string;
  name?: string;
  degree?: number;
  belt_color?: string;
  stripe_color?: string;
  show_center_line?: boolean;
  center_line_color?: string;
  sessions?: number;
  minimum_age?: number;
}

interface UpdateModalitySettingsInput {
  modalityId: string;
  name: string;
  type?: string;
  level_rank_style?: string;
  secondary_color?: string;
  auto_assign_initial_rank?: boolean;
  promotion_setting?: 'manual' | 'automatic';
  promotion_fee?: number;
  require_sessions: boolean;
  require_minimum_age: boolean;
  levels: LevelInput[];
}

interface ActionResult {
  success: boolean;
  errors?: any;
}

/**
 * Server Action: atualiza as configurações de uma modalidade para o tenant corrente
 * Além das configurações gerais, também atualiza (ou cria) os requisitos de graduação
 * para todos os ranks enviados.
 */
export async function updateModalitySettingsAction(
  data: unknown,
): Promise<ActionResult> {
  console.log('🚀 Action updateModalitySettingsAction chamada com dados:', data);
  const { user } = await requireAuth();

  // Validação dos dados
  const parsed = UpdateModalitySettingsSchema.safeParse(data);
  if (!parsed.success) {
    console.error('❌ Erro de validação na action:', parsed.error.format());
    return {
      success: false,
      errors: parsed.error.format(),
    };
  }

  console.log('✅ Dados validados com sucesso na action');

  const {
    modalityId,
    name,
    require_sessions,
    require_minimum_age,
    levels,
    ...settings
  } = parsed.data as UpdateModalitySettingsInput;

  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    return {
      success: false,
      errors: { _form: 'Tenant não identificado' },
    };
  }

  if (role !== 'admin') {
    return {
      success: false,
      errors: { _form: 'Acesso negado' },
    };
  }

  try {
    // 1. Atualiza o nome da modalidade na tabela modalities
    console.log('🔄 Atualizando nome da modalidade:', name);
    const nameUpdateResult = await updateModalityName(tenantId, modalityId, name);

    if (!nameUpdateResult.success) {
      console.error('❌ Erro ao atualizar nome da modalidade:', nameUpdateResult.error);
      return {
        success: false,
        errors: { _form: 'Falha ao atualizar nome da modalidade' },
      };
    }

    console.log('✅ Nome da modalidade atualizado com sucesso');

    // 2. Atualiza configurações da modalidade (incluindo os novos campos)
    const settingsWithRequirements = {
      ...settings,
      require_sessions,
      require_minimum_age,
    };
    const result = await updateModalitySettings(tenantId, modalityId, settingsWithRequirements);

    if (!result.success) {
      return {
        success: false,
        errors: { _form: 'Falha ao atualizar configurações da modalidade' },
      };
    }

    // 3. Preparar e processar belt levels
    console.log('🚀 Preparando dados dos belt levels...');
    const levelsPayload = levels.map((lvl, idx) => ({
      id: lvl.id,
      belt_color: lvl.belt_color as any,
      degree: lvl.degree ?? 0,
      label: lvl.name ?? '',
      stripe_color: (lvl as any).stripe_color ?? null,
      show_center_line: (lvl as any).show_center_line ?? null,
      center_line_color: (lvl as any).center_line_color ?? null,
      sort_order: idx,
    }));

    console.log('📝 Payload dos níveis preparado:', levelsPayload.length, 'níveis');

    // 3.1. A RPC agora faz UPSERT corretamente, não precisa de limpeza prévia
    console.log('� RPC modificada para fazer UPSERT real com ON CONFLICT');

    // 3.2. Executar upsert dos belt levels
    console.log('🚀 Executando upsert dos belt levels...');
    const upsertLevelsResult = await upsertBeltLevels(tenantId, modalityId, levelsPayload);
    console.log('📊 Resultado do upsert dos níveis:', upsertLevelsResult);

    if (!upsertLevelsResult.success) {
      console.error('❌ Falha no upsert dos belt levels:', upsertLevelsResult.error);
      return {
        success: false,
        errors: { _form: 'Falha ao salvar graduações' },
      };
    }

    console.log('✅ Upsert dos belt levels concluído com sucesso!');

    // 4. Atualiza (ou cria) requisitos de todos os ranks presentes no formulário
    try {
      console.log('🔧 Iniciando processo de atualização de requisitos...');
      const adminClient = await import('@/services/supabase/server').then(m => m.createAdminClient());

      // Obter todos os belt_levels atuais após o upsert (ordenados)
      console.log('🔍 Buscando belt_levels após upsert...');
      const { data: dbLevels, error: dbLevelsError } = await adminClient
        .from('belt_levels')
        .select('id,label')
        .eq('tenant_id', tenantId)
        .eq('modality_id', modalityId);

      if (dbLevelsError) {
        console.error('❌ Erro ao buscar belt_levels para requisitos:', dbLevelsError);
      } else {
        console.log('✅ Belt levels encontrados após upsert:', dbLevels?.length || 0);
        console.log('📋 Labels encontrados:', dbLevels?.map(l => l.label));

        // Mapa de label -> id para corresponder ao formulário
        const labelToId = new Map(dbLevels!.map((l: any) => [l.label, l.id]));
        console.log('🗺️ Mapa label->id criado:', Object.fromEntries(labelToId));

        const upsertPromisesAll = levels.map((lvl) => {
          // Validar ID do level antes de usar
          let levelId = lvl.id && lvl.id.trim() !== '' ? lvl.id : undefined;
          if (!levelId) {
            levelId = labelToId.get(lvl.name ?? '');
          }
          
          console.log(`🔗 Processando requisitos para "${lvl.name}": levelId=${levelId}`);

          if (!levelId || levelId.trim() === '') {
            console.warn(`⚠️ Nenhum levelId válido encontrado para "${lvl.name}"`);
            return Promise.resolve({ success: true });
          }

          // Validação adicional de UUID
          const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
          if (!uuidRegex.test(levelId)) {
            console.warn(`⚠️ ID inválido para level "${lvl.name}": ${levelId}`);
            return Promise.resolve({ success: true });
          }

          const req: BeltLevelRequirementsInput = {
            sessions: require_sessions ? lvl.sessions : undefined,
            minimum_age: require_minimum_age ? lvl.minimum_age : undefined,
          };
          return upsertRankRequirements(tenantId, levelId, req);
        });

        const reqResults = await Promise.all(upsertPromisesAll);
        const failedReq = reqResults.find((r) => !r.success);
        if (failedReq) {
          console.error('❌ Falha ao atualizar requisitos:', failedReq);
          return {
            success: false,
            errors: { _form: 'Falha ao atualizar requisitos de alguns ranks' },
          };
        }

        console.log('✅ Requisitos atualizados com sucesso!');
      }
    } catch (reqErr) {
      console.error('Erro ao atualizar requisitos:', reqErr);
      return {
        success: false,
        errors: { _form: 'Erro ao criar/atualizar requisitos' },
      };
    }

    // 5. Remover níveis que não estão mais no payload (limpeza pós-upsert)
    try {
      console.log('🧹 Verificando se há níveis para remover...');
      const adminClient = await import('@/services/supabase/server').then(m => m.createAdminClient());

      // Buscar todos os levels atuais após upsert
      const { data: currentLevels, error: listError } = await adminClient
        .from('belt_levels')
        .select('id, label, belt_color, degree')
        .eq('tenant_id', tenantId)
        .eq('modality_id', modalityId);

      if (!listError && currentLevels) {
        const payloadLabels = levelsPayload.map(l => l.label);
        const levelsToDelete = currentLevels.filter((level: any) => !payloadLabels.includes(level.label));

        console.log('🗑️ Registros a serem removidos:', levelsToDelete.map((l: any) => l.label));

        if (levelsToDelete.length > 0) {
          const deletePromises = levelsToDelete.map((level: any) => deleteBeltLevel(tenantId, level.id));
          const deleteResults = await Promise.all(deletePromises);
          const failedDel = deleteResults.find((r) => !r.success);
          if (failedDel) {
            console.warn('⚠️ Falha ao deletar alguns belt_levels:', failedDel.error);
          } else {
            console.log(`✅ ${levelsToDelete.length} registros antigos removidos com sucesso`);
          }
        } else {
          console.log('✅ Nenhum registro para remover');
        }
      }
    } catch (delErr) {
      console.warn('⚠️ Erro no processo de limpeza pós-upsert:', delErr);
    }

    console.log('✅ Processo de belt levels concluído com sucesso!');

    // Buscar slug da modalidade para revalidação correta
    try {
      const modalities = await listModalities(tenantId);
      const modality = modalities.find(m => m.id === modalityId);
      const modalitySlug = modality?.slug;

      // Revalidar caches relacionados
      revalidatePath('/academia/configuracoes/modalidades');
      if (modalitySlug) {
        revalidatePath(`/academia/configuracoes/modalidades/${modalitySlug}/editar`);
      }
      revalidatePath('/dashboard');
      console.log('🔄 Cache revalidado para páginas de modalidades');
    } catch (revalidationError) {
      console.warn('⚠️ Erro na revalidação de cache:', revalidationError);
      // Não falhar a operação por causa da revalidação
    }

    return { success: true };
  } catch (error: any) {
    console.error('[updateModalitySettingsAction] erro:', error);
    return {
      success: false,
      errors: { _form: 'Falha ao atualizar configurações da modalidade' },
    };
  }
}