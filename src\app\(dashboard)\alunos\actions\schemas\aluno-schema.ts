import { z } from "zod";
import { userFilterStatusSchema } from "@/services/user/schemas/user-schema";

export const alunoFormSchema = z.object({
  name: z.string().min(2, { message: "O nome deve ter pelo menos 2 caracteres" }),
  email: z.string().email({ message: "Email inválido" }),
  phone: z.string().min(8, { message: "Telefone inválido" }),
  birthDate: z.date().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional().refine(
    (val) => !val || /^[A-Z]{2}$/.test(val.toUpperCase()),
    { message: "O estado deve ter 2 letras (UF)" }
  ),
  documentId: z.string().optional(),
  belt: z.enum(["white", "blue", "purple", "brown", "black"], {
    message: "Selecione uma faixa válida"
  }),
  branch: z.string({
    message: "Selecione uma filial"
  }),

});

export type AlunoFormValues = z.infer<typeof alunoFormSchema>;

// Schema para filtros
export const alunoFilterSchema = z.object({
  search: z.string().optional(),
  belt: z.array(z.string()).optional(),
  status: userFilterStatusSchema,
  financialStatus: z.array(z.string()).optional(),
  branch: z.array(z.string()).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
});

export type AlunoFilterValues = z.infer<typeof alunoFilterSchema>; 