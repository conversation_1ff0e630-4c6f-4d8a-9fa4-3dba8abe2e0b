'use client';

import { useState, startTransition } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Edit2, Users, Trash2, AlertCircle } from 'lucide-react';
import { BeltDisplay, beltColorTranslation } from '@/components/belt';
import { type GraduationLevel } from '@/services/belts/levels';
import { type BeltLevelRequirements } from '@/services/belts/requirements';
import RankEditDialog from './RankEditDialog';
import { deleteRankAction } from '@/app/(dashboard)/academia/actions/delete-rank';
import { toast } from '@/hooks/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface GraduationLevelWithRequirements extends GraduationLevel {
  requirements?: BeltLevelRequirements;
  stripe_color?: string;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

interface Props {
  modality: {
    id: string;
    slug: string;
    name: string;
    enabled: boolean;
  };
  levelsWithRequirements: GraduationLevelWithRequirements[];
}

export default function RanksList({ modality, levelsWithRequirements }: Props) {
  const [editingRank, setEditingRank] = useState<GraduationLevelWithRequirements | null>(null);
  const [deletingRank, setDeletingRank] = useState<GraduationLevelWithRequirements | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleEditRank = (rank: GraduationLevelWithRequirements) => {
    setEditingRank(rank);
  };

  const handleCloseEdit = () => {
    setEditingRank(null);
  };

  const handleDeleteRank = (rank: GraduationLevelWithRequirements) => {
    setDeletingRank(rank);
  };

  const handleCloseDelete = () => {
    setDeletingRank(null);
  };

  const handleConfirmDelete = async () => {
    if (!deletingRank) return;
    
    setIsDeleting(true);
    
    startTransition(async () => {
      try {
        const result = await deleteRankAction({ 
          beltLevelId: deletingRank.id 
        });
        
        if (result.success) {
          toast({
            title: "Sucesso",
            description: "Graduação excluída com sucesso.",
          });
          // Recarrega a página para atualizar os dados
          window.location.reload();
        } else {
          toast({
            title: "Erro",
            description: result.errors?._form || "Falha ao excluir a graduação.",
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Erro",
          description: "Ocorreu um erro inesperado. Tente novamente.",
          variant: "destructive",
        });
      } finally {
        setIsDeleting(false);
        handleCloseDelete();
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Níveis / Graduações</h3>
        <Badge variant="secondary" className="text-xs">
          {levelsWithRequirements.length} graduações configuradas
        </Badge>
      </div>

      <div className="space-y-4">
        {levelsWithRequirements.map((level) => (
          <Card key={level.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <BeltDisplay 
                    belt={level.belt_color} 
                    stripes={level.degree} 
                    size="lg" 
                    showTranslation={false}
                  />
                  <div>
                    <CardTitle className="text-base">
                      {level.label || beltColorTranslation[level.belt_color] || level.belt_color}
                    </CardTitle>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                      <Users className="h-4 w-4" />
                      <span>{level.members_count} membros</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditRank(level)}
                    className="h-8 w-8 p-0"
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteRank(level)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    disabled={level.members_count > 0}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            {level.requirements && (
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {level.requirements.sessions && (
                    <div className="text-center">
                      <div className="font-medium text-foreground">{level.requirements.sessions}</div>
                      <div className="text-muted-foreground">Aulas</div>
                    </div>
                  )}
                  {level.requirements.hours && (
                    <div className="text-center">
                      <div className="font-medium text-foreground">{level.requirements.hours}</div>
                      <div className="text-muted-foreground">Horas</div>
                    </div>
                  )}
                  {level.requirements.days_in_rank && (
                    <div className="text-center">
                      <div className="font-medium text-foreground">{level.requirements.days_in_rank}</div>
                      <div className="text-muted-foreground">Dias na Faixa</div>
                    </div>
                  )}
                  {level.requirements.minimum_age && (
                    <div className="text-center">
                      <div className="font-medium text-foreground">{level.requirements.minimum_age}</div>
                      <div className="text-muted-foreground">Idade Mín.</div>
                    </div>
                  )}
                </div>
                
                {level.requirements.promotion_fee && (
                  <div className="mt-4 pt-4 border-t">
                    <div className="text-sm">
                      <span className="text-muted-foreground">Taxa de Promoção: </span>
                      <span className="font-medium">R$ {level.requirements.promotion_fee.toFixed(2)}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            )}

            {!level.requirements && (
              <CardContent className="pt-0">
                <div className="text-center py-4 text-muted-foreground">
                  <p className="text-sm">Nenhum requisito configurado</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditRank(level)}
                    className="mt-2"
                  >
                    Configurar Requisitos
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {levelsWithRequirements.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <p className="text-muted-foreground">
              Nenhum nível de graduação encontrado para esta modalidade
            </p>
          </CardContent>
        </Card>
      )}

      {editingRank && (
        <RankEditDialog
          rank={editingRank}
          modality={modality}
          open={!!editingRank}
          onClose={handleCloseEdit}
        />
      )}

      <AlertDialog open={!!deletingRank} onOpenChange={handleCloseDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Excluir Graduação
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta graduação? 
              {deletingRank && (
                <span className="font-medium block mt-2">
                  {deletingRank.label || beltColorTranslation[deletingRank.belt_color] || deletingRank.belt_color}
                </span>
              )}
              <p className="mt-2">Esta ação não poderá ser desfeita.</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleConfirmDelete();
              }}
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 