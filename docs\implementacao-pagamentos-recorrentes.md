# Implementação de Pagamentos Recorrentes Reais

## 📋 Problema Identificado

O sistema estava mostrando "Próximos Pagamentos" na interface do usuário, mas esses pagamentos eram gerados dinamicamente na rota `/api/user/[userId]/payments/upcoming` e não existiam como registros reais na tabela `payments` do banco de dados.

### Comportamento Anterior
- Quando um plano recorrente era atribuído a um usuário, apenas a `membership` era criada
- Os "próximos pagamentos" eram calculados dinamicamente baseado no `next_billing_date` da membership
- Não havia registros reais na tabela `payments` para os pagamentos futuros

## 🔧 Solução Implementada

### 1. Nova Função RPC: `create_recurring_payments`

Criada uma função no banco de dados que gera múltiplos pagamentos recorrentes reais:

**Arquivo:** `migrations/20241222_001_create_recurring_payments_function.sql`

**Funcionalidades:**
- Recebe `membership_id` e número de pagamentos a criar (padrão: 3)
- Valida se é um plano recorrente
- Cria registros reais na tabela `payments` com datas calculadas baseadas na frequência
- Suporta frequências: `weekly`, `monthly`, `yearly`
- Evita duplicação verificando se já existem pagamentos para a membership

**Exemplo de uso:**
```sql
SELECT create_recurring_payments(
  'membership-uuid'::uuid,
  3 -- criar 3 pagamentos futuros
);
```

### 2. Novo Método no Payment Service

**Arquivo:** `src/services/billing/payment-service.ts`

Adicionado método `createRecurringPayments()` que chama a função RPC:

```typescript
async createRecurringPayments(data: { 
  membershipId: string; 
  paymentsCount?: number 
}): Promise<PaymentRPCResult>
```

### 3. Atualização do Membership Actions

**Arquivo:** `src/app/(dashboard)/academia/actions/membership-actions.ts`

Modificado para detectar planos recorrentes e criar pagamentos automaticamente:

- Para planos `recurring`: chama `createRecurringPayments()`
- Para planos `one-time`: chama `createInitialPayment()` (comportamento anterior)
- Funciona tanto no fluxo direto quanto no RPC

### 4. Atualização da Rota Upcoming Payments

**Arquivo:** `src/app/api/user/[userId]/payments/upcoming/route.ts`

Modificada para priorizar pagamentos reais do banco:

- Busca primeiro os pagamentos pendentes reais da tabela `payments`
- Usa pagamentos projetados apenas como fallback se não houver suficientes registros reais
- Evita duplicação verificando se já existe pagamento real para uma data específica

## 🧪 Teste Realizado

### Cenário de Teste
1. **Plano:** "Plano Faixa Preta" (recorrente, R$ 20/mês)
2. **Estudante:** Ana Ferreira
3. **Membership criada:** 15/01/2025 com next_billing_date: 15/02/2025

### Resultado
✅ **3 pagamentos criados automaticamente:**
- Pagamento 1: 15/02/2025 - R$ 20,00 (sequência 1)
- Pagamento 2: 15/03/2025 - R$ 20,00 (sequência 2)  
- Pagamento 3: 15/04/2025 - R$ 20,00 (sequência 3)

### Verificação no Banco
```sql
SELECT id, amount, due_date, payment_type, description
FROM payments 
WHERE membership_id = 'ca20f4ad-9288-44d6-93f5-e09f1a599ee9'
ORDER BY due_date;
```

## 📈 Benefícios da Implementação

### 1. **Dados Consistentes**
- Pagamentos futuros agora existem como registros reais no banco
- Facilita relatórios e análises financeiras
- Permite rastreamento individual de cada pagamento

### 2. **Melhor Experiência do Usuário**
- Interface mostra dados reais, não projeções
- Possibilidade de adicionar funcionalidades como lembretes e notificações
- Histórico completo de pagamentos

### 3. **Flexibilidade Operacional**
- Administradores podem modificar pagamentos individuais se necessário
- Possibilidade de aplicar descontos ou ajustes em pagamentos específicos
- Melhor controle sobre inadimplência

### 4. **Escalabilidade**
- Sistema preparado para funcionalidades avançadas como:
  - Processamento automático de pagamentos
  - Integração com gateways de pagamento
  - Relatórios financeiros detalhados

## 🔄 Fluxo Atualizado

### Criação de Membership com Plano Recorrente

1. **Usuário atribui plano recorrente** → `membership-actions.ts`
2. **Sistema cria membership** → tabela `memberships`
3. **Sistema detecta plano recorrente** → verifica `pricing_config.type`
4. **Sistema chama função RPC** → `create_recurring_payments()`
5. **Função cria pagamentos reais** → tabela `payments`
6. **Interface mostra pagamentos reais** → rota `/upcoming`

### Visualização de Próximos Pagamentos

1. **Usuário acessa perfil** → componente `MensalidadesTab`
2. **Sistema busca pagamentos pendentes** → rota `/api/user/[userId]/payments/upcoming`
3. **Rota consulta tabela payments** → registros reais com `status='pending'`
4. **Interface exibe dados reais** → componente `UpcomingPayments`

## 🚀 Próximos Passos Sugeridos

1. **Processamento Automático:** Implementar job para processar pagamentos vencidos
2. **Notificações:** Sistema de lembretes antes do vencimento
3. **Gateway de Pagamento:** Integração com PIX/cartão para pagamento automático
4. **Relatórios:** Dashboard financeiro com base nos dados reais
5. **Gestão de Inadimplência:** Fluxo automatizado para pagamentos em atraso

## 📝 Arquivos Modificados

- ✅ `migrations/20241222_001_create_recurring_payments_function.sql`
- ✅ `src/services/billing/payment-service.ts`
- ✅ `src/app/(dashboard)/academia/actions/membership-actions.ts`
- ✅ `src/app/api/user/[userId]/payments/upcoming/route.ts`

## 🎯 Resultado Final

**Problema resolvido:** Agora quando um plano recorrente é atribuído a um usuário, os pagamentos futuros são criados como registros reais na tabela `payments`, garantindo consistência de dados e melhor experiência do usuário.
